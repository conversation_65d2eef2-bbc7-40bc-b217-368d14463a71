#!/usr/bin/env ruby
# frozen_string_literal: true

require 'json'
require 'colorize'
require 'fileutils'

##
# Configuration Environment Manager
#
# This script allows switching between different development configurations
# by managing JSON configuration files in the config/dev-envs directory.
# The active configuration is always config/development.json.
#
class ConfigurationManager
  # Configuration constants
  CURRENT_CONFIG_PATH = 'config/development.json'
  DEV_ENVS_DIR = 'config/dev-envs'
  CONFIG_PATTERN = File.join(DEV_ENVS_DIR, '*.json')

  # JSON field constants
  NAME_FIELD = '__comment__'
  FILE_FIELD = '__file__'
  FAVORITE_FIELD = '_fav'

  # Display constants
  NAME_COLUMN_WIDTH = 25
  INDEX_FORMAT = '[%2d]'
  QUIT_OPTION = 'q'
  CREATE_OPTION = 'c'

  def initialize
    ensure_dev_envs_directory
    @config_files = load_config_files
    @configurations = load_configurations
    @current_config = load_current_config
  end

  ##
  # Main execution method
  #
  def run
    display_header
    display_configurations
    display_footer

    selection = prompt_for_selection
    return exit_gracefully if quit_requested?(selection)
    return create_new_configuration if create_requested?(selection)

    switch_configuration(selection)
  end

  private

  ##
  # Ensure the dev-envs directory exists
  #
  def ensure_dev_envs_directory
    FileUtils.mkdir_p(DEV_ENVS_DIR) unless Dir.exist?(DEV_ENVS_DIR)
  end

  ##
  # Load and sort configuration files
  #
  def load_config_files
    Dir.glob(CONFIG_PATTERN).sort
  end

  ##
  # Parse JSON configurations from files
  #
  def load_configurations
    @config_files.map { |file| parse_json_file(file) }
  end

  ##
  # Load current active configuration
  #
  def load_current_config
    parse_json_file(CURRENT_CONFIG_PATH)
  end

  ##
  # Parse JSON file with error handling
  #
  def parse_json_file(filepath)
    JSON.parse(File.read(filepath))
  rescue JSON::ParserError => e
    puts "Error parsing #{filepath}: #{e.message}".red
    exit(1)
  rescue Errno::ENOENT
    puts "Configuration file not found: #{filepath}".red
    exit(1)
  end

  ##
  # Display current configuration header
  #
  def display_header
    puts 'Current configuration: '.bold
    display_config_info(@current_config)
    puts
  end

  ##
  # Display all available configurations
  #
  def display_configurations
    @configurations.each_with_index do |config, index|
      print '  '
      display_config_info(config, @config_files[index], index)
    end
  end

  ##
  # Display footer with options
  #
  def display_footer
    puts "  #{format_create_option} Create new configuration".green.bold
    puts "  #{format_quit_option} Quit without changing configuration".red.bold
    puts
    puts 'Current configuration: '.bold
    display_config_info(@current_config)
  end

  ##
  # Format configuration information for display
  #
  def display_config_info(config, filename = nil, index = nil)
    name = config[NAME_FIELD]
    suffix = build_filename_suffix(name, filename)

    print_index(index) if index
    print_config_name(config, name, suffix)
  end

  ##
  # Build filename suffix for display
  #
  def build_filename_suffix(name, filename)
    return '' unless filename

    # Extract just the filename from the full path
    display_filename = File.basename(filename)
    spaces = ' ' * [NAME_COLUMN_WIDTH - name.length, 0].max
    "#{spaces} (#{display_filename})".green
  end

  ##
  # Print formatted index
  #
  def print_index(index)
    formatted_index = format(INDEX_FORMAT, index)
    print "#{formatted_index} ".blue.bold
  end

  ##
  # Print configuration name with appropriate styling
  #
  def print_config_name(config, name, suffix)
    styled_name = favorite?(config) ? name.blue.bold : name.blue
    puts "#{styled_name} #{suffix}"
  end

  ##
  # Check if configuration is marked as favorite
  #
  def favorite?(config)
    config[FAVORITE_FIELD] == true
  end

  ##
  # Format quit option display
  #
  def format_quit_option
    "[#{QUIT_OPTION}]"
  end

  ##
  # Format create option display
  #
  def format_create_option
    "[#{CREATE_OPTION}]"
  end

  ##
  # Prompt user for configuration selection
  #
  def prompt_for_selection
    print 'Select configuration: '.blue.bold
    gets.chomp.strip
  end

  ##
  # Check if user requested to quit
  #
  def quit_requested?(input)
    input.empty? || input.downcase == QUIT_OPTION
  end

  ##
  # Check if user requested to create new configuration
  #
  def create_requested?(input)
    input.downcase == CREATE_OPTION
  end

  ##
  # Exit gracefully without changes
  #
  def exit_gracefully
    puts 'No configuration selected'.red
    exit(0)
  end

  ##
  # Switch to selected configuration
  #
  def switch_configuration(selection)
    index = parse_selection(selection)
    validate_selection(index)

    target_config = @configurations[index]
    target_file = @config_files[index]

    perform_configuration_switch(target_config, target_file)
  end

  ##
  # Parse user selection to integer
  #
  def parse_selection(selection)
    Integer(selection)
  rescue ArgumentError
    puts 'Invalid selection. Please enter a number.'.red
    exit(1)
  end

  ##
  # Validate selection is within valid range
  #
  def validate_selection(index)
    return if index.between?(0, @configurations.length - 1)

    puts 'Invalid selection. Please choose a valid configuration number.'.red
    exit(1)
  end

  ##
  # Perform the actual configuration file switching
  #
  def perform_configuration_switch(target_config, target_file)
    backup_filename = generate_backup_filename
    target_name = target_config[NAME_FIELD]

    puts "Backing up current configuration to #{backup_filename}".yellow
    FileUtils.cp(CURRENT_CONFIG_PATH, backup_filename)

    puts "Activating configuration: #{target_name.green.bold}".blue
    FileUtils.cp(target_file, CURRENT_CONFIG_PATH)

    puts "Configuration switch completed successfully!".green.bold
  end

  ##
  # Generate backup filename for current configuration
  #
  def generate_backup_filename
    suffix = @current_config[FILE_FIELD] ||
             @current_config[NAME_FIELD].downcase.gsub(/\s+/, '-')
    File.join(DEV_ENVS_DIR, "#{suffix}.json")
  end

  ##
  # Create a new configuration based on current one
  #
  def create_new_configuration
    puts "\nCreating new configuration based on current one...".blue.bold

    print "Enter name for new configuration: ".blue.bold
    config_name = gets.chomp.strip

    if config_name.empty?
      puts "Configuration name cannot be empty.".red
      return
    end

    # Generate filename from config name
    filename = "#{config_name.downcase.gsub(/\s+/, '-')}.json"
    filepath = File.join(DEV_ENVS_DIR, filename)

    if File.exist?(filepath)
      puts "Configuration '#{config_name}' already exists.".red
      return
    end

    # Create new configuration based on current one
    new_config = @current_config.dup
    new_config[NAME_FIELD] = config_name
    new_config[FILE_FIELD] = filename.gsub('.json', '')

    # Write new configuration file
    File.write(filepath, JSON.pretty_generate(new_config))

    puts "New configuration '#{config_name.green.bold}' created successfully!".blue
    puts "File saved as: #{filepath}".green

    # Ask if user wants to switch to the new configuration
    print "\nSwitch to new configuration now? (y/N): ".blue.bold
    response = gets.chomp.strip.downcase

    if response == 'y' || response == 'yes'
      FileUtils.cp(filepath, CURRENT_CONFIG_PATH)
      puts "Switched to new configuration: #{config_name.green.bold}".blue
    end
  end
end

# Execute the configuration manager
if __FILE__ == $PROGRAM_NAME
  ConfigurationManager.new.run
end


