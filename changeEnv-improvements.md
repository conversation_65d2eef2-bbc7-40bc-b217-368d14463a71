# Configuration Environment Manager - Improvements

## Changes Made

### 1. Directory Structure Update
- **Changed from**: `config/development-*-local.json` pattern
- **Changed to**: `config/dev-envs/*.json` directory structure
- **Active config**: Still uses `config/development.json` (unchanged)

### 2. Functional Improvements

#### A. Automatic Directory Creation
- The script now automatically creates the `config/dev-envs/` directory if it doesn't exist
- No manual setup required

#### B. Create New Configuration Feature
- **New option**: Press `[c]` to create a new configuration
- Creates a new config based on the current active configuration
- Prompts for a descriptive name
- Automatically generates filename from the name
- Prevents overwriting existing configurations
- Option to immediately switch to the new configuration

#### C. Improved File Operations
- **Changed from**: `File.rename()` (moves files)
- **Changed to**: `FileUtils.cp()` (copies files)
- **Benefit**: Preserves original files in `dev-envs/` directory
- **Benefit**: Safer operations - no risk of losing configurations

#### D. Enhanced Display
- Increased name column width from 20 to 25 characters
- Shows only filename (not full path) for cleaner display
- Added create option to footer menu

#### E. Better Error Handling
- Validates configuration names when creating new ones
- Checks for existing files before creating
- Graceful handling of empty inputs

## Usage Examples

### Switching Configurations
```bash
./changeEnv.rb
# Select from numbered list of available configurations
```

### Creating New Configuration
```bash
./changeEnv.rb
# Press 'c' when prompted
# Enter descriptive name (e.g., "Local Development")
# Choose whether to switch immediately
```

## File Structure
```
config/
├── development.json          # Active configuration (unchanged)
└── dev-envs/                # New directory for all environments
    ├── local-dev.json
    ├── staging-test.json
    ├── production-mirror.json
    └── custom-setup.json
```

## Migration from Old Structure

If you have existing `config/development-*-local.json` files:

1. Create the `config/dev-envs/` directory
2. Move your existing files to the new directory
3. Rename them to remove the `development-` prefix and `-local` suffix
   - `config/development-staging-local.json` → `config/dev-envs/staging.json`
   - `config/development-prod-local.json` → `config/dev-envs/prod.json`

## Benefits of New Structure

1. **Cleaner Organization**: All development environments in one dedicated folder
2. **Version Control Friendly**: Easier to ignore the entire `dev-envs/` folder
3. **Safer Operations**: Copy instead of move preserves original configurations
4. **Extensible**: Easy to add new configurations without complex naming conventions
5. **User-Friendly**: Create new configurations interactively
6. **Consistent**: All environment files follow the same simple naming pattern

## Backward Compatibility

The script maintains full backward compatibility with the active configuration file (`config/development.json`). Only the storage location for environment templates has changed.
