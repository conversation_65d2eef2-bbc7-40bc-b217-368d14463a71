import * as functions from 'firebase-functions';
import * as _firestore from '@google-cloud/firestore';
const cors = require('cors')({
  origin: '*',
});

// The Firebase Admin SDK to access the Firebase Realtime Database.
const admin = require('firebase-admin');
var serviceAccount = require("./service/calculating-cats-firebase-adminsdk-98abq-0ee45dc00b.json");
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://calculating-cats.firebaseio.com"
});
const firestore:_firestore.Firestore = admin.firestore(); 
firestore.settings({
  timestampsInSnapshots: true
})
const postResponse = (req, res:functions.Response, success:boolean, data:any) => {
  return cors(req, res, () => {
    res.send(JSON.stringify({
      success,
      data
    }))
  });
  
}



// // Start writing Firebase Functions
// // https://firebase.google.com/docs/functions/typescript
//
export const holaMundo = functions.https.onRequest((req, res) => {
  const v = '2019-07-24';
  firestore
    .collection('classrooms')
    .limit(1)
    .get()
    .then(snap => {
      postResponse(req, res, true, { v, data: snap.docs.map(doc => doc.data) });
    })
    .catch(err => postResponse(req, res, true, { v, data: err }) )
});

export const holaBacko = functions.https.onRequest((req, res) => {
  res.send(req.body);
});

export const revokeAuthSessionByUid = functions.https.onRequest((req, res) => {

  const params = req.body;
  const uid = params.uid;

  admin.auth().revokeRefreshTokens(uid)
  .then(() => {
    return admin.auth().getUser(uid);
  })
  .then((userRecord) => {
    return new Date(userRecord.tokensValidAfterTime).getTime() / 1000;
  })
  .then((timestamp) => {
    postResponse(req, res, true, {msg: 'Tokens revoked at: '+ timestamp})
  });

})

export const createTeacher = functions.https.onRequest((req, res) => {
  let params = req.body;
  let email = params.email || (params.username+'@teacher.calculatingcats.com');
  let authCreationPayload = {
    email: email,
    emailVerified: false,
    password: params.password,
    disabled: false
  }
  let userCreationPayload = {
    email: email,
    displayName: params.firstName + ' ' + params.lastName,
    firstName: params.firstName,
    lastName: params.lastName,
    ui: 'teacher',
    classroomsAsTeacher: [],
    uid: '',
  }
  if (params.classroomIds){
    userCreationPayload.classroomsAsTeacher = params.classroomIds;
  }
  
  admin.auth().createUser(authCreationPayload).then(function (userRecord) {
    let uid:string = userRecord.uid;
    userCreationPayload.uid = uid;
    firestore.doc('/users/'+uid)
    .set(userCreationPayload , {merge: true})
    .then((snapshot) => {
      if (!params.classroomIds){
        postResponse(req, res, true, { uid, isNoClasses: true });
      }
      else{
        let classroomIds:string[] = params.classroomIds;
        Promise.all(classroomIds.map(classroomId => {
          return firestore.doc('/classrooms/'+classroomId).set({
            currentTeachers: admin.firestore.FieldValue.arrayUnion(uid)
          }, {merge: true})
        }))
        .then ( () => {
          postResponse(req, res, true, { uid });
        })  
      }
    });
  })
  .catch(function(error) {
    postResponse(req, res, false, {error})
  });
});

interface ITaskAssignmentDef {
  name: string,
  itematicLessonId: string,
  legacy_resourceId: number,
  timeClose: number,
  timeOpen: number,
  legacy_slug: string,
  questions: Array<{
    itematicScreenId: string,
    screenshot: string
  }>,
  classrooms: string[],
}

export const injestAssignmentNames = functions.https.onRequest((req, res) => {
  let assignments:{assignmentId:string, name:string}[] = req.body;
  Promise.all(
    assignments.map(assignment => {
      return firestore.doc('/assignments/'+assignment.assignmentId).update({
        name: assignment.name,
        isArchived: false
      })
    })  
  ).then( ()=>{
    postResponse(req, res, true, {})
  })

});

const handleQuerySnapshotResponse = (req, res, querySnapshot) => {
  let map = {};
  querySnapshot.forEach(function(doc) {
      // doc.data() is never undefined for query doc snapshots
      map[doc.id] = doc.data();
  });
  postResponse(req, res, true, map);
}

export const getAssignmentScreenStateSubmCollectionQuery = functions.https.onRequest((req, res) => {
  var config = req.body;
  firestore.collection('/assignmentTaskScreenStates/')
    .where('assignmentId', '==', config.assignmentId)
    .where('itematicScreenId', '==', config.itematicScreenId)
    .where('uid', '==', config.uid)
    .get()
    .then( querySnapshot => handleQuerySnapshotResponse(req, res, querySnapshot) )
});

export const getAssignmentTaskSubmCollectionQuery = functions.https.onRequest((req, res) => {
  var config = req.body;
  firestore.collection('/assignmentTaskSubmissions/')
    .where('assignmentId', '==', config.assignmentId)
    .where('questionTaskId', '==', config.questionTaskId)
    .get()
    .then( querySnapshot => handleQuerySnapshotResponse(req, res, querySnapshot) )
});

export const postAssignmentTaskSubmCollectionQuery = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  var entries = req.body;
  // var lastPair;
  var requests = [];
  Object.keys(entries).forEach(id => {
    var entry = entries[id];
    // lastPair = [id, entry]
    requests.push( firestore.doc('/assignmentTaskSubmissions/'+id).set(entry) );
  })
  Promise.all(requests)  .then( () => {
    postResponse(req, res, true, {});
  })
  // postResponse(req, res, false, lastPair);
});

export const dumpAssignmentTaskSubmCollectionQuery = functions.https.onRequest((req, res) => {
  var config = req.body;
  firestore.collection('/assignmentTaskSubmissions/')
    .where('assignmentId', '==', config.assignmentId)
    .where('questionTaskId', '==', config.questionTaskId)
    .get()
    .then(function(querySnapshot) {
      let map = {};
      querySnapshot.forEach(function(doc) {
          // doc.data() is never undefined for query doc snapshots
          map[doc.id] = doc.data();
      });
      postResponse(req, res, true, map);
    })
});

export const getCollection = functions.https.onRequest((req, res) => {
  let pathDef = req.body;
  firestore.collection('/'+pathDef.path+'/').get().then( value => {
    let entries = [];
    value.docs.forEach(doc => {
      entries.push(doc.data());
    })
    postResponse(req, res, true, entries)
  })
});

const validateTeacherRole = (classroomId:string, teacherUid:string) => {
  return firestore.doc('/classrooms/'+classroomId)
    .get()
    .then( snap => {
      const classroomInfo = snap.data();
      if (classroomInfo){
        const currentTeachers:string[] = classroomInfo.currentTeachers;
        if (currentTeachers){
          if (currentTeachers.indexOf(teacherUid) !== -1){
            return true;
          }
        }
        else {
          throw new Error('not_classroom_teacher');
        }
      }
      else{
        throw new Error('no_classroom');
      }
      
      return false;
    })
    .catch(err => {
      console.error('validateTeacherStudentRelationship ERROR ', err)
    })
}

const validateTeacherStudentRelationship = (classroomId:string, teacherUid:string, studentUid:string) => {
  return firestore.doc('/classrooms/'+classroomId)
    .get()
    .then( snap => {
      const classroomInfo = snap.data();
      const currentTeachers:string[] = classroomInfo.currentTeachers;
      const currentStudents:string[] = classroomInfo.currentStudents;
      if (currentTeachers && currentStudents){
        if (currentTeachers.indexOf(teacherUid) !== -1){
          if (currentStudents.indexOf(studentUid) !== -1){
            return true;
          }
        }
      }
      else {
        throw new Error('not_classroom_teacher');
      }
      return false;
    })
    .catch(err => {
      console.error('validateTeacherStudentRelationship ERROR ', err)
    })
}

export interface IRequestBodyUpdateUsernamePassword {
  classroomId: string,
  teacherUid: string,
  studentUid: string,
  studentUsername: string,
  studentEmail: string,
  newPassword: string
}
export const updateUsernamePassword = functions.https.onRequest((req, res) => {
  // to do: validate the teacher token
  const reqData:IRequestBodyUpdateUsernamePassword = req.body;
  return validateTeacherStudentRelationship(reqData.classroomId, reqData.teacherUid, reqData.studentUid)
    .then(()=>{
      return admin.auth().updateUser(reqData.studentUid, {
        email: reqData.studentEmail,
        password: reqData.newPassword,
      })
    })
    .then(function(userRecord) {
      postResponse(req, res, true, {uid: reqData.studentUid})
    })
    .catch(function(error) {
      postResponse(req, res, false, {uid: reqData.studentUid})
    });
})

export interface IRequestBodyUpdateUserDisplayName {
  classroomId: string,
  teacherUid: string,
  teacherToken: any, // ?
  studentUid: string,
  firstName: string,
  lastName: string,
  displayName: string,
}
export const updateUserDisplayName = functions.https.onRequest((req, res) => {
  // to do: validate the teacher token
  const reqData:IRequestBodyUpdateUserDisplayName = req.body;
  return validateTeacherStudentRelationship(reqData.classroomId, reqData.teacherUid, reqData.studentUid)
    .then(()=>{
      return firestore
        .doc('/users/'+reqData.studentUid)
        .update({
          firstName: reqData.firstName,
          lastName: reqData.lastName,
          displayName: reqData.displayName,
        })
    })
    .then(function(userRecord) {
      postResponse(req, res, true, {uid: reqData.studentUid})
    })
    .catch(function(error) {
      console.log('Error updating user:', error);
      postResponse(req, res, false, {uid: reqData.studentUid})
    });

});

export interface IRequestNewTeacherStudent {
  classroomId: string,
  teacherUid: string,
  teacherToken: any, // ?
  email:string,
  username:string,
  password:string,
  displayName: string,
}
export const createTeacherStudent = functions.https.onRequest((req, res) => {
  // to do: validate the teacher token
  const reqData:IRequestNewTeacherStudent = req.body;
  let authCreationPayload = {
    email: reqData.email,
    emailVerified: false,
    password: reqData.password,
    disabled: false
  }
  let userCreationPayload = {
    email: reqData.email,
    displayName: reqData.displayName,
    username: reqData.username,
    ui: 'student',
    classroomsAsStudent: [],
    uid: '',
  }
  return validateTeacherRole(reqData.classroomId, reqData.teacherUid)
    .then(()=>{
      return admin
        .auth()
        .createUser(authCreationPayload)
    })
    .then((userRecord) => {
      let uid:string = userRecord.uid;
      userCreationPayload.uid = uid;
      firestore
        .doc('/users/'+uid)
        .set(userCreationPayload , {merge: true})
        .then((snapshot) => {
          postResponse(req, res, true, {uid: userCreationPayload.uid})
        });
    })
    .catch(function(error) {
      console.log('Error creating user:', error);
      postResponse(req, res, false, {payload: authCreationPayload})
    });

});


export const injestTaskScreenshots = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  let tasks:{taskId:string, screenshot:any}[] = req.body;
  Promise.all(
    tasks.map(task => {
      return firestore.doc('/tasks/'+task.taskId).update({
        screenshot: task.screenshot
      })
    })  
  ).then( ()=>{
    postResponse(req, res, true, {})
  })

});

export const injestTaskSubmissions = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  let submissions = req.body;
  submissions.forEach(element => {
    element.timeCreated = new Date(element.timeCreated);
    element.timeLastModified = new Date(element.timeLastModified);
  });
  Promise.all( 
    submissions.map(submission => firestore.collection('/assignmentTaskSubmissions/').add(submission) )
  ).then( ()=>{
    postResponse(req, res, true, {})
  })
});

export const generateClassCodes = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 're-enable as needed'});
  let config = req.body;
  return;
  // VERY DANGEROUS!!!!!!
  let dbTransactions = [];
  let batchSize = 1000;
  // let to = 100000;
  for (var i=config.from*1; i<config.from*1+batchSize; i++){
    let classCode = i.toString(16);
    dbTransactions.push(firestore.doc('/classroomsClassCode/'+classCode).set({
      isUsed: false,
      i,
      timeCreated: new Date(),
      classroomId: '',
    }));
  };
  Promise.all(dbTransactions).then( ()=>{
    postResponse(req, res, true, {})
  })
});


export const injestTaskAssignments = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  // if (true){
  //   console.error('Should not use this function again until we put protections for duplicate creations')
  //   // this code also resolves after the first assignment is processed (too early... so dont get all the data back)
  //   return 
  // }
  let collectedAssessments = [];
  let collectedQuestions = [];
  let collectedAssignments = [];
  let taskAssignments:ITaskAssignmentDef[] = req.body;
  Promise.all(
    taskAssignments.map(taskDefinition => {
      return firestore.collection('/tasks/').add({
        type: 'assessment',
        isArchived: false,
        isPublic: true,
        isDirectStart: false,
        isDirectClose: true,
        itematicLessonId: taskDefinition.itematicLessonId,
        legacy_resourceId: taskDefinition.legacy_resourceId,
        legacy_slug: taskDefinition.legacy_slug,
        name: taskDefinition.name,
        numThumbsUp: 0,
        numThumbsDown: 0,
        questions: []
      }).then( val => {
        let assessmentTaskId = val.id;
        console.log('create asmt', assessmentTaskId)
        collectedAssessments.push({assessmentTaskId, resource_id: taskDefinition.legacy_resourceId})
        let insertions = [];
        // create assignment
        taskDefinition.classrooms.forEach(classroomId => {
          console.log('create assignment', assessmentTaskId, classroomId);
          insertions.push(
            firestore.collection('/assignments/').add({
              classroom: classroomId,
              name: taskDefinition.name,
              tasks: [assessmentTaskId],
              isArchived: false,
              isDirectClose: false,
              isDirectStart: false,
              timeClose: new Date(taskDefinition.timeClose),
              timeStart: new Date(taskDefinition.timeOpen),
              timeCreated: new Date(taskDefinition.timeOpen),
              timeModified: new Date(taskDefinition.timeOpen),
            }).then( val => {
              let assignmentId = val.id;
              collectedAssignments.push({
                assignmentId, 
                assessmentTaskId, 
                classroom: classroomId,
                resource_id: taskDefinition.legacy_resourceId
              })
            })
          )
        })
        // define inner question
        taskDefinition.questions.forEach((question, questionIndex) => {
          console.log('create question', assessmentTaskId, question.itematicScreenId);
          insertions.push(
            firestore.collection('/tasks/').add({
              type: 'question_screen',
              itematicScreenId: question.itematicScreenId,
              screenshot: question.screenshot,
              timeCreated: admin.firestore.FieldValue.serverTimestamp(),
              parentTask: assessmentTaskId
            })
            .then(val=>{
              let questionTaskId = val.id;
              collectedQuestions.push({questionTaskId, itematicScreenId: question.itematicScreenId, assessmentTaskId, resource_id: taskDefinition.legacy_resourceId})
              let questionArrInsertion = {questions: {}};
              questionArrInsertion.questions[questionIndex] = questionTaskId

              return firestore.doc('/tasks/'+assessmentTaskId).set(questionArrInsertion, {merge: true})
            })
            // admin.firestore.FieldValue.arrayUnion(questionTaskId)
          )
        });
        return Promise.all(insertions)
      })
    })
  ).then( () => {
    postResponse(req, res, true, {
      collectedAssessments, 
      collectedAssignments, 
      collectedQuestions,
    })
  })
  
});

export const changeStudentPassword = functions.https.onRequest((req, res) => {
  // let params = req.body;
  // _createStudent(params, (isSuccess:boolean, data:any) => postResponse(req, res, isSuccess, data) );
});

const _createStudent = (params, resolve) => {
  if (!params.email){
    let possibleEmail:string = params.username;
    if (possibleEmail.indexOf('@') !== -1){
      params.email = possibleEmail
    }  
  }
  let email = params.email || (params.username+'@student.calculatingcats.com');
  let authCreationPayload = {
    email: email,
    emailVerified: false,
    password: params.password,
    disabled: false
  }
  let userCreationPayload = {
    email: email,
    displayName: params.firstName + ' ' + params.lastName,
    firstName: params.firstName,
    lastName: params.lastName,
    username: params.username,
    legacyUserId: params.legacyUserId,
    ui: 'student',
    classroomsAsStudent: [],
    uid: '',
  }
  if (params.classroomIds){
    userCreationPayload.classroomsAsStudent = params.classroomIds;
  }
  admin.auth().createUser(authCreationPayload).then(function (userRecord) {
    let uid:string = userRecord.uid;
    userCreationPayload.uid = uid;
    firestore.doc('/users/'+uid)
    .set(userCreationPayload , {merge: true})
    .then((snapshot) => {
      if (!params.classroomIds){
        resolve(true, { uid, isNoClasses: true, legacyUserId:userCreationPayload.legacyUserId });
      }
      else{
        let classroomIds:string[] = params.classroomIds;
        Promise.all(classroomIds.map(classroomId => {
          return firestore.doc('/classrooms/'+classroomId).set({
            currentStudents: admin.firestore.FieldValue.arrayUnion(uid)
          }, {merge: true})
        }))
        .then ( () => {
          resolve( true, { uid, legacyUserId:userCreationPayload.legacyUserId });
        })  
      }
    });
  })
  .catch(function(error) {
    resolve( false, {error, legacyUserId:userCreationPayload.legacyUserId})
  });
}

export const createStudent = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  let params = req.body;
  _createStudent(params, (isSuccess:boolean, data:any) => postResponse(req, res, isSuccess, data) );
});

export const createStudents = functions.https.onRequest((req, res) => {
  return postResponse(req, res, false, {msg: 'deprecated'});
  let students = req.body;
  let numResolved = 0;
  let aggregateData =  [];
  let isAllSuccessful:boolean = true;
  let onSingleResolution = (isSuccess:boolean, data:any) => {
    numResolved ++;
    console.log('Resolved: ', numResolved, 'of', students.length );
    isAllSuccessful = isAllSuccessful && isSuccess;
    aggregateData.push({isSuccess, data});
    if (numResolved >= students.length){
      postResponse(req, res, isAllSuccessful, aggregateData);
    }
  }
  students.forEach(student => {
    if (!student.password){
      student.password = 'math123';
    }
    _createStudent({
      username: student.uuid,
      password: student.password,
      legacyUserId: student.user_id,
      firstName: student.first_name,
      lastName: student.last_name,
      classroomIds: student.classroomIds
    }, onSingleResolution);
  });
  
});