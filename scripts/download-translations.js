const fs = require("fs");
var axios = require("axios");
const path = require("path");
const glob = require("glob");

const agentCreds = JSON.parse(fs.readFileSync('./scripts/creds/auth.json').toString('utf8'));
// const agentCreds = {
//   email: "<EMAIL>",
//   password: "password",
//   strategy: "local"
// };

if(!agentCreds.email || !agentCreds.password) {
  console.error('Please provide agent credentials in creds/auth.json');
  process.exit(1);
}

const profiles = {
  PROD: {
    API_ROOT: "https://eqao2-api.vretta.com"
  }
};

const profile = profiles.PROD;
const { API_ROOT } = profile;

const axiosCreate = async (route, data, API_HEADERS) => {
  try {
    const res = (
      await axios({
        method: "post",
        url: API_ROOT + route,
        data,
        headers: API_HEADERS
      })
    ).data;
    return res;
  } catch (e) {
    console.error(route, e);
  }
};

const axiosGet = async (route, params, API_HEADERS) => {
  try {
    const res = (
      await axios({
        method: "get",
        url: API_ROOT + route,
        params,
        headers: API_HEADERS
      })
    ).data;
    return res;
  } catch (e) {
    console.error(route, e);
  }
};

const axiosUpdate = async (route, params, API_HEADERS) => {
  try {
    const res = (
      await axios({
        method: "patch",
        url: API_ROOT + route,
        params,
        headers: API_HEADERS
      })
    ).data;
    return res;
  } catch (e) {
    console.error(route, e);
  }
};

const axiosFind = async (route, params, API_HEADERS) => {
  try {
    const res = (
      await axios({
        method: "get",
        url: API_ROOT + route,
        params,
        headers: API_HEADERS
      })
    ).data;
    return res;
  } catch (e) {
    console.error(route, e);
  }
};

const createTranslationMap = async records => {
  const map = {};
  const langs = ["en", "fr"];
  langs.forEach(lang => {
    const langVoice = lang + "_voice";
    map[lang] = {};
    map[langVoice] = {};
  });
  records.forEach(record => {
    const key = record.slug || record.id;
    langs.forEach(lang => {
      map[lang][key] = record[lang];
      const langVoice = lang + "_voice";
      const voiceUrl = record[langVoice];
      if (voiceUrl) {
        map[langVoice][key] = voiceUrl;
      }
    });
  });
  return map;
};

const directDownloadTranslationMapFromDb = async API_HEADERS => {
  return axiosFind(
    "/public/translation",
    {
      $limit: 6000
    },
    API_HEADERS
  )
    .then(activeRecords => {
      if (!activeRecords || !activeRecords.data) {
        console.log("No translations found");
        return {};
      }
      return createTranslationMap(activeRecords.data);
    })
    .catch(e => {
      console.error("Error downloading translations", e);
    });
};

const loginAs = async target_uid => {
  const authRes = await axiosCreate("/authentication", agentCreds, {});
  const API_HEADERS = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + authRes.accessToken
  };
  const alias_auth = await axiosCreate("/public/support/login", { target_uid }, API_HEADERS);
  const { accessToken } = await axiosCreate("/authentication", { id: alias_auth.id, secret: alias_auth.secret, strategy: "alias" }, API_HEADERS);
  return {
    "Content-Type": "application/json",
    Authorization: "Bearer " + accessToken
  };
};

const main = async () => {
  const API_HEADERS = await loginAs(21);
  const translations = await directDownloadTranslationMapFromDb(API_HEADERS);

  // remove ./data/translations.json
  // const translationsPath = path.join(__dirname, 'data', 'translations.json');
  const translationsPath = path.join(process.cwd(), "data", "translations.json");
  if (fs.existsSync(translationsPath)) {
    fs.unlinkSync(translationsPath);
  }
  fs.writeFileSync(translationsPath, JSON.stringify(translations, null, 2));
};

main();
