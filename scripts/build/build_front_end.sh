# Description: Integrates with .gitlab-ci.yml to build the front end node client using npx ng command with build
#              parameters based on the release branch; i.e., the --prod and --optimization options are configured
#              deoending on the current branch being deployed.
#
#              Typically any branch with "qc" in the name will have the "--optimization" option specified. Other
#              branches without "qc" in the name are generally deemed to be production and will use
#              "--prod --optimization" as the build parameters.
#
#              Build parameters based on branch name can be overridden by specifying the options to use within the
#              $build_opts_override variable.

# Configugure build parameter override here, leave as empty string to set options based on branch name
# Example: build_opts_override="--prod --optimization"

build_opts_override=""

# Compile app using npx ng build

echo -e "\nConfiguring npx ng build parameters..."

if [ -n "${build_opts_override}" ]; then

  message="\nBuild parameter override detected."
  build_opts="$build_opts_override"

elif [ $(echo "${CI_COMMIT_BRANCH}" | grep qc) ]; then

  message="\nQC environment detected from release branch name ${CI_COMMIT_BRANCH}."
  build_opts="--optimization"

else

  message="\nProduction environment detected from release branch name ${CI_COMMIT_BRANCH}."
  build_opts="--prod --optimization"

fi

echo -e "$message The following command will be used for the build:\nnpx ng build $build_opts\n.Beginning build..."

npx ng build $build_opts
