
import Redis  from 'ioredis';
import { sanitizeDateTime } from '../converts'
import { KTestSession, REDIS_ASSESSMENT_DATA_EXPIRE, RedisDate } from '../redis';
import { Application } from '../../declarations';
import { Id } from '@feathersjs/feathers';
import { addClassTestSessions, getClassTestSessions } from '../relation-key-operations/class-test-sessions'
import { addTestWindowTestSessions } from '../relation-key-operations/test-window-test-sessions';

/** associate redis keys/field name
 *  Add/Remove this key might need to update the following data
 *  ====================================================================
 *  RedisKey: ClassTestSessions
 */

/**
 * Redis Key "TestSession" Hash Values 
 */
export interface RedisKeyTestSession {
    // Table columns
    id: number,
    date_time_start: RedisDate,
    date_time_end: RedisDate,
    is_paused: number,
    test_window_id: number,
    is_closed: number,
    paused_by_uid: number | null,

    // Non current table columns
    slug:string, // scts.slug
    school_class_id: number,

    // Non database table values
    is_messaged: number,
    auto_close_after: RedisDate,
}

//dateTime Data Type
const dateTimeTypeKeys:string[] = ["date_time_start", "date_time_end", "auto_close_after"]

/**
 * Create "TestSession:{test_session_id}" hash value in redis
 * @param app  Target application
 * @param payload the new TestSession Data
 * @returns null
 */
export const createRedisTestSession = async (app: Application, payload: RedisKeyTestSession) => {
    await patchRedisTestSession(app, payload.id, payload)

    //update ClassTestSessions
    await addClassTestSessions(app, payload.school_class_id, [payload.id])

    //update TestWindowTestSessions
    await addTestWindowTestSessions(app, payload.test_window_id, [payload.id])
}

/**
 * Update Partial(all) "TestSession:{test_session_id}" hash value in redis
 * @param app  Target application
 * @param test_session_id the test session id
 * @param payload the update payload
 */
export const patchRedisTestSession = async (app: Application, test_session_id: Id, payload: Partial<RedisKeyTestSession>, is_expire = true) => {
    const redis: Redis = app.get('redis');

    //Sanitize dateTime type
    if(dateTimeTypeKeys.length){
        sanitizeDateTime(payload, dateTimeTypeKeys)
    }
    
    // Stored into redis
    const redis_key = KTestSession(test_session_id)
    if(is_expire){
        await redis.pipeline()
          .hmset(redis_key, payload)
          .expire(redis_key, REDIS_ASSESSMENT_DATA_EXPIRE)
          .exec();
    }else{
        await redis.pipeline()
        .hmset(redis_key, payload)
        .exec();
    }
}

/**
 * Patch "TestSession:{test_session_id}" in both DB and Redis  
 * DB Patch is done in the background
 * @param payload Should only contain fields that are both in DB and Redis
 */
export const patchTestSession = async (app: Application, test_session_id: Id, payload: Partial<RedisKeyTestSession>) => {
    if(dateTimeTypeKeys.length){
        sanitizeDateTime(payload, dateTimeTypeKeys)
    }
    
    app.service('db/write/test-sessions').patch(test_session_id, payload)
    return patchRedisTestSession(app, test_session_id, payload)
}

/**
 * Get "TestSession:{test_session_id}" hash value in redis
 * @param app  Target application
 * @param test_session_id test session id
 * @returns RedisKeyTestSession
 */
export const getRedisTestSession = async (app: Application, test_session_id: Id): Promise<RedisKeyTestSession> => {
    const redis: Redis = app.get('redis');
    const redisHash = await redis.hgetall(KTestSession(test_session_id));;

    const parsedRedisHash: RedisKeyTestSession = {
        id: parseInt(redisHash.id),
        date_time_start: redisHash.date_time_start === '' ? null : new Date(redisHash.date_time_start),
        date_time_end: redisHash.date_time_end === '' ? null : new Date(redisHash.date_time_end),
        slug: redisHash.slug,
        is_paused: parseInt(redisHash.is_paused),
        test_window_id: parseInt(redisHash.test_window_id),
        is_closed: parseInt(redisHash.is_closed),
        paused_by_uid: redisHash.paused_by_uid === '' ? null : parseInt(redisHash.paused_by_uid),
        school_class_id: parseInt(redisHash.school_class_id),
        is_messaged: parseInt(redisHash.is_messaged),
        auto_close_after: redisHash.auto_close_after === '' ? null : new Date(redisHash.auto_close_after)
    };

    return parsedRedisHash
}

/**
 * Get Array "TestSession:{test_session_id}" hash value in redis
 * @param app  Target application
 * @param test_session_ids
 * @returns RedisKeyTestSession[]
 */
export const getRedisTestSessions = async (app: Application, test_session_ids: Id[]): Promise<RedisKeyTestSession[]> =>{
    return Promise.all(test_session_ids.map(id => getRedisTestSession(app, +id)))
}

/**
 * Get array of "TestSession:{test_session_id}" hash values in redis associate with school_class_id
 * @param app  Target application
 * @param school_class_id school class id
 * @returns RedisKeyTestSession[]
 */
export const getRedisTestSessionFromSchoolClassId = async (app: Application, school_class_id:Id): Promise<RedisKeyTestSession[]> => {
    const test_session_ids = await getClassTestSessions(app, school_class_id)

    const redis_test_sessions = await getRedisTestSessions(app, test_session_ids)
    return redis_test_sessions
}

/**
 * Check of the test session if pause
 * @param app  Target application
 * @param test_session_id test session id
 * @returns boolean
 */
export const isTestSessionPaused = async (app: Application, test_session_id: Id): Promise<boolean> => {
    const redis: Redis = app.get('redis');
    return (await redis.hget(KTestSession(test_session_id), 'is_paused') === '1');
}