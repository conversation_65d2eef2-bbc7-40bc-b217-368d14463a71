import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle} from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { ITestAttempt, ITestAttemptInfo } from '../../../db/schemas/test_attempts.schema';
import { ITestFormQuesConfig } from '../../educator/report-results/report-results.class';
import { TestFormConstructionMethod } from '../../test-auth/test-design-question-versions/test-design-question-versions.class';
import { ILanguage, UpsertResult } from '../../test-question-register/tqr-publish/types';
import { QuestionContent } from '../../test-taker/invigilation/question-content/question-content.class';
import { IAttemptPayload } from '../../test-taker/invigilation/test-attempt/test-attempt.class';
import { IExpectedResponseData } from '../extract-item-response/extract-item-response.class';
import { ITestAttemptQuestionResponses } from '../session/session.class';
import { dbDateOffsetHours } from '../../../../util/db-dates';
import { getQuestionRespondableDeep } from './matrix-validation-helpers/utils';
import { scoreMatrixElementTypes } from './matrix-validation-helpers/model';
import { generatePossibleElementCombinations } from './matrix-validation-helpers/generate-combinations';

const _ = require('lodash');

enum LogType {
  Comment = "comment",
  Resolve = "resolve",
  Unresolve = "unresolve",
  AddSimulatedSubmission = "add_simulated_submission",
}

interface Data extends ISqaAuditConfig{
  test_window_id : number
  resolve_note? : string
  is_resolved? : boolean
  session_question_audit_ids? : number[]
  comment_text?: string
  log_type?: LogType,
}

interface ISqaAuditConfig {
  attemptProcessingInterval? : number,
  attemptProcessingLimit?: number,
  includeProcessedAttempts: boolean,
  includeProcessedTaqrs: boolean,
  studentOEN?: number,
}

interface IAuditCreationData {
  uid?: number
  test_attempt_id: number,
  test_question_id: number,
  audit_slug: EAuditSlugs,
  audit_description: string,
  score?: number
  response_raw? : any
  response? : string,
  created_by_uid: number
}

interface IAuditRequiredData {
  test_attempt_id: number,
  test_question_id: number,
  response_raw: string,
  module_id?: number,
  section_index?: number,
  response?: string
}

interface IFormattedResponse {
  formatted_response: any;
  score: number;
  weight: number;
}

type answer_map = Map<string, Map<string, number>> // maping items to formatted responses, and those formatted responses contain a score

export enum AuditStatus {
  RUNNING = "RUNNING",
  COMPLETED = "COMPLETED",
  UNCOMPLETED_INACTIVE = "UNCOMPLETED_INACTIVE"
}

export enum EAuditSlugs {
  POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED_WITH_MATRIX = 'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED_WITH_MATRIX',
  QUESTION_SCORE_NOT_ALIGNED_WITH_MATRIX = 'QUESTION_SCORE_NOT_ALIGNED_WITH_MATRIX',
  POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED = 'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED',
  QUESTION_SCORE_NOT_ALIGNED_WITH_EA = 'QUESTION_SCORE_NOT_ALIGNED_WITH_EA',
  QUESTION_SCORE_OUT_OF_RANGE = 'QUESTION_SCORE_OUT_OF_RANGE',
  QUESTION_NOT_ASSOCIATED_TO_TEST_FORM = 'QUESTION_NOT_ASSOCIATED_TO_TEST_FORM'  ,
  MSCAT_IMPOSSIBLE_PANEL_PATHWAY = 'MSCAT_IMPOSSIBLE_PANEL_PATHWAY',
}

export const auditSlugDescription  = {
  'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED_WITH_MATRIX': 'Student response is not aligned to the Matrix generated Expected Answers captured by EQAO Assessment team',
  'QUESTION_SCORE_NOT_ALIGNED_WITH_MATRIX': 'Student response score is not aligned to the Matrix generated Expected Answers captured by EQAO Assessment team',
  'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED' : 'Student response is not aligned to the Possible Expected Answers captured by EQAO Assessment team',
  'QUESTION_SCORE_NOT_ALIGNED_WITH_EA' : 'Student response score is not aligned to the Possible Expected Answers captured by EQAO Assessment team',
  'QUESTION_SCORE_OUT_OF_RANGE' : 'Student response contains a score value greater than the maximum score points indicated by EQAO Assessment team',
  'QUESTION_NOT_ASSOCIATED_TO_TEST_FORM': 'Student response relates to an item that is not associated with the test form in which they are assigned',
  'MSCAT_IMPOSSIBLE_PANEL_PATHWAY': "Student responses represent an impossible panel pathway (this can happen in the case of student un-submissions)."
}

export enum ACTIONS {
  AuditFlags = "AuditFlags",
  AuditHistory = "AuditHistory",
  AuditProgress = "AuditProgress"
}

//const NUM_ASYNC_ATTEMPTS = 20;

interface ServiceOptions {}

const SECONDS_TO_MS = 1000;

export class SessionQuestionAudit implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS')
    const action = params.query.action;
    const { test_window_id } = params.query
    if (action == ACTIONS.AuditFlags) {
      return await this.getAuditFlags(test_window_id);
    }
    if(action == ACTIONS.AuditHistory) {
      return await this.getBatchAuditLog(test_window_id);
    }
    else if (action == ACTIONS.AuditProgress){
      return await this.getAuditProgress(test_window_id);
    }
    else throw new Errors.BadRequest()
  }


  /**
   * Returns audit flags for a test window
   * @param test_window_id - ID of the test window
   * @returns Records of audit flags grouped by test question ID, audit flag type/slug, response and score at the time of flagging, and flag resolved status.
   * Additional info returned includes the first flagged response of the group as an example to display.
   *
   * Note:  Response, formatted_response, response_raw must come from the audit record which stored them at the time of flagging, the response in the TAQR could now be different
  */
  async getAuditFlags (test_window_id: number): Promise<any> {
    
    const records = await dbRawReadReporting(this.app, [test_window_id], `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ GROUP_CONCAT(DISTINCT sqa.id ORDER BY sqa.id) AS audit_ids 
        , COUNT(DISTINCT sqa.id) as num_cases
        , ta.id  as attempt_id
        , twtdar.slug
        , sqa.created_on first_flagged_on
        , um.value as StudentOEN
        , ta.uid
        , u.first_name
        , u.last_name
        , tq.question_label
        , tq.question_set_id as item_set_id
        , tq.question_set_id
        , sqa.test_question_id
        , tw.id as test_window_id
        , sc.access_code
        , ta.test_form_id
        , sqa.audit_description
        , sqa.resolve_note
        , tf.lang
        , taqr.weight
        , taqr.created_on taqr_created_on
        , taqr.updated_on taqr_updated_on
        , taqr.id taqr_id
        , taqr.module_id
        , taqr.section_id
        , sqa.is_resolved
        , sqa.audit_slug
        , sqa.response formatted_response
        , sqa.response_raw
        , sqa.score
        , tqr.is_field_trial
      from test_windows tw
      join test_sessions ts on ts.test_window_id = tw.id
      join test_attempts ta on ta.test_session_id = ts.id and ta.is_invalid = 0 and ta.started_on is not null
      join school_class_test_sessions scts on scts.test_session_id = ts.id
      join session_question_audit sqa on sqa.uid = ta.uid and sqa.test_attempt_id = ta.id and sqa.audit_slug != "QUESTION_NOT_ASSOCIATED_TO_TEST_FORM"
      join user_metas um on um.uid = ta.uid and um.key in ('StudentOEN')
      join users u on u.id = ta.uid
      left join test_questions tq on tq.id = sqa.test_question_id
      join school_classes sc on sc.id = scts.school_class_id
      left join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
      left join test_forms tf on tf.id = ta.test_form_id
      left join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and sqa.test_question_id = taqr.test_question_id  and taqr.is_invalid != 1
      join test_question_register tqr on tqr.question_id = tq.id and tqr.test_form_id = tf.id
      where tw.id = ? and (twtdar.slug is null or twtdar.is_sample != 1)
      GROUP BY sqa.test_question_id, sqa.audit_slug, sqa.response, taqr.score, sqa.is_resolved;
    ;`);

    return records

  }

  async getBatchAuditLog (test_window_id: number): Promise<any> {

    const auditHistory = await dbRawRead(this.app, [test_window_id], `
      select
      CONCAT(ur.first_name, ' ', ur.last_name) as created_by,
      sqal.id, sqal.audit_status, sqal.created_on, sqal.completed_on, sqal.updated_on,
      sqal.is_incl_processed_ta, sqal.is_incl_processed_taqr, sqal.ta_processing_interval, sqal.num_ta_requested, sqal.num_ta_completed
      from session_question_audit_log sqal
      left join users ur
        on ur.id = sqal.created_by_uid
      where sqal.test_window_id = ?
      order by sqal.created_on desc
    `);

    return auditHistory
  }

  async getAuditProgress (test_window_id: number): Promise<any> {
    const auditProgressRecords = await dbRawReadReporting(this.app, [test_window_id], `
    select
    COUNT(CASE WHEN (ta.is_real_time_audit_processed = 1 and twtdar.lang = "en") THEN 1 END) AS audit_processed_en_ta,
    COUNT(CASE WHEN twtdar.lang = "en" THEN 1 END) AS total_en_ta,
    COUNT(CASE WHEN (ta.is_real_time_audit_processed = 1 and twtdar.lang = "fr") THEN 1 END) AS audit_processed_fr_ta,
    COUNT(CASE WHEN twtdar.lang = "fr" THEN 1 END) AS total_fr_ta
    from test_windows as tw
    join test_sessions as ts
      on ts.test_window_id = tw.id
    join test_attempts as ta
      on ta.test_session_id = ts.id
    join test_window_td_alloc_rules twtdar
      on twtdar.id = ta.twtdar_id
    where tw.id = ?
    and ta.is_invalid != 1 and ta.started_on is not null
    and twtdar.is_sample != 1
    and twtdar.is_questionnaire != 1
    and ta.started_on is not null;
    `);
    return auditProgressRecords[0]
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<any> {
    // used to run verify question exist in test form audit :        - Depricated
    if(!id) throw new Errors.BadRequest();
    if(!params) throw new Errors.BadRequest('MISSING_PARAMS');
    const test_window_id = +id
    const current_uid = await currentUid(this.app, params)
    return await this.verifyQuestionsExistInTestForm(test_window_id, current_uid);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {

    if(!params?.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const { test_window_id, attemptProcessingInterval, attemptProcessingLimit, includeProcessedAttempts, includeProcessedTaqrs, studentOEN} = data;
    if(!data?.test_window_id) throw new Error("MISSING_TEST_WINDOW_ID");

    const current_uid = await currentUid(this.app, params);

    // Case 1: individual audit
    if (studentOEN){
      this.initAudit(data, params, current_uid)
      return {studentOEN}
    }

    // Case 2: batch audit
    const ongoingAudits = await dbRawRead(this.app, [test_window_id], `
    select
    sqal.id,
    sqal.created_on,
    CONCAT(ur.first_name, ' ', ur.last_name) AS username,
    CASE WHEN (sqal.updated_on > ${dbDateOffsetHours(this.app, -1)}) THEN "running"
      ELSE "uncompleted_inactive"
      END as current_status
    from session_question_audit_log sqal
    left join users ur
      on ur.id = sqal.created_by_uid
    where sqal.test_window_id = ?
    and sqal.is_completed != 1
    and audit_status = "RUNNING"
    order by sqal.created_on desc;
    `);

    const runningAudits = ongoingAudits.filter(audit => audit.current_status == "running")
    const uncompletedInactiveAudits = ongoingAudits.filter(audit => audit.current_status == "uncompleted_inactive")

    if(uncompletedInactiveAudits.length){
      for (let auditLog of uncompletedInactiveAudits){
        this.app.service('db/write/session-question-audit-log').patch(auditLog.id, {
          audit_status: AuditStatus.UNCOMPLETED_INACTIVE
        });
      }
    }

    if (runningAudits.length) {
      throw new Errors.Unavailable("ONGOING_AUDIT", {
        created_on: runningAudits[0].created_on,
        username: runningAudits[0].username
      })
    }

    const auditRecord = await this.app.service('db/write/session-question-audit-log').create({
      test_window_id,
      created_by_uid: current_uid,
      is_incl_processed_ta: includeProcessedAttempts ? 1 : 0,
      is_incl_processed_taqr: includeProcessedTaqrs ? 1 : 0,
      num_ta_requested: attemptProcessingLimit,
      ta_processing_interval: attemptProcessingInterval,
    })

    this.initAudit(data, params, current_uid, auditRecord.id, )
    return {batchAuditId: auditRecord.id}

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<any> {
    throw new Errors.MethodNotAllowed();
  }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async patch (id: NullableId, data: Data, params?: Params): Promise<any> {
    if(!id || !data || !params) throw new Errors.BadRequest();

    const { session_question_audit_ids, resolve_note, is_resolved } = data

    if(!session_question_audit_ids || !session_question_audit_ids.length)  throw new Errors.BadRequest();

    const patch = {
      resolve_note,
      is_resolved : is_resolved ? 1 : 0,
      updated_on: dbDateNow(this.app),
      updated_by_uid: await currentUid(this.app, params)
    }

    const patchRecordsPromises: Promise<Data>[] = [];
    for(const audit_id of session_question_audit_ids){
      const p = this.app.service('db/write/session-question-audit').patch(audit_id, patch);
      patchRecordsPromises.push(p);
    }

    return await Promise.all(patchRecordsPromises);
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    const audit_id = id 
    if (audit_id){
      return this.app.service('db/write/session-question-audit-log').patch(audit_id, {audit_status: AuditStatus.UNCOMPLETED_INACTIVE});
    }
    throw new Errors.BadRequest();
  }

  async _createAuditLog(data: IAuditCreationData){
    if(!data)  throw new Errors.BadRequest();

    let {
      test_attempt_id,
      test_question_id,
      audit_slug,
      audit_description,
      score,
      response_raw,
      response,
      uid,
      created_by_uid
    } = data


    if(!test_attempt_id ) throw new Errors.BadRequest("MISSING_DATA");

    let currentAttempt: ITestAttempt;

    if(!uid){
      // get associated user with the test_attempt
      currentAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);
      uid = currentAttempt.uid;
    }

    // check if the issue already exists in the log

    const existingISsues = await this.app
      .service('db/read/session-question-audit')
      .db()
      .where('test_attempt_id', test_attempt_id)
      .andWhere('test_question_id', test_question_id)
      .andWhere('audit_slug', audit_slug)
      .select();

    //Depending on the flag type, can flag again for the same question if the response or score has now changed
    if(existingISsues && existingISsues.length) {
      if (audit_slug == EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED || audit_slug == EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED_WITH_MATRIX) {
        return existingISsues.some(issue => issue.response == response);
      }
      else if (audit_slug == EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_EA || audit_slug == EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_MATRIX){
        return existingISsues.some(issue => issue.response == response && issue.score == score);
      }
      else if (audit_slug == EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE){
        return existingISsues.some(issue => issue.score == score);
      }
      else return;
    }

    return await this.app
      .service('db/write/session-question-audit')
      .create({
        uid,
        test_attempt_id,
        test_question_id,
        audit_slug,
        audit_description,
        score,
        response_raw,
        response,
        created_by_uid,
        created_on: dbDateNow(this.app)
      })
  }

  async initAudit(data:Data, params:Params, current_uid:number, auditRecordId?: number){
    const { test_window_id, attemptProcessingLimit, includeProcessedAttempts, includeProcessedTaqrs, studentOEN} = data;

    if(auditRecordId) console.log("----Start batch audit ID " + auditRecordId)
    else if (studentOEN) console.log("----Start audit for OEN " + studentOEN)

    const processedAttemptIds = await this.runRealTimeAuditsOnTw(data, auditRecordId, current_uid);

    // If in batch, update log table
    if (auditRecordId){
      await this.app.service('db/write/session-question-audit-log').patch(auditRecordId, {
        is_completed: 1,
        audit_status: AuditStatus.COMPLETED,
        completed_on: dbDateNow(this.app),
      });
    }

    logger.info('session-question-audit-logs', {
    created_by_uid: await currentUid(this.app, params),
    created_on: dbDateNow(this.app),
    options: JSON.stringify(data),
    results: JSON.stringify(processedAttemptIds)
    })

    if(auditRecordId) console.log("----complete batch audit ID " + auditRecordId)
    else if (studentOEN) console.log("----complete audit for OEN " + studentOEN)

  }

  async runRealTimeAuditsOnTw(data:Data, auditRecordId: number|null = null, current_uid:number) {
    const { test_window_id, attemptProcessingInterval, attemptProcessingLimit, includeProcessedAttempts, includeProcessedTaqrs, studentOEN} = data;

    // fetch student attempt(s)
    const test_attempts = await this.findTestAttemptsForTw(test_window_id, attemptProcessingLimit, includeProcessedAttempts, studentOEN);

    const test_design_cache: Map<number, any> = new Map();

    const tqr_form_cache: Map<number, Map<number, any>> = new Map();

    // map to keep track of both methods used to validate students response
    const item_auth_generated_ans: answer_map = new Map();
    const item_matrix_generated_ans: answer_map  = new Map();
    const processedAttempts: number[] = [];


    for(const test_attempt of test_attempts){

      const startTime = new Date().valueOf();

      try {
        const processedId = await this.processAttempt(test_attempt, test_design_cache, includeProcessedTaqrs, tqr_form_cache, current_uid, auditRecordId, item_auth_generated_ans, item_matrix_generated_ans);

        if(processedId) processedAttempts.push(processedId)

        const totalExecutionTime = new Date().valueOf() - startTime;

        if(attemptProcessingInterval ){
          const ms = attemptProcessingInterval * SECONDS_TO_MS;
          if(totalExecutionTime < ms){
            // wait for additional time before processing next
            const waitingTime = ms - totalExecutionTime
            await this.gutterTimer(waitingTime)
          }
        }
        if (auditRecordId){
          await this.app.service('db/write/session-question-audit-log').patch(auditRecordId, {
            num_ta_completed: processedAttempts.length
          });
        }
      }
      catch (error) {
        logger.error('Error processing test attempt in RT Audit', error, {
          batch_audit_id: auditRecordId || -1,
          test_attempt_id: test_attempt.id
        })
      }
    }

    //Process attempts in groups that run async simultaneously, update audit log after every group
    // for (let i = 0; i < test_attempts.length; i += NUM_ASYNC_ATTEMPTS) {
    //   const test_attempts_grouping = test_attempts.slice(i, i + NUM_ASYNC_ATTEMPTS);
    //   const ta_promises = test_attempts_grouping.map(test_attempt => {
    //     return this.processAttempt(test_attempt, test_design_cache, includeProcessedTaqrs, tqr_form_cache)
    //   });

    //   await (Promise as any).allSettled(ta_promises)
    //   .then((responses: any[]) => {
    //     responses.forEach((response:any) => {
    //       if (response.status === 'fulfilled') {
    //         processedAttempts.push(response.value);
    //       }
    //     });
    //   });

    //   if (auditRecordId){
    //     await this.app.service('db/write/session-question-audit-log').patch(auditRecordId, {
    //       num_ta_completed: processedAttempts.length
    //     });
    //   }
    // }

    return processedAttempts;

  }

  async processAttempt(currentAttempt:ITestAttempt, test_design_cache: Map<number, any>, includeProcessedTaqr: boolean, tqr_form_cache: Map<number, Map<number, any>>, current_uid:number, auditRecordId: number|null, item_auth_generated_ans:answer_map, item_matrix_generated_ans:answer_map){

    const { id: test_attempt_id, test_form_id, twtdar_id, lang, is_closed, closed_on, is_submitted, is_real_time_audit_processed: ta_processed} = currentAttempt;

    const flagAttemptAuditProcessed = (test_attempt_id:number) => {
      return this.app
      .service('db/write/test-attempts')
      .patch(+test_attempt_id, { is_real_time_audit_processed: 1 });
    }

    const taqrRecords = await this.findTestAttemptQuestionResponses(test_attempt_id);

    if(!taqrRecords || !taqrRecords.length) {
      await flagAttemptAuditProcessed(test_attempt_id)
      return test_attempt_id;
    }
    const filteredTaqrRecords = includeProcessedTaqr ? taqrRecords : taqrRecords.filter(r => !r.is_real_time_audit_processed)

    // all the Taqrs are processed for this attempt
    if(!filteredTaqrRecords || filteredTaqrRecords?.length === 0 ) {
      await flagAttemptAuditProcessed(test_attempt_id)
      return test_attempt_id;
    }

    const tf_record = await this.app.service('db/read/test-forms').get(test_form_id);
    let framework = test_design_cache.get(+tf_record.test_design_id);
    // cache test design
    if(!framework){
      const td_record = await this.app.service('db/read/test-designs').get(tf_record.test_design_id);
      framework = JSON.parse(td_record.framework || '');
      test_design_cache.set(+td_record.id, framework);
    }


    let testLang = lang // based on currentAttempt
    if(!testLang) testLang = tf_record.lang  // based on tf_record
    if(!testLang) {  // based on twtdar
      if(twtdar_id){
        const twtdar_record = await this.app.service('db/read/test-window-td-alloc-rules').get(+twtdar_id);
        testLang = twtdar_record.lang
      }
    }

    const tqrOfTAMap: Map<number, any> = new Map()
    const tqrInTestAttempt = await dbRawReadReporting(this.app, [test_attempt_id], `
    select tqr.*
    from test_attempts ta
    join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
    join test_question_register tqr on tqr.test_design_id = IFNULL(twtar.tqr_ovrd_td_id, twtar.test_design_id)
    where ta.id = ?
    `);
    tqrInTestAttempt.forEach(tqrRecord => tqrOfTAMap.set(tqrRecord.question_id, tqrRecord))

    let tqr_form_data = tqr_form_cache.get(test_form_id);
    if(!tqr_form_data){
      const tqrRecords = <any[]> await this.app.service('db/read/test-question-register')
      .find({
        query: {
          test_form_id
        },
        paginate: false,
      })
      const questionsInForm: Map<number, any> = new Map()
      tqrRecords.forEach(tqrRecord => questionsInForm.set(tqrRecord.question_id, tqrRecord))
      tqr_form_cache.set(test_form_id, questionsInForm)
    }

    // process TAQRs
    for(const currentTaqrRecord of filteredTaqrRecords){
      try {
        await this.runAudits(currentAttempt, currentTaqrRecord, { testLang, framework, taqrRecords, tqrOfTAMap, test_form_id, tqr_form_cache}, current_uid, item_auth_generated_ans, item_matrix_generated_ans);
        // update status for taqr record
        await this.app
        .service('db/write/test-attempt-question-responses')
        .patch(+currentTaqrRecord.id, { is_real_time_audit_processed: 1 });
      }
      catch (error) {
        logger.error('Error processing TAQR in RT Audit', error, {
          batch_audit_id: auditRecordId || -1,
          test_attempt_id,
          taqr_id: currentTaqrRecord.id
        })
      }

    }

    await flagAttemptAuditProcessed(test_attempt_id)

    return test_attempt_id;

  }


  async runAudits(currentAttempt: ITestAttempt, currentTaqrRecord: any, assessmentData: {testLang:string, framework: any, taqrRecords: any[], tqrOfTAMap: Map<number,any>, test_form_id: number, tqr_form_cache:Map<number, Map<number, any>>}, current_uid:number, item_auth_generated_ans:answer_map, item_matrix_generated_ans:answer_map) {

    const {id: test_attempt_id, uid} = currentAttempt;
     const { test_question_id, response_raw, is_paper_format } = currentTaqrRecord;

     const {framework, testLang, taqrRecords, tqrOfTAMap, test_form_id, tqr_form_cache} =  assessmentData;

      const data: IAuditRequiredData = {
        response_raw,
        test_attempt_id,
        test_question_id
      }

      const tqrRecord = tqrOfTAMap.get(test_question_id)

      const tqrFormRecord = tqr_form_cache.get(test_form_id)?.get(test_question_id)
      if(!tqrFormRecord){
        await this._createAuditLog({
          uid,
          test_attempt_id,
          test_question_id,
          audit_slug: EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM,
          audit_description: auditSlugDescription[EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM],
          created_by_uid: current_uid
        })
      }


      let isEligible = true
      // Determine eligibility to run the audit
      if(this.isQuestionnaire(framework, testLang, tqrRecord)) isEligible = false;
      if(this.isReadingSelectionPage(testLang, tqrRecord)) isEligible = false;
      if(!this.isRespondable(tqrRecord)) isEligible = false

      if(!isEligible)  return Promise.resolve();

      // If it's a paper/scan response, no point in comparing to expected responses and scores
      if (!is_paper_format) {
        const formatted_response_record:IFormattedResponse = await this.app
        .service("public/student/extract-item-response")
        .processResponse(response_raw, +test_question_id);

        data.response = formatted_response_record.formatted_response;

        await this.runExpectedAnswerAudit(data, currentAttempt, framework, formatted_response_record, testLang, tqrRecord, current_uid, item_auth_generated_ans, item_matrix_generated_ans);
      }
      await this.checkForImpossiblePathWay(data, currentAttempt, framework, currentTaqrRecord, taqrRecords, current_uid);
  }


  async runExpectedAnswerAudit(data:IAuditRequiredData, currentAttempt:ITestAttempt, framework:any, formatted_response_record:IFormattedResponse, testLang: string, tqrRecord:any, current_uid:number, item_auth_generated_ans:answer_map, item_matrix_generated_ans:answer_map) {
    const {test_attempt_id, test_question_id} = data;

    const { formatted_response : currentAnswer, score : currentScore} = formatted_response_record

    // const ta_record:ITestAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);

    const qScoreInfo = await this.app
    .service('public/test-auth/test-question-scoring-info')
    .get(test_question_id, { query: { lang: testLang }});

    if(qScoreInfo && qScoreInfo.length && qScoreInfo[0].is_human_scored) return;

    const is_sample = await dbRawReadSingle(this.app, [test_attempt_id], `
      SELECT twtar.is_sample
      FROM test_attempts ta
      JOIN test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
      where ta.id = ?
    ;`)


    const scorePoint = (tqrRecord['score_points'] && !isNaN(tqrRecord['score_points'])) ? +tqrRecord['score_points'] : 0;

    let isPossibleEaFound = false;
    let isScoreAligned = false;
    let isScoreOutOfBounds = false;
    let isMatrixValidated = false; // boolean to keep track if it can be matrix validated
    let isMatrixError: boolean | string = false;
    // In case of multilingual assesments each question gets a different key for enabled language  and in case different test design was used
    const test_question_key = test_question_id + '_' + testLang + '_' + tqrRecord.test_design_id; 

    if(!item_auth_generated_ans.has(test_question_key)){ // populate a map using the expected answer table to reduce time complexity
      const item_auth_exp_ans_map: Map<string, number> = new Map();
      item_auth_generated_ans.set(test_question_key, item_auth_exp_ans_map);
      // get all possible EAs for the questions
      const possibleExpectedAnswers  = <any[]>await this.app
        .service("public/student/extract-item-response")
        .find({ query : { test_question_id, lang: testLang } , paginate : false } )
  
      possibleExpectedAnswers.forEach((expectedAns: IExpectedResponseData) => {
          const { formatted_response: formatted_ea,  score, is_score_matrix_validated} = expectedAns;
          if(is_score_matrix_validated !== 0){
            item_auth_exp_ans_map.set(formatted_ea, score);
          }
        });
    }

    const score = item_auth_generated_ans.get(test_question_key)?.get(currentAnswer);
    if(score !== undefined) { // check if the map has the answer and score aligns
      if(this.roundNumeric(+score) === this.roundNumeric(+currentScore)){isScoreAligned = true};
      isPossibleEaFound = true;
    }
    if(((!isPossibleEaFound || !isScoreAligned ) && currentAnswer)){  // only run matrix audit if possibleEA is not found or score doesn't align so we don't have to do costly operations
      if(!item_matrix_generated_ans.has(test_question_key)){ // populate a map using the matrix combination generation table to reduce time complexity
        const questionData:{config:string} = await this.app.service('public/test-auth/test-design-question-versions').getTestDesignQuestionVersion(test_question_id, tqrRecord.test_design_id);
        const question = JSON.parse(questionData.config);
        const respondables = getQuestionRespondableDeep(testLang === 'en' ? question : question.langLink); // retrieve content by language
        const item_matrix_exp_ans_map: Map<string, number> = new Map();
        item_matrix_generated_ans.set(test_question_key, item_matrix_exp_ans_map);

        // check if it can be validated through matrix (this can only be done for single Dnd, grouping, insertion, and ordering)
        if (respondables?.length === 1 && scoreMatrixElementTypes.includes(respondables[0].elementType)) {
          isMatrixValidated = true;
          try{
            const combinations = generatePossibleElementCombinations(respondables[0]);
            if(!combinations.length){
              isMatrixError = "MISSING MATRIX"; // if not combinations return then it isn't matrix validated
            }
            for (let combination of combinations) {
              const response = {
                response_raw: JSON.stringify(combination),
                item_id: test_question_id,
              };
              const subRecord: any = await this.app
                .service("public/test-ctrl/schools/student-attempts")
                .processResponseRaw({
                  response,
                  responseTypes: <{ [key: string]: boolean }>{},
                }); // generate formatted response
                item_matrix_exp_ans_map.set(subRecord[0].response, subRecord[0].score);
            }
          } catch (e){
             // if an error occured then it wasn't matrix validated
            isMatrixError = "ERROR VALIDATING"
          }
        }

      }
      const score = item_matrix_generated_ans.get(test_question_key)?.get(currentAnswer);
      if(score !== undefined){
        isPossibleEaFound = true;
        if(this.roundNumeric(+score) === this.roundNumeric(+currentScore)) isScoreAligned = true;
      }
    }

    if(currentAnswer && !isPossibleEaFound){
      const slug = isMatrixValidated ? EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED_WITH_MATRIX : EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED; // change slug depending on if it was matrix validated
      await this._createAuditLog({
          ...data,
          uid: currentAttempt.uid,
          score: currentScore,
          audit_slug : slug, 
          audit_description: isMatrixError? isMatrixError + ' - ' : ''  + auditSlugDescription[slug],
          created_by_uid: current_uid
        });
    } 

    if(currentAnswer && isPossibleEaFound && !isScoreAligned) {
        const slug = isMatrixValidated ? EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_MATRIX : EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_EA; // change slug depending on if it was matrix validated
        await this._createAuditLog({
          ...data,
          uid: currentAttempt.uid,
          score: currentScore,
          audit_slug : slug, 
          audit_description: isMatrixError? isMatrixError + ' - ' : ''  + auditSlugDescription[slug],
          created_by_uid: current_uid
        });
    }

    if(!is_sample){
      if(+currentScore > scorePoint ) isScoreOutOfBounds = true;

      if(isScoreOutOfBounds) {
          await this._createAuditLog({
            ...data,
            uid: currentAttempt.uid,
            score: currentScore,
            audit_slug : EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE,
            audit_description: auditSlugDescription[EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE],
            created_by_uid: current_uid
          });
      }
    }
  }


  // for all the TAQRs - could be ran as periodical script
  async verifyQuestionsExistInTestForm(test_window_id : number, current_uid:number) {

    const slug = EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM
    const processedTaqrs = this.generateUpsertResObj();
    const auditLogs = this.generateUpsertResObj();

    const taqrPromises: Promise<any>[] = [];
    const logPromises: Promise<any>[] = [];

    const test_attempts = await this.findTestAttemptsForTw(test_window_id);

    // cache test_form
    const test_form_map: Map<number, any>  = new Map()

    for(const ta of test_attempts){
      const test_attempt_id = +ta.id;
      const uid = +ta.uid;

      let test_form = test_form_map.get(+ta.test_form_id)

      if(!test_form) {
        test_form =  await this.app.service('public/student/session').loadAttemptTestFormData(ta);
        test_form_map.set(+ta.test_form_id, test_form)
      }

      // fetch all TAQRs
      const taqr_records = <any[]>await this.app
      .service('db/write/test-attempt-question-responses')
      .find({
        query: {
          $select: ['id', 'test_question_id'],
          test_attempt_id,
          is_invalid: 0,
          is_processed_tf_verify: 0,
        },
        paginate: false
      });

      if(taqr_records && taqr_records.length) {

        const questionsSet = this.objectToNumSet(test_form.questionDb);

        for(const taqr of taqr_records){

          const test_question_id = +taqr.test_question_id
          if(!questionsSet.has(test_question_id)){

            // create log
            const log = this._createAuditLog({
              uid,
              test_attempt_id,
              test_question_id,
              audit_slug: slug,
              audit_description: auditSlugDescription[slug],
              created_by_uid: current_uid
            })

            logPromises.push(log);

          }
          // create record in TAQR
          const taqrProcessLog = this.app
            .service('db/write/test-attempt-question-responses')
            .patch(+taqr.id, { is_processed_tf_verify: 1 })
            .then( (res) =>  processedTaqrs.updated.push(+taqr.id))
            .catch( (e) => processedTaqrs.failed.push(+taqr.id))
          taqrPromises.push(taqrProcessLog);

        }
      }

    }

    await Promise.all(logPromises);
    await Promise.all(taqrPromises);
    return processedTaqrs;
  }

  async checkForImpossiblePathWay(data: IAuditRequiredData, currentAttempt:ITestAttempt, framework:any, currentTaqrRecord:any, taqrRecords: any[], current_uid:number) {

    // Only for MSCAT
    const {test_attempt_id, test_question_id, response_raw} = data;

    const { testFormType } = framework;

    if(testFormType !== TestFormConstructionMethod.MSCAT) return;

    const moduleToStage: Map<number, number> = new Map();
    const seenStageWithModule : Map<number, Set<number>> = new Map();

    // generate module to stage map
    framework.panelAssembly.allModules.forEach((m:any) => moduleToStage.set(+m.id, +m.stageNumber));

    const currentModuleId = currentTaqrRecord.module_id;
    const currentStage: any = moduleToStage.get(+currentModuleId);

    // const uniqueModules = [ ...new Set([...taqrRecords.map((m:any) => +m.module_id)])]

    let isImpossiblePanelPathWay = false;
    const impossibleModuleIds: number[][] = [];


    // construct seenStageToModule map
    taqrRecords?.forEach( record => {
      const { module_id} = record
      const relatedStageNumber = <number>(moduleToStage.get(module_id))
      if(relatedStageNumber){
        if(seenStageWithModule.has(relatedStageNumber)) {
          seenStageWithModule.get(relatedStageNumber)?.add(+module_id);
        } else {
          seenStageWithModule.set(relatedStageNumber, new Set([+module_id]))
        }
      }
    });

    const totalUniqueModulesAssociatedWithStageFromTaqr = seenStageWithModule.get(currentStage)
    if( totalUniqueModulesAssociatedWithStageFromTaqr ){
      const modules = Array.from(totalUniqueModulesAssociatedWithStageFromTaqr)
      if(modules.length > 1){  // students can only access one module from one stage
        isImpossiblePanelPathWay = true;
        impossibleModuleIds.push(modules);
      }
    }



    // uniqueModules.forEach(module_id => {
    //   const relatedStageNumber = moduleToStage.get(module_id)

    //   if(relatedStageNumber) {

    //     if(seenStageWithModule.has(relatedStageNumber)) {
    //       const preExistedModule = seenStageWithModule.get(relatedStageNumber);
    //       if(preExistedModule != null && preExistedModule != +module_id) {
    //         isImpossiblePanelPathWay = true;
    //         impossibleModuleIds.push([preExistedModule, +module_id]);
    //       }
    //     } else {
    //       seenStageWithModule.set(+relatedStageNumber, +module_id)
    //     }

    //   }
    // });

    if(isImpossiblePanelPathWay && impossibleModuleIds.length) {
      const slug = EAuditSlugs.MSCAT_IMPOSSIBLE_PANEL_PATHWAY
      await this._createAuditLog({
        ...data,
        uid: currentAttempt.uid,
        audit_slug: slug,
        audit_description: auditSlugDescription[slug] + ` Impossible Module ids: ${JSON.stringify(impossibleModuleIds)}`,
        created_by_uid: current_uid
      })
    }

  }

  async findTestAttemptsForTw(test_window_id: number , limit?: number, includeProcessedAttempts?: boolean , StudentOEN?: number) {

    let targetUID = ''
    if(StudentOEN){
      const db:Knex = await this.app.get('knexClientReadReporting');
      const studentUid = await db('user_metas')
      .where('key', 'StudentOEN')
      .andWhere('key_namespace', 'eqao_sdc')
      .andWhere('value', +StudentOEN)
      .select("uid");

      //For some reason had a case on local, QC6 where the above query took a while and timed out - in which case used the commented out version below
      //Can't replicate, I think it was a temporary issue on local. The above query should work to get the UID quickly.

      // const studentUid = await dbRawRead(this.app, [+StudentOEN], `
      // select /*+ MAX_EXECUTION_TIME(1440000000)*/ uid from user_metas um
      // where um.key = 'StudentOEN'
      // and um.key_namespace = 'eqao_sdc'
      // and um.value = ?;
      // `);

      if(studentUid && studentUid.length && studentUid[0].uid){
        targetUID = studentUid[0].uid
      } else {
        // no uid found
        return [];
      }
    }

    const plainQuery = `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ ta.*, tf.test_design_id
      from test_windows as tw
      join test_sessions as ts
        on ts.test_window_id = tw.id
      join test_attempts as ta
        on ta.test_session_id = ts.id
      join test_forms as tf
        on tf.id = ta.test_form_id
      join test_window_td_alloc_rules twtdar
        on ta.twtdar_id = twtdar.id
      where tw.id = ?
      and ta.is_invalid != 1
      and ta.started_on is not null
      and twtdar.is_sample != 1
      and twtdar.is_questionnaire != 1
      ${targetUID? `and ta.uid = ${targetUID}` : ''}
      ${!includeProcessedAttempts? 'and ta.is_real_time_audit_processed != 1' : ''}
      ${limit? `limit ${limit}` : ''}
      ;
    `
    const test_attempts = await dbRawReadReporting(this.app, [test_window_id], plainQuery);
    return test_attempts;

  }

  async findTestAttemptQuestionResponses(test_attempt_id: number, includeProcessedTaqr?: boolean) {
    const taqr_records = <any[]>await this.app
      .service('db/write/test-attempt-question-responses')
      .find({
        query: {
          $select: ['id', 'test_question_id', 'response_raw', 'score', 'weight', 'section_id', 'module_id', 'is_real_time_audit_processed', 'is_paper_format'],
          test_attempt_id,
          is_invalid: 0,
        },
        paginate: false
      });

      return taqr_records;
  }

  isQuestionnaire = (framework: any, lang: string, tqrRecord:any) => {
    const isQuestionnaireAssessment = framework.isQuestionnaireAssessment;
    const isCurrQuestionQuestionnaire = tqrRecord && tqrRecord.is_questionnaire
    if(isQuestionnaireAssessment || isCurrQuestionQuestionnaire) return true;
    return false;
  }

  isReadingSelectionPage = (lang: string, tqrRecord:any) => {
    if(tqrRecord && tqrRecord.is_reading_passage) return true;
    return false;
  }

  isRespondable = (tqrRecord:any) => {
    if(tqrRecord && tqrRecord.is_respondable) return true;
    return false;
  }

  // util
  private objectToNumMap(obj: { [key: string | number]: any }) {
    const map = new Map();
    Object.keys(obj).forEach((key:string | number) => {
      map.set(+key, obj[key]);
    })
    return map;
  }

  private objectToNumSet(obj: { [key: string | number]: any }) {
    const set: Set<number> = new Set();
    Object.keys(obj).forEach((key:string | number) => {
      set.add(+key)
    })
    return set;
  }

  private generateUpsertResObj = () => {
    return <UpsertResult>  {
      created: [],
      failed: [],
      updated: [],
    }
  }

  async gutterTimer(rate: number){
    return new Promise((resolve, _) => {
      setTimeout(resolve, rate)
    })
  }

  roundNumeric(val: number, decimalPoint: number = 4) {
    const roundConst = Math.pow(10, decimalPoint);
    return Math.round((val + Number.EPSILON) * roundConst) / roundConst
  }



}
