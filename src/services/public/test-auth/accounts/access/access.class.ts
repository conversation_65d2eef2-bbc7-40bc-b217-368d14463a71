import { Id, NullableId, <PERSON><PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from "../../../../../constants/db-extracts";
import { defaultDomain } from "../../../../../constants/mail-constants";
import { Application } from '../../../../../declarations';
import { Errors } from "../../../../../errors/general";
import { AccountType } from "../../../../../types/account-types";
import * as DBT from "../../../../../types/db-types";
import { normalizeDomain } from "../../../../../util/domain-whitelist";
import { currentUid } from '../../../../../util/uid';
import { IRoleDef } from '../../../../auth/invitation/invitation.class';
import { stringList } from 'aws-sdk/clients/datapipeline';
import { dbDateNow } from '../../../../../util/db-dates';
import { IUGroupSingular } from "../../../../db/schemas/u_groups_singular.schema";
import { NotifType } from '../../notifications/notifications.class';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

interface INewAccountInfo {
  firstName?: string,
  lastName?: string,
  email: string | string[],
  role_type: DBD_U_ROLE_TYPES,
  /////////
  isAutoEmail?: boolean,
  langCode?: string,
  domain?: string,
  group_id: number,
  isAssessment?: boolean,
  expires_on?: string
}

export class Access implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  getPrimaryGroupRoleTypes(role_type: DBD_U_ROLE_TYPES) {
    let roleTypes = [role_type];

    if (role_type === DBD_U_ROLE_TYPES.test_item_author_super){
      roleTypes.push(DBD_U_ROLE_TYPES.test_item_author)
    }

    if(role_type.startsWith('bc_auth')) {
      roleTypes.push(DBD_U_ROLE_TYPES.bc_auth_base);
    }
    return roleTypes;
  }

  async createNewAuthor(data: {
    isAutoEmail?: boolean,
    roles: IRoleDef[],
    domain?: string,
    singular_group_id: number,
    email: string,
    created_by_uid: number,
    firstName?: string,
    lastName?: string,
    langCode: string
  }) {
    const {isAutoEmail, domain, singular_group_id, email, created_by_uid, langCode} = data;

    let roles = data.roles;
    // create personal group.
    const personal = await this.app.service('db/write/u-groups').create({
      group_type: 'questionbank',
      description: '%PERSONAL%'
    });

    roles = roles.concat( [
      {
        role_type: DBD_U_ROLE_TYPES.test_item_author,
        group_id: singular_group_id, // for the singular group
      },
      {
        role_type: DBD_U_ROLE_TYPES.test_item_author,
        group_id: personal.id,
      },
      {
        role_type: DBD_U_ROLE_TYPES.test_item_author_super,
        group_id: personal.id,
      }
    ]);

    const DOMAIN = normalizeDomain(domain || defaultDomain);

    return this.app
    .service('auth/invitation')
    .create({
      account_type: AccountType.TEST_AUTH,
      roles,
      created_by_uid,
      first_name: data.firstName,
      last_name: data.lastName,
      contact_email: email,
      isAutoEmail,
      langCode,
      offsetDays: 7,
      domain: DOMAIN,
      emailSubjectSlug: 'subj_email_acct_invite_im_auth',
      emailTemplateSlug: 'email_acct_invite_im_auth',
      emailTemplateParams: {
        DOMAIN,
        LANG_CODE: langCode || 'en',
        EMAIL: email,
        EMAIL_ENCODED: encodeURIComponent(email || ''),
      },
    })
  }

  async getSingularGroupId() {
    const groupsSingularRecords = <Paginated<IUGroupSingular>> await this.app
      .service('db/read/u-groups-singular')
      .find({
        query:{
          group_type: 'mpt_test_controller'
        }
      });
    return groupsSingularRecords.data[0].id
  }

  async create (data: INewAccountInfo, params?: Params): Promise<Data> {

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = <number> userInfo.uid;
    const {
      isAutoEmail,
      role_type,
      domain,
      group_id,
      isAssessment,
      expires_on
    } = data;
    const langCode = data.langCode || 'en';

    const single_group_id = await this.getSingularGroupId();
    // get groupid of the mpt_test_controller group

    const roles: IRoleDef[] = [];

    const primaryGroupRoleTypes:DBD_U_ROLE_TYPES[] = this.getPrimaryGroupRoleTypes(role_type);

    primaryGroupRoleTypes.forEach(primaryRoleType => {
      roles.push({
        role_type: primaryRoleType,
        group_id,
        expires_on
      });
    });


    let emails:string[] = [];
    if(Array.isArray(data.email)) {
      emails = data.email;
    } else{
      emails = [data.email];
    }

    const db:Knex = await this.app.get('knexClientRead');

    let existingUsers:any[] = await db('users as u').join('auths as a', 'u.id', 'a.uid').whereIn('a.email', emails).select('u.*', 'a.email as auth_email')

    const existingUnclaimedUsers:any[] = await db('users as u')
    .where('u.is_claimed', 0)
    .join('u_invites as i', 'i.uid', 'u.id')
    .whereIn('u.contact_email', emails)
    .where( builder => builder.whereNull('i.expire_on').orWhere('i.expire_on', '>', dbDateNow(this.app)))
    .where('i.is_revoked', 0)
    .groupBy('u.contact_email') //May have multiple unclaimed with the same contact email and valid invites, but just want one record here.
    .select('u.*', 'u.contact_email as auth_email')

    existingUsers = existingUsers.concat(existingUnclaimedUsers);

    const existingEmails = existingUsers.map( u => u.auth_email.toLowerCase());

    const emailsToInvite = emails.filter( email => !existingEmails.includes(email.toLowerCase()) );

    const invites = emailsToInvite.map((email) => {
      return this.createNewAuthor({
        isAutoEmail,
        roles,
        domain,
        singular_group_id: <number>single_group_id,
        email,
        created_by_uid,
        firstName: data.firstName,
        lastName: data.lastName,
        langCode
      })
    })


    await Promise.all(invites);

    const uidsToUpdate = existingUsers.map( u => u.id );

    await this.updateRoles(uidsToUpdate, group_id, role_type, expires_on);

    let item_set_id: number;

    if(isAssessment) {
      const res = await this.app.service('db/read/temp-question-set').db().where('single_group_id', group_id).select('id');
      item_set_id = res[0].id;

      //Notify all e-mails shared
      for(const email of emails) {
        //Test auth notifications
        this.app.service('public/test-auth/notifications').createNotif( {
          config: {
            notifType: NotifType.SHARED_ASSESSMENT,
            itemId: item_set_id,
            uid: userInfo.uid,
            email
          },
          forDevCoords: true
        }, params);
      }
    } else {
      //Notify all e-mails shared
      for(const email of emails) {
        //Test auth notifications
        this.app.service('public/test-auth/notifications').createNotif( {
          config: {
            notifType: NotifType.SHARED_AUTH_GROUP,
            itemId: group_id,
            uid: userInfo.uid,
            email
          },
          forDevCoords: true
        }, params);
      }
    }

    const DOMAIN = normalizeDomain(domain || defaultDomain);

    //email notifications
    const notifications = existingUsers.map(async (user) => {
      //Send an e-mail
      const translation = this.app.service('public/translation');

      const emailSubjectSlug = isAssessment ? 'subj_email_acct_access_grant_asmt' : 'subj_email_acct_access_grant_group';
      const emailTemplateSlug = isAssessment ? 'email_acct_access_grant_asmt' : 'email_acct_access_grant_group'

      return this.app.service('mail/core').sendEmail({
        whitelabel: 'https://bced-qc.vretta.com',
        emailAddress: user.contact_email,
        subject: await translation.getOneBySlug(emailSubjectSlug, langCode),
        emailTemplate: await translation.getOneBySlug(emailTemplateSlug, langCode),
        parameterMapping: { EMAIL: user.auth_email,
          DASH_LINK: `${DOMAIN}#/en/test-auth/dashboard`,
          ASMT_LINK: `${DOMAIN}#/en/test-auth/item-set-editor/${item_set_id}`
        }
      });
    })

    await Promise.all(notifications);
    return {};
  }

  async extendItemSetAccess(uids: number[], group_id: number, role_type: DBD_U_ROLE_TYPES, expires_on: DBT.DATETIME) {
    const updateRoles = await this.app.service('db/write/user-roles').db()
    .update({ expires_on })
    .whereIn('uid', uids)
    .where({
        group_id,
        role_type,
        is_revoked: 0
    })

    return updateRoles
  }


  /**
   * make sure uid have role type in group id
   * @param uid 
   * @param group_id 
   * @param role_type 
   * @returns 
   */
  async ensureSystemRolePresent(uid:number, group_id:number, role_type:DBD_U_ROLE_TYPES){
    if(!uid || !group_id || !role_type){
      return // do nothing if uid or group id or role_type is not presented
    }
    const authSysRoleRecords = await dbRawRead(this.app, {uid, group_id, role_type}, `
      select ur.id
        from user_roles ur
       where ur.uid = :uid
         and ur.group_id = :group_id
         and ur.role_type = :role_type
         and ur.is_revoked = 0
    ;`)
    if (authSysRoleRecords.length === 0){
      await this.app.service('auth/user-role-actions').assignUserRoleToGroup({
        uid,
        group_id,
        role_type,
        created_by_uid: uid,
      });
    }
  }   

  async updateRoles(uids: number[], group_id: number, newRole: DBD_U_ROLE_TYPES, expires_on?: DBT.DATETIME ) {
    for(const uid of uids) {
      const newRoleTypes = this.getPrimaryGroupRoleTypes(newRole);

      //Find existing roles in the db
      const existingRoles = await this.app.service('db/write/user-roles')
      .db()
      .where('uid', uid)
      .where('group_id', group_id)
      .whereIn('role_type', newRoleTypes)
      .select();

      const existingRoleTypes = existingRoles.map( (r:any) => r.role_type);

      //Revoke all unnecessary author roles.
      await this.app
      .service('auth/user-role-actions')
      .revokeUserRolesFromGroupNotInRoleTypes(uid, group_id, existingRoleTypes);

      //Find the necessary roles that are revoked (Todo: also expired)
      const revokedExistingIds = existingRoles.filter((r:any) => r.is_revoked === 1).map((r:any) => r.id);

      const expiresOn = expires_on ? new Date(<string>expires_on) : null;
      //unrevoke them
      const unrevokeProms = revokedExistingIds.map ((id:number) => this.app.service('db/write/user-roles').patch(id, {is_revoked: 0, expires_on: expiresOn}));
      await Promise.all(unrevokeProms);

      //Find the necessary roles that don't exist
      const notExistingRoles = newRoleTypes.filter( r => !existingRoleTypes.includes(r));

      //create them
      const createProms = notExistingRoles.map( (r:DBD_U_ROLE_TYPES) => this.app.service('db/write/user-roles').create(
        {
          uid,
          group_id: group_id,
          role_type: r,
          expires_on: expiresOn
        }
      ) )

      await Promise.all(createProms);
      
      //make sure role: test_item_author, group_id: 24 is included in the user_roles for uid
      const base_group_id = await this.app.service('auth/user-role-actions').getSingularGroupId(DBD_U_GROUP_TYPES.mpt_test_controller); // group id 24
      const base_role_type = DBD_U_ROLE_TYPES.test_item_author
      if(base_group_id){
        await this.ensureSystemRolePresent(uid, base_group_id, base_role_type)
      }
    }
    return {};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {

    if(id == null) {
      throw new Errors.BadRequest();
    }

    const uid = +id;
    const {group_id, newRole, expires_on} = <any>data;

    if(expires_on) {
      return this.extendItemSetAccess([uid], group_id, newRole, expires_on);
    }

    return this.updateRoles([uid], group_id, newRole);
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query || !id) {
      throw new Errors.BadRequest();
    }
    const group_id = params.query.group_id;
    const isAssessment = params.query.isAssessment == 'true';
    const uid = +id;

    await this.app
    .service('auth/user-role-actions')
    .revokeUserFromGroup(uid, group_id);

    const db:Knex = this.app.get('knexClientRead');

    //May not have an auths entry, get both e-mails
    const res = await db('users as u').joinRaw('LEFT JOIN auths as a ON u.id = a.uid').where('u.id', uid).select('a.email', 'u.contact_email');
    const email = res[0].email || res[0].contact_email;

    const userId = await currentUid(this.app, params);
    if(isAssessment) {
      const asmt = await this.app.service('db/read/temp-question-set').db().where('single_group_id', group_id).select('id');
      const item_set_id = asmt[0].id;

      this.app.service('public/test-auth/notifications').createNotif({
        config: {
          notifType: NotifType.REVOKED_ASMT_ACCESS,
          itemId: item_set_id,
          uid: userId,
          email
        },
        forDevCoords: true
      }, params);
    } else {
      this.app.service('public/test-auth/notifications').createNotif({
        config: {
          notifType: NotifType.REVOKED_AUTH_GROUP_ACCESS,
          itemId: group_id,
          uid: userId,
          email
        },
        forDevCoords: true
      }, params);

      //Also revoke access to all assessments in this authoring group
      const assessments = await db('temp_question_set as qs').where('qs.group_id', group_id).whereNotNull('qs.single_group_id')
      const singleGroupIds = assessments.map( (a:any) => a.single_group_id);

      await this.app
      .service('auth/user-role-actions')
      .revokeUserFromGroups(uid, singleGroupIds);

      //create notification for each assessment revoked
      for(const asmt of assessments) {
        this.app.service('public/test-auth/notifications').createNotif({
          config: {
            notifType: NotifType.REVOKED_ASMT_ACCESS,
            itemId: asmt.id,
            uid: userId,
            email
          },
          forDevCoords: true
        }, params);
      }

    }

    return { id };
  }
}
