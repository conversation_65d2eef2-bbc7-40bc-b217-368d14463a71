// Initializes the `public/test-auth/suggestion-highlights` service on path `/public/test-auth/suggestion-highlights`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SuggestionHighlights } from './suggestion-highlights.class';
import hooks from './suggestion-highlights.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/suggestion-highlights': SuggestionHighlights & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/suggestion-highlights', new SuggestionHighlights(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/suggestion-highlights');

  service.hooks(hooks);
}
