import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { ENoteItemType } from '../notes/notes.class';

interface Data {}

interface ServiceOptions {}

export enum EHighlightType {
  HIGHLIGHT = 'HIGHLIGHT',
  COMMENT = 'COMMENT'
}


export class SuggestionHighlights implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {suggestion_id, entry_id, prop, start, end, comment} =  <any>data;

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    const type = comment ? EHighlightType.COMMENT : EHighlightType.HIGHLIGHT;

    const highlight = await this.app.service('db/write/suggestion-highlights').create({
      suggestion_id, entry_id, prop, start, end, created_by_uid: uid, type
    })

    /*
        const {
      text,
      overwrite_created_on, // correct order for imported comments
      parent_note_id,
      item_id,
      item_type,
      revision,
      has_child,
      is_pinned,
      assigned_uid = null,
    } = <any> data;
    */
    if(comment) {
      await this.app.service('public/test-auth/notes').create({
        text: comment,
        item_id: highlight.id,
        item_type: ENoteItemType.HIGHLIGHT,

      }, params);
    }
    return highlight;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
