import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { Knex } from 'knex';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';

interface Data { }

interface IPatchData {
  test_window_id: number;
  revoke_all?: boolean;
  student_uids?: number[];
  revoke_report?: boolean;
}

interface ServiceOptions {}

export class RevokeG9InstantReports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
   * Get ASRG records
   * @param testWindowId
   * @param studentUids 
   * @returns query results
   */
  async getAsrgRecords(testWindowId: number, studentUids: number[]) {
    const uids = studentUids.length > 0 ? studentUids : undefined;
    const results = await dbRawRead(this.app, { testWindowId, uids }, `
      SELECT asrg.id
        , asrg.student_uid
        , asrg.student_report_id
      FROM auto_student_report_generations asrg
      JOIN test_attempts ta ON ta.id = asrg.test_attempt_id
      JOIN test_window_td_alloc_rules twtar ON twtar.id = ta.twtdar_id
      WHERE twtar.test_window_id = :testWindowId
        AND asrg.is_revoked = 0
        -- AND asrg.student_report_id IS NOT NULL -- some asrg.student_report_id are null
        ${studentUids.length > 0 ? `AND asrg.student_uid IN (:uids)` : ''} 
      ;
    `);
    return results;
  }

  /**
   * Get ANSRG (Not fully participate students) records
   * @param testWindowId 
   * @param studentUids 
   * @returns 
   */
  async getAnsrgRecords(testWindowId: number, studentUids: number[]) {
    const uids = studentUids.length > 0 ? studentUids : undefined;
    const results = await dbRawRead(this.app, { testWindowId, uids }, `
      SELECT ansrg.id
        , ansrg.student_uid
      FROM auto_nfp_student_report_generations ansrg
      WHERE ansrg.test_window_id = :testWindowId
        AND ansrg.is_revoked = 0
        AND ansrg.student_pdf_generated_on IS NOT NULL
        ${studentUids.length > 0 ? `AND ansrg.student_uid IN (:uids)` : ''} 
      ;
    `);
    return results;
  }

  /**
   * Get BSRGR records
   * @param testWindowId
   * @param studentUids 
   * @returns query results
   */
  async getBsrgrRecords(testWindowId: number, studentUids: number[]) {
    const uids = studentUids.length > 0 ? studentUids : undefined;
    const results = await dbRawRead(this.app, { testWindowId, uids }, `
      SELECT DISTINCT bsrgr.id
      FROM auto_student_report_generations asrg
      JOIN test_attempts ta ON ta.id = asrg.test_attempt_id
      JOIN test_window_td_alloc_rules twtar ON twtar.id = ta.twtdar_id
      JOIN school_class_test_sessions scts ON scts.test_session_id = ta.test_session_id
      JOIN school_classes sc ON sc.id = scts.school_class_id
      JOIN bulk_student_results_g9_reports bsrgr ON bsrgr.class_group_id = sc.group_id
      WHERE twtar.test_window_id = :testWindowId
        AND asrg.is_revoked = 0
        AND asrg.student_pdf_generated_on IS NOT NULL
        ${studentUids.length > 0 ? `AND asrg.student_uid IN (:uids)` : ''} 
        AND bsrgr.is_revoked = 0
      ;
    `);

    // Get not fully participate students' bulk report records
    const nfpResults = await dbRawRead(this.app, { testWindowId, uids }, `
      SELECT DISTINCT bsrgr.id
      FROM bulk_student_results_g9_reports bsrgr
      JOIN auto_nfp_student_report_generations ansrg ON ansrg.schl_class_group_id = bsrgr.class_group_id
      WHERE ansrg.test_window_id = (:testWindowId)
        AND ansrg.is_revoked = 0
        AND ansrg.student_pdf_generated_on IS NOT NULL
      ${studentUids.length > 0 ? "AND ansrg.student_uid IN (:uids)" : "" }
        AND bsrgr.is_revoked != 1
    ;`)

    const combinedBsrgrIDs = results.concat(nfpResults)
    return combinedBsrgrIDs;
  }

  /**
   * Revoke student report records
   * @param studentReportIds
   * @param uid
   * @returns number of revoked records
   */
  async revokeStudentReports(studentReportIds: number[], uid: number) {
    if (studentReportIds.length > 0) {
      // log
      logger.info({
        created_by_uid: uid,
        slug: 'REVOKE_STUDENT_REPORTS',
        data: JSON.stringify({
          studentReportIds
        })
      });
      // revoke
      const knex: Knex = this.app.get('knexClientWrite');
      const count = await knex('student_reports')
        .whereIn('id', studentReportIds)
        .update({
          is_revoked: 1,
          revoked_by_uid: uid,
          revoked_on: dbDateNow(this.app),
        });
      return count;
    }
    return 0;
  }

  /**
   * Reset ASRG records
   * @param asrgIds
   * @returns number of reset records
   */
  async resetAsrgRecords(asrgIds: number[], uid: number, revoke_report:boolean = false, new_report_id?:number) {
    if (asrgIds.length > 0) {
      // log
      logger.info({
        created_by_uid: uid,
        slug: 'RESET_ASRG',
        data: JSON.stringify({
          asrgIds
        })
      });
      // reset
      let updateData:any = {
        student_pdf_generated_on: null,
        eqao_validated_on: null,
        validated_by_uid: null,
      }

      if (revoke_report) {
        updateData = {
          ...updateData,
          student_report_generated_on: null,
          student_report_id: null
        };
      }

      if (new_report_id) {
        updateData.student_report_id = new_report_id
      }

      const knex: Knex = this.app.get('knexClientWrite');
      const count = await knex('auto_student_report_generations')
        .whereIn('id', asrgIds)
        .update(updateData);
      return count;
    }
    return 0;
  }

  /**
   * Reset ANSRG (Not fully participated student PDF) records
   * @param ansrgIds
   * @returns number of reset records
   */
  async resetAnsrgRecords(ansrgIds: number[], uid: number) {
    if (ansrgIds.length > 0) {
      // log
      logger.info({
        created_by_uid: uid,
        slug: 'RESET_ANSRG',
        data: JSON.stringify({
          ansrgIds
        })
      });
      // reset
      const knex: Knex = this.app.get('knexClientWrite');
      const count = await knex('auto_nfp_student_report_generations')
        .whereIn('id', ansrgIds)
        .update({
          student_pdf_generated_on: null,
          eqao_validated_on: null,
          validated_by_uid: null,
        });
      return count;
    }
    return 0;
  }

  /**
   * Revoke BSRGR records
   * @param bsrgrIds
   * @param uid
   * @returns number of revoked records
   */
  async revokeBsrgrRecords(bsrgrIds: number[], uid: number) {
    if (bsrgrIds.length > 0) {
      // log
      logger.info({
        created_by_uid: uid,
        slug: 'REVOKE_BSRGR',
        data: JSON.stringify({
          bsrgrIds
        })
      });
      // revoke
      const knex: Knex = this.app.get('knexClientWrite');
      const count = await knex('bulk_student_results_g9_reports')
        .whereIn('id', bsrgrIds)
        .update({
          is_revoked: 1,
          revoked_by_uid: uid,
          revoked_on: dbDateNow(this.app),
        });
      return count;
    }
    return 0;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: IPatchData, params?: Params): Promise<Data> {
    if (!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    const { test_window_id, revoke_all, student_uids, revoke_report } = data;
    const revoked_by_uid = await currentUid(this.app, params);
    return this.revokeReports(revoked_by_uid, test_window_id, student_uids, revoke_report, revoke_all)
  }

  async revokeReports(revoked_by_uid:number, test_window_id:number, student_uids:any, revoke_all?:boolean, revoke_report?:boolean, new_report_id?:number) {

    // Get input
    const revokeAll = (revoke_all === true); // default false
    const studentUids = (student_uids && Array.isArray(student_uids)) ? student_uids : [];

    // Validate input
    if (!test_window_id) {
      throw new Errors.BadRequest('MISSING_TW_ID');
    }
    if (!revokeAll && studentUids.length === 0) {
      throw new Errors.BadRequest('MISSING_STU_UIDS');
    }
    if (revokeAll && studentUids.length !== 0) {
      throw new Errors.BadRequest('ERR_REVOKE_ALL_WITH_UIDS');
    }

    // Process

    const asrgRecords = await this.getAsrgRecords(test_window_id, studentUids);
    const ansrgRecords = await this.getAnsrgRecords(test_window_id, studentUids);
    const bsrgrRecords = await this.getBsrgrRecords(test_window_id, studentUids);

    const asrgIds = asrgRecords.map((asrg) => asrg.id);
    const ansrgIds = ansrgRecords.map((ansrg) => ansrg.id);
    const bsrgrIds = [...new Set(bsrgrRecords.map((bsrgr) => bsrgr.id))];

    let revokedSrCount = 0
    if(revoke_report){
      const studentReportIds = asrgRecords.map((asrg) => asrg.student_report_id);
      revokedSrCount = await this.revokeStudentReports(studentReportIds, revoked_by_uid);
    }

    const resetAsrgCount = await this.resetAsrgRecords(asrgIds, revoked_by_uid, revoke_report, new_report_id);
    const resetAnsrgCount = await this.resetAnsrgRecords(ansrgIds, revoked_by_uid);
    const revokedBsrgrCount = await this.revokeBsrgrRecords(bsrgrIds, revoked_by_uid);

    // Return counts
    return {
      revoked_student_report_count: revokedSrCount,
      reset_asrg_count: resetAsrgCount,
      reset_ansrg_count: resetAnsrgCount,
      revoked_bsrgr_count: revokedBsrgrCount,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
