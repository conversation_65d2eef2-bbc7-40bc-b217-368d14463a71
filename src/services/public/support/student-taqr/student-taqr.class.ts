import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class StudentTaqr implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id, limit} = params.query;
      return dbRawRead(this.app, {test_window_id}, `
        select taqr.id, taqr.is_nr, taqr.is_nr_checked 
        from test_window_td_alloc_rules twtar 
        join test_attempts ta on ta.twtdar_id = twtar.id 
        join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
        where twtar.test_window_id = :test_window_id
          and taqr.is_nr_checked = 0 
        limit 1000
      `)
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    
    if (!params?.query){
      throw new Errors.BadRequest();
    }

    const updated_by_uid = await currentUid(this.app, params)

    const { 
      test_attempt_id,
      test_question_id,
      response_raw,
      section_id,
      module_id,
      updated_on,
      note,
    } = <any> data;

    const subRecordResponsePayload = {
      response_raw,
      item_id: test_question_id
    }
    const subRecords: any = await this.app.service('public/test-ctrl/schools/student-attempts').processResponseRaw({
      response: subRecordResponsePayload,
      responseTypes: <{ [key: string]: boolean }>{}
    })
    const record = subRecords[0];
    const is_nr = record.no_response ? 1 : 0;
    const { response} = record;
    const { score, weight } = this.app
      .service("public/student/session-question")
      .aggregateScoreAndWeightFromResponseRaw(response_raw);

    // check for existing record before creating a new one
    const existingRecord = await dbRawRead(this.app, {test_question_id, test_attempt_id}, `
      select id
      from test_attempt_question_responses taqr
      where test_attempt_id = :test_attempt_id
        and test_question_id = :test_question_id
        and is_invalid  = 0
    `)
    if (existingRecord.length){
      throw new Errors.GeneralError('EXISTING_RECORD');
    }

    const newRecord = await this.app.service('db/write/test-attempt-question-responses').create({
      test_attempt_id,
      test_question_id,
      response_raw,
      is_nr,
      score,
      weight,
      response,
      updated_by_uid,
      section_id,
      module_id,
      created_on: updated_on,
      updated_on,
      invalidation_note: note,
    })

    const taqr_id = newRecord.id

    return {taqr_id};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    
    const {response_raw, isScoreOverrideAllowed, isResponseOverrideAllowed, isNROverrideAllowed} = <any> data;

    const isResponseRawOverride = !!response_raw;

    const taqr = await this.app.service('db/read/test-attempt-question-responses').get(<number> id)
    const subRecordResponsePayload = {
      response_raw: isResponseRawOverride ? response_raw : taqr.response_raw, // holding on to response raw override to the end
      item_id: taqr.test_question_id
    }
    const subRecords: any = await this.app.service('public/test-ctrl/schools/student-attempts').processResponseRaw({
      response: subRecordResponsePayload,
      responseTypes: <{ [key: string]: boolean }>{}
    })
    const record = subRecords[0];
    const is_nr = record.no_response ? 1 : 0;
    const { score, weight } = this.app
      .service("public/student/session-question")
      .aggregateScoreAndWeightFromResponseRaw(isResponseRawOverride ? response_raw : taqr.response_raw);

    const changes:{id: NullableId, prop: string, before:string|number, after:string|number}[] = [];
    const patchPayload:any = { };
    const applyChange = (prop: string, before:string|number, after:string|number, isActive:boolean, forceChange:boolean=false) => {
      if (isActive){
        if (forceChange || before != after){
          changes.push({id, prop, before, after})
          patchPayload[prop] = after;
          if (prop === 'is_nr'){
            patchPayload.is_nr_checked = 1;
          }
        }
      }
    }

    applyChange('score',         taqr.score,         score,                isScoreOverrideAllowed)
    applyChange('weight',        taqr.weight,        weight,               isScoreOverrideAllowed)
    applyChange('response',      taqr.response,      record.response,      isResponseOverrideAllowed)
    applyChange('is_nr',         taqr.is_nr,         is_nr,                isNROverrideAllowed,  taqr.is_nr_checked==0)
    applyChange('response_raw',  taqr.response_raw,  response_raw,         isResponseRawOverride )

    if (changes.length){
      await this.app.service('db/write/test-attempt-question-responses').patch(id, patchPayload)
    }
    return <any> changes;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
