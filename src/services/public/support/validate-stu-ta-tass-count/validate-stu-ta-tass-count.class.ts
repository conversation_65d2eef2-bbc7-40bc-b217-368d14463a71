import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
interface Data {}

interface ServiceOptions {}

export class ValidateStuTaTassCount implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Check if there's following error in the test window
   * 1) duplicate test attempts in same test session
   * 2) test attempt subsessions counts/null test attempt sub session
   * 3) duplicate "started" operational (twtdar.is_secured = 1) test attempt in test window
   * @param params.query test_window_id as input
   * @returns 
   */
  async find (params?: Params): Promise<any> {
    if (!params || !params.query){
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const { test_window_id } = params.query;

    // return empty array if twId is empty
    if (!test_window_id){
      return []
    }
    
    //Check if any student have duplicate test attempts in same test session
    const duplicateTestAttempts = await this.checkDuplicateTestAttempts(+test_window_id)

    //Check test attempt subsessions counts/null test attempt sub session
    const testAttemptSubSessionsCountError = await this.checktestAttemptSubSessionsCount(+test_window_id)
    

    //Check duplicate "started" operational (twtdar.is_secured = 1) test attempt in test window
    const duplicateTAsInTestWindow = await this.checkDuplicateTAsInTestWindow(+test_window_id)

    //Check unlinked test attempt in test window
    const unlinkedTestAttempts = await this.checkUnlinkedTestAttempts(+test_window_id)

    return {
      duplicateTestAttempts,
      testAttemptSubSessionsCountError,
      duplicateTAsInTestWindow,
      unlinkedTestAttempts
    }
  }


  /**
   * Check if any student have duplicate test attempts in same test session for the input test window
   * @param test_window_id 
   * @returns array of student's record with duplicate test attempts
   */
  async checkDuplicateTestAttempts(test_window_id: number){
    const duplicateTestAttempts = await dbRawRead(this.app, {test_window_id}, `
             select tw.id as test_window_id
                  , schl.foreign_id as school_mident
                  , sc.access_code as class_access_code
                  , ts.id as test_session_id
                  , scts.slug as scts_slug
                  , ta.uid as student_uid
                  , ta.twtdar_order
                  , count(distinct ta.id) ta_counts
                  , group_concat(distinct ta.id) as ta_ids
               from test_windows tw
               join test_sessions ts on ts.test_window_id = tw.id and ts.is_cancelled = 0
               join school_class_test_sessions scts on scts.test_session_id = ts.id
               join school_classes sc on sc.id = scts.school_class_id
               join schools schl on schl.group_id = sc.schl_group_id
               join test_attempts ta on ta.test_session_id = ts.id and ta.is_invalid = 0
              where tw.id = :test_window_id
           group by ta.test_session_id, ta.uid, ta.twtdar_order
             having ta_counts > 1
    ;`)

    return duplicateTestAttempts;
  }
 
  /**
   * Check test attempt subsessions counts/null test attempt sub session
   * @param test_window_id 
   * @returns array of student's record with null test attempt sub session
   */
  async checktestAttemptSubSessionsCount(test_window_id: number){
    const testAttemptSubSessionsCountError = await dbRawRead(this.app, {test_window_id}, `
         select * from
        (select tw.id as test_window_id
                , schl.foreign_id as school_mident
                , sc.access_code as class_access_code
                , ts.id as test_session_id
                , scts.slug as scts_slug
                , ta.uid as student_uid
                , ta.twtdar_order
                , ta.id ta_id
                , tsss.id as tsss_id
                , tass.id as tass_id
                , count(distinct tass.id) as tass_count
                , group_concat(distinct tass.id) as tass_ids
            from test_windows tw
            join test_sessions ts on ts.test_window_id = tw.id and ts.is_cancelled = 0
            join school_class_test_sessions scts on scts.test_session_id = ts.id
            join school_classes sc on sc.id = scts.school_class_id
            join schools schl on schl.group_id = sc.schl_group_id
            join test_session_sub_sessions tsss on tsss.test_session_id = ts.id
            join test_attempts ta on ta.test_session_id = ts.id and ta.is_invalid = 0 and ta.twtdar_order = tsss.twtdar_order
       left join test_attempt_sub_sessions tass on tass.test_attempt_id = ta.id and tass.sub_session_id = tsss.id and tass.uid = ta.uid
           where tw.id = :test_window_id
        group by tsss.id, ta.uid
      ) a 
      where a.tass_count != 1
    ;`)
    return testAttemptSubSessionsCountError;
  }

  /**
   * Check duplicate "started" operational (twtdar.is_secured = 1) test attempt in test window
   * @param test_window_id 
   * @returns array of student's record with duplicate "started" operational (twtdar.is_secured = 1) test attempt in test window
   */
  async checkDuplicateTAsInTestWindow(test_window_id: number){
    const duplicateTAsInTestWindow = await dbRawRead(this.app, {test_window_id}, `
             select tw.id as test_window_id
                  , schl.foreign_id as school_mident
                  , sc.access_code as class_access_code
                  , group_concat(distinct ts.id) as test_session_ids
                  , scts.slug as scts_slug
                  , ta.uid as student_uid
                  , ta.twtdar_order
                  , count(distinct ta.id) ta_counts
                  , group_concat(distinct ta.id) as ta_ids
               from test_windows tw
               join test_sessions ts on ts.test_window_id = tw.id
               join school_class_test_sessions scts on scts.test_session_id = ts.id
               join school_classes sc on sc.id = scts.school_class_id
               join schools schl on schl.group_id = sc.schl_group_id
               join test_attempts ta on ta.test_session_id = ts.id and ta.is_invalid = 0 and ta.started_on is not null
               join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and taqr.is_invalid = 0
               join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.is_secured = 1
              where tw.id = :test_window_id
           group by ts.test_window_id, scts_slug, ta.uid, ta.twtdar_order
             having ta_counts > 1
    ;`)

    return duplicateTAsInTestWindow;
  }

  /**
   * Check unlinked test attempt in test window
   * @param test_window_id 
   * @returns array of student's record with unlinked test attempt in test window
   */
  async checkUnlinkedTestAttempts(test_window_id: number){
    const unlinkedTestAttempts = await dbRawRead(this.app, {test_window_id}, `
       select tw.id as test_window_id
            , schl.foreign_id as school_mident
            , sd.is_sample as is_sample_schl
            , sc.access_code as class_access_code
            , ts.id as test_session_id
            , scts.slug as scts_slug
            , ta.uid as student_uid
            , ta.twtdar_order
            , ta.id as ta_id
            , ta.started_on as ta_started_on
            , count(distinct taqr.id) as taqr_count
         from test_windows tw
         join test_sessions ts on ts.test_window_id = tw.id 
         join school_class_test_sessions scts on scts.test_session_id = ts.id
         join school_classes sc on sc.id = scts.school_class_id
         join schools schl on schl.group_id = sc.schl_group_id
         join school_districts sd on sd.group_id = sc.schl_dist_group_id
         join test_attempts ta on ta.test_session_id  = -ts.id and ta.started_on is not null
         join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
        where tw.id = :test_window_id
          and ta.uid < 0
     group by ta.id
       having taqr_count > 0
    ;`)
    return unlinkedTestAttempts
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
