import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {
  id?: number,
  start_timestamp: number,
  end_timestamp: number,
  start_date: string,
  end_date: string,
  log_group_name: string,
  s3_bucket: string,
  s3_key_raw: string,
  task_id?: string,
  created_on?: string,
  raw_created_on?: string,
  is_processed?: number,
  processed_on?: string,
}

interface CreateData {
  start_date: string;
  end_date: string;
  log_group_name: string;
  s3_bucket: string;
  s3_prefix?: string;
  interval?: number;
}

interface PatchData {
  s3_key_raw?: string;
  task_id?: string;
  is_processed?: number;
}

interface QueueData {
  log_group_name: string,
  s3_bucket: string,
  interval: number,
}

interface ServiceOptions {}

const DEFAULT_RAW_PREFIX = 'raw_logs/';

enum GetTask {
  GET_NEXT_EXPORT = 'next_export',
  GET_UNPROCESSED_EXPORT = 'next_unprocessed',
  GET_FAILED = 'failed',
  GET_RECENT = 'all_recent',
}

enum PatchTask {
  START_RAW = 'start_raw',
  MARK_PROCESSED = 'mark_processed',
}

export class ProcessData implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('Missing parameters')
    }

    const mode = params.query.mode;
    const limit: number = Number(params.query.limit);
    if (!limit){
      throw new Errors.BadRequest('Missing required parameter "limit"');
    }
    if (mode == GetTask.GET_NEXT_EXPORT) {
      return dbRawRead(this.app, [limit], `
        SELECT id, start_timestamp, end_timestamp, log_group_name, s3_bucket, s3_key_raw, task_id
        FROM process_data_logs_exports_log
        WHERE raw_created_on IS NULL
          AND end_date < NOW()
        ORDER BY id ASC
        LIMIT ?
      `);
    }
    else if (mode == GetTask.GET_UNPROCESSED_EXPORT) {
      return dbRawRead(this.app, [limit], `
        SELECT id, start_timestamp, end_timestamp, log_group_name, s3_bucket, s3_key_raw, task_id
        FROM process_data_logs_exports_log
        WHERE raw_created_on IS NOT NULL
          AND is_processed = 0
        ORDER BY id ASC
        LIMIT ?
      `);
    }
    else if (mode == GetTask.GET_FAILED) {
      return dbRawRead(this.app, [limit], `
        SELECT *
        FROM process_data_logs_exports_log
        WHERE is_processed < 0
        ORDER BY id DESC
        LIMIT ?
      `);
    }
    else if (mode == GetTask.GET_RECENT) {
      return dbRawRead(this.app, [limit], `
        SELECT *
        FROM process_data_logs_exports_log
        ORDER BY id DESC
        LIMIT ?
      `);
    }

    throw new Errors.BadRequest(`Unrecognized mode ${mode}`)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: CreateData, params?: Params): Promise<Data> {
    if (params?.query?.method === 'enqueue') {
      return await this.queueNext(<QueueData> data);
    }

    let {
      start_date,
      end_date,
      log_group_name,
      s3_bucket,
      s3_prefix,
    } = <any> data;

    const date_regex = /^\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}(:\d{2}(.\d{3})?)?Z?$/;
    if (!date_regex.test(start_date) || !date_regex.test(end_date)) {
      throw new Errors.BadRequest(`'start_date' and 'end_date' fields must be in format "yyyy-mm-dd hh:mm:ss" or "yyyy-mm-ddThh:mm:ss"`);
    }

    s3_prefix = s3_prefix ?? DEFAULT_RAW_PREFIX;

    // Force UTC timezone if not specified
    start_date = (start_date.endsWith('Z')) ? start_date : start_date + 'Z';
    end_date = (end_date.endsWith('Z')) ? end_date : end_date + 'Z';

    const start_timestamp = Number(new Date(start_date)); // Converting to number gives milliseconds since epoch as required by AWS
    const end_timestamp = Number(new Date(end_date))

    // Enforce matching format for DB insert
    start_date = new Date(Number(start_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;
    end_date = new Date(Number(end_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;

    return await this.insertTask({
      start_timestamp,
      end_timestamp,
      start_date,
      end_date,
      log_group_name,
      s3_bucket,
      s3_key_raw: s3_prefix,
    });

  }

  async insertTask(data: Data) {
    const scheduled = await this.app.service('db/write/process-data-logs-exports-log').create(data)

    return scheduled;
  }

  async queueNext({log_group_name, s3_bucket, interval}: QueueData) {
    if (!log_group_name || !s3_bucket || !interval ) {
      throw new Errors.BadRequest('Invalid payload for "enqueue" method');
    }

    // Find latest for the log_group by the end_timestamp

    let query = `
    SELECT * FROM process_data_logs_exports_log
    WHERE log_group_name = :log_group_name
    ORDER BY end_timestamp DESC
    LIMIT 1
    `;
    const latest = await dbRawRead(this.app, {log_group_name}, query)

    if (latest.length == 0) {
      throw new Errors.Unprocessable(`No existing extractions for log group '${log_group_name}'`)
    }

    // if it is not yet extracted, then do nothing
    if (!latest[0].raw_created_on) {
      // Just returning the end of the queue
      return latest[0];
    }

    // otherwise create the new record from the latest one
    const start_timestamp = latest[0].end_timestamp;
    // Note: the interval in minutes is configured via the EventBridge rule that triggers the lambda
    const end_timestamp = start_timestamp + interval * 60 * 1000;

    const start_date = new Date(Number(start_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;
    const end_date = new Date(Number(end_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;

    const newTask = {
      start_timestamp,
      end_timestamp,
      start_date,
      end_date,
      log_group_name,
      s3_bucket,  // NOTE: this is passed from the client, but could also just be the value from the previous record.
      s3_key_raw: DEFAULT_RAW_PREFIX,
    }
    return await this.insertTask(newTask)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: PatchData, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    let action = params?.query?.action;
    if (!action) {
      throw new Errors.BadRequest('Missing "action" parameter');
    }

    let logId = Number(id);
    if (!logId) {
      throw new Errors.BadRequest('Missing id to patch');
    }

    if (action === PatchTask.START_RAW) {
      return await this.patchStartRaw(logId, data)
    }

    if (action === PatchTask.MARK_PROCESSED) {
      return await this.patchMarkProcessed(logId, data)
    }

    throw new Errors.BadRequest('Invalid action')
  }

  async patchStartRaw(id: number, data: PatchData) {
    const { s3_key_raw, task_id } = data;

    if (!s3_key_raw || !task_id ) {
      throw new Errors.BadRequest('Missing required fields for updating start of raw log extract');
    }

    return await this.app.service('db/write/process-data-logs-exports-log').patch(id, {
      s3_key_raw,
      task_id,
      raw_created_on: dbDateNow(this.app),
    });

    // TODO: checking to queue the next could be called here rather than from the calling lambda.
  }

  async patchMarkProcessed(id: number, data: PatchData) {
    const { is_processed } = data;

    if (!(is_processed === 1 || is_processed === -1 || is_processed === 0)) {
      throw new Errors.BadRequest('Invalid value for is_processed');
    }

    return await this.app.service('db/write/process-data-logs-exports-log').patch(id, {
      is_processed,
      processed_on: dbDateNow(this.app),
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
