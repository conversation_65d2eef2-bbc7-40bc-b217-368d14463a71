import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { AxiosInstance } from 'axios';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { TypeConvertSourceType, TypeConvertTargetType, convertTypes } from '../../../../util/type-convert';


interface Data {}

interface ServiceOptions {}

// const sessionToTranslationSlug: {[key: string]: string} = {
//   'SESSION_A': 'pj_lang_session_A',
//   'SESSION_B': 'pj_lang_session_B',
//   'SESSION_C': 'pj_lang_session_C',
//   'SESSION_DW': 'pj_lang_session_D',
//   'SESSION_DR': 'pj_lang_session_D',
// }

export interface IGenRespSheetConfig {
  testSessionId: number,
  classroomId: number,
  schl_class_group_id?: number,
  students?: { uid: string | number, slugs: string[] }[],
  asmtSlug: string,
  isIndividual?: boolean | string,
  isSasn?: number
}

export class GenResponseSheets implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = app.get('eqaoScanningService');
  }

  async generatePdf(studentData: any, isIndividual: boolean, isSasn: boolean) {
    const res = await this.eqaoScanningService.post(`/generate`, { studentData, isIndividual, isSasn });
    if (isIndividual) {
      const studentPdfs = res.data.student_sheets;
      return [{ studentPdfs }];
    } else {
      const responsePdf = res.data.base64;
      return [{ responsePdf }];
    }
  }

  public renderStudentInfoQuery(testSessionId: number, asmtSlug: string, isSasn: number) {
    return `
        SELECT us.id as uid
        , us.first_name 
        , us.last_name
        , schl.foreign_id as school_mident
        , sc.name as class_name
        , tw.date_start as school_year_start
        , tw.date_end as school_year_end
        , um_oen.value as 'student_OEN'
        , um_sasn.value as 'student_SASN'
        , case when schl.is_sasn_login or um_oen.value = '000000000' then 1
                else 0
          end as is_sasn
        , tw.id as test_window_id
        , twtdar.slug as assessment_type
        , twtdar.user_metas_filter
        , ta.test_session_id as test_session_id
      from users us 
      join user_roles ur on ur.uid = us.id and ur.is_revoked != 1 and ur.role_type = 'schl_student'
      join user_metas um_oen on um_oen.uid = ur.uid and um_oen.key_namespace = 'eqao_sdc' and um_oen.key = 'StudentOEN'
 left join user_metas um_sasn on um_sasn.uid = ur.uid and um_sasn.key_namespace = 'eqao_sdc' and um_sasn.key = 'SASN'
      join school_classes sc on sc.group_id = ur.group_id and sc.is_active = 1
      join school_semesters ss on ss.id = sc.semester_id 
      join test_attempts ta on ta.uid = us.id and ta.test_session_id = ${testSessionId}
      join test_windows tw on tw.id = ss.test_window_id and tw.is_active = 1
      join test_window_td_alloc_rules twtdar on twtdar.test_window_id = tw.id and twtdar.id = ta.twtdar_id 
      join schools schl on schl.group_id = sc.schl_group_id 
      where us.id in (?)
      and ta.is_invalid = 0
      and twtdar.type_slug = '${asmtSlug}'
      and twtdar.slug like '%lang_op%' -- only lang op assessments have response sheets
    ;`
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async getTranslation(questionSlug: string, umFilter: string) {
    const translation = this.app.service('public/translation');

    const lang = JSON.parse(umFilter)['eqao_dyn.Lang'][0];
    const slug = await convertTypes(this.app, TypeConvertSourceType.SessionTitle, TypeConvertTargetType.TranslationSlug, questionSlug);
    //const slug = sessionToTranslationSlug[questionSlug];

    return await translation.getOneBySlug(slug, lang);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (testSessionId: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!params) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    if (!data) throw new Errors.BadRequest('ERR_MISSING_DATA');
    const uid = await currentUid(this.app, params);

    const {testSessionId, students, asmtSlug, isIndividual, isSasn} = <IGenRespSheetConfig>data;
    
    if (!testSessionId || !students || !asmtSlug || isIndividual == undefined || isSasn == undefined) {
      throw new Errors.GeneralError('ERR_MISSING_PARAMS');
    }

    const studentUids = students.map((stu) => stu.uid);
    const studentDataUnsorted = await dbRawRead(this.app, [studentUids], this.renderStudentInfoQuery(testSessionId, asmtSlug, +isSasn));

    const studentData: any[] = [];
    students.forEach((student: any) => {
      const studentResult = studentDataUnsorted.find((stu) => +stu.uid === +student.uid);
      studentData.push({
        ...studentResult,
        slugs: student.slugs
      });
    })

    let pdf_gen_data: any = [];
    for (const student of studentData) {
      const yearStart = new Date(student.school_year_start).getFullYear();
      const yearEnd = new Date(student.school_year_end).getFullYear();
      student['school_year_start'] = yearStart;
      student['school_year_end'] = yearEnd;
      const sessionTitles = student.slugs as string[];
      await Promise.all(sessionTitles.map(async (title, i) => {
        const temp_data = {
          ...student,
            session_title: await this.getTranslation(title, student.user_metas_filter),
            question_slug: sessionTitles[i],
            full_name: `${student['first_name']} ${student['last_name']}`
        };
        pdf_gen_data.push(temp_data);
      }))
    }

    pdf_gen_data.forEach((student: any) => {
      student.first_name = student.first_name.toUpperCase();
      student.last_name = student.last_name.toUpperCase();
      student.class_name = student.class_name.toUpperCase();
      student.full_name = student.full_name.toUpperCase();
    })

    const sessionARec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_A');
    const sessionBRec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_B');
    const sessionCRec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_C');
    //const sessionDRec = pdf_gen_data.filter((stu: any) => stu.question_slug.includes('SESSION_D'));
    const sessionDRec = pdf_gen_data.filter((stu: any) => ['SESSION_DR', 'SESSION_DW'].includes(stu.question_slug));

    const gen_data = [...sessionARec, ...sessionBRec, ...sessionCRec, ...sessionDRec]

    const pdfData = await this.generatePdf(gen_data, !!isIndividual, +isSasn === 1);
    return pdfData;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
