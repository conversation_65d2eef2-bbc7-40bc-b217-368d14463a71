export enum SchoolBoardView {
    TECH_READI = 'tech_readiness',
    //CONTACT = 'contact',
    SECURITY = 'security',
    SESSIONS = 'sessions',
    REPORTS = "reports"
  }

  export const SCHOOL_BOARD_VIEWS = [
    {
      id:SchoolBoardView.TECH_READI, 
      caption: ('lbl_network_readiness'), //'Technical Readiness',
      imgUrl: 'https://eqao.vretta.com/authoring/user_uploads/21/authoring/clipboard/1602235150476/clipboard.png',
      description: ('txt_tech_readi'), //'',
      hasIndicator: true,
    },
    // {
    //   id:SchoolBoardView.CONTACT, 
    //   caption: ('lbl_contact_info'), //'Class Sections',
    //   imgUrl: 'https://eqao.vretta.com/authoring/user_uploads/21/authoring/school/*************/school.png',
    //   description: ('txt_sa_classrooms_info_1'), //'You can review and manage the classes as they progress with their introductory materials and administration of the assessment.',
    // },
    {
      id:SchoolBoardView.SECURITY, 
      caption:('lbl_security_info'), //'Teachers',
      imgUrl: 'https://eqao.vretta.com/authoring/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
      description: ('txt_teachers'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
    },
    {
      id:SchoolBoardView.SESSIONS, 
      caption:('lbl_monitoring'), //'Teachers',
      imgUrl: 'https://eqao.vretta.com/authoring/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
      description: ('txt_teachers'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
    },
    {
      id:SchoolBoardView.REPORTS, 
      caption:('lbl_reports'), 
      imgUrl: 'https://eqao.vretta.com/authoring/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
      description: ('txt_teachers'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
    }
]