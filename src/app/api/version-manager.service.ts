import { Injectable } from '@angular/core';
import { LoginGuardService } from './login-guard.service';
import { AuthService } from './auth.service';
import { RoutesService } from './routes.service';
import { APP_VERSION } from 'src/version';
import { LangService } from '../core/lang.service';

interface IGetResponse {
  isValid: boolean, 
  expectedVersion: string
}

@Injectable({
  providedIn: 'root'
})
export class VersionManagerService {

  constructor(
    private loginGuard: LoginGuardService,
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
  ) { }

  checkVersion(client_app_version = APP_VERSION): void {
    this.auth.apiGet(this.routes.VERSION_MANAGEMENT, client_app_version).then((res:IGetResponse) =>{
      if(!res.isValid){
        this.loginGuard.quickPopup(this.lang.tra('QUICK_POP_VERSION_INVALID'));
      }
      console.log('Required Client Version: ', res.expectedVersion)
    }).catch(() => {
        this.loginGuard.quickPopup(this.lang.tra('QUICK_POP_VERSION_INVALID'));
    })
  }
}
