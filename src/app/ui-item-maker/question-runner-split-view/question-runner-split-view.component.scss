.split-question-runner {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.question-container-split-view {
    height: 100%;
    display: flex;
    justify-content: space-around;
}

.question-runner-left,
.question-runner-right {
    flex: 1;
    box-sizing: border-box;
    overflow: auto;
    height: 35rem;
}

.question-runner-left {
    border-right: solid #888;
}

.divider-container {
    display: flex;
    margin-top: 1em;
}

.divider {
    display: flex;
    align-items: center;
    flex: 1;
    flex-direction: column;
}
