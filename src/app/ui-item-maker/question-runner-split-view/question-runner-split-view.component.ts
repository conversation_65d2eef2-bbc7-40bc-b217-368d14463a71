import { Component, Input, OnInit } from '@angular/core';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { IQuestionConfig } from '../item-set-editor/models';

@Component({
  selector: 'question-runner-split-view',
  templateUrl: './question-runner-split-view.component.html',
  styleUrls: ['./question-runner-split-view.component.scss']
})
export class QuestionRunnerSplitViewComponent implements OnInit {

  @Input() currentQuestionLeft!: IQuestionConfig;
  @Input() questionStateLeft!: any;
  @Input() langLeft?: string;
  @Input() labelLeft?: string;
  
  @Input() currentQuestionRight!: IQuestionConfig;
  @Input() questionStateRight!: any;
  @Input() langRight?: string;
  @Input() labelRight?: string;
  
  @Input() isLangLocked:boolean;
  @Input() itemBankCtrl: ItemBankCtrl;
  @Input() itemEditCtrl: ItemEditCtrl;
  @Input() printViewCtrl: ItemSetPrintViewCtrl;

  splitScreenZoom = {left: 65, right: 65};

  constructor(
    public itemComponentEdit: ItemComponentEditService,
  ) { }

  ngOnInit(): void {
  }

  getZoomFactor(zoom:number):string{
    return `${zoom}%`
  }

}
