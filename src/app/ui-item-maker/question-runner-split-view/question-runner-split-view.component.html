<span>
    <div class="divider-container">
      <div class="divider">
        <div class="label"><tra [slug]="labelLeft"></tra></div>
        <div class="input-container">
          {{splitScreenZoom.left}}%
          <input type="range" min="10" max="200" [(ngModel)]="splitScreenZoom.left">
          <i class="fas fa-search"></i>
        </div>
        
      </div>
      <div class="divider">
        <div class="label"><tra [slug]="labelRight"></tra></div>
        <div class="input-container">
          {{splitScreenZoom.right}}%
          <input type="range" min = '10' max = '200' [(ngModel)]="splitScreenZoom.right">
          <i class="fas fa-search"></i>
        </div>
        
      </div>
    </div>
      <div *ngIf="isLangLocked" class="question-container-split-view">
        <div class="question-runner-left"  >
          <span [style.zoom]="getZoomFactor(splitScreenZoom.left)">
            <question-runner-lang-locked
            [langCode]="langLeft"
            [currentQuestion]="currentQuestionLeft" 
            [questionState]="questionStateLeft"
            [isSubmitted]="itemEditCtrl.isLocked"
            [isPrintMode]="printViewCtrl.isResultsPrint"
            [selectedEditEntry]="itemComponentEdit.selectedEntry"
            ></question-runner-lang-locked>
          </span>
        </div>
        <div class="question-runner-right">
          <span [style.zoom]="getZoomFactor(splitScreenZoom.right)">
            <question-runner-lang-locked
              [langCode]="langRight"
              [currentQuestion]="currentQuestionRight" 
              [questionState]="questionStateRight"
              [isSubmitted]="itemEditCtrl.isLocked"
              [isPrintMode]="printViewCtrl.isResultsPrint"
              [selectedEditEntry]="itemComponentEdit.selectedEntry"
            ></question-runner-lang-locked>
          </span>
        </div>
      </div>
      <div *ngIf="!isLangLocked" class="question-container-split-view">
        <div class="question-runner-left"  >
          <span [style.zoom]="getZoomFactor(splitScreenZoom.left)">
            <question-runner
            [currentQuestion]="currentQuestionLeft" 
            [questionState]="questionStateLeft"
            [isSubmitted]="itemEditCtrl.isLocked"
            [isPrintMode]="printViewCtrl.isResultsPrint"
            [selectedEditEntry]="itemComponentEdit.selectedEntry"
            ></question-runner>
          </span>
        </div>
        <div class="question-runner-right">
          <span [style.zoom]="getZoomFactor(splitScreenZoom.right)">
            <question-runner
            [currentQuestion]="currentQuestionRight" 
            [questionState]="questionStateRight"
            [isSubmitted]="itemEditCtrl.isLocked"
            [isPrintMode]="printViewCtrl.isResultsPrint"
            [selectedEditEntry]="itemComponentEdit.selectedEntry"
            ></question-runner>
          </span>
        </div>
      </div>
  </span>
