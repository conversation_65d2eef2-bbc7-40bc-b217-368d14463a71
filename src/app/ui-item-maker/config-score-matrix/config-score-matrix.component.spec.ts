import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfigScoreMatrixComponent } from './config-score-matrix.component';

describe('ConfigScoreMatrixComponent', () => {
  let component: ConfigScoreMatrixComponent;
  let fixture: ComponentFixture<ConfigScoreMatrixComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ConfigScoreMatrixComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConfigScoreMatrixComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
