import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AuthService, getFrontendDomain } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { UserRoles } from 'src/app/api/models/roles';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { AuthRolesService } from '../auth-roles.service';
import { ItemMakerService } from '../item-maker.service';
import * as moment from 'moment-timezone';
import { isEmailValid } from '../../core/util/validation';


interface INewAccountInfo {
  firstName?: string,
  lastName?: string,
  email?: string | string[],
  role_type?: string,
  isAutoEmail?: boolean,
  domain?:string,
  langCode?:string,
  isAssessment: boolean,
  expires_on?: Date
}

@Component({
  selector: 'share-access',
  templateUrl: './share-access.component.html',
  styleUrls: ['./share-access.component.scss']
})
export class ShareAccessComponent implements OnInit {


  public authRoleTypes;
  public testAuthorCreateError;
  public isTestAuthorCreateSaving: boolean = false;


  testAuthorCreateForm = {
    emails: new FormControl(),
    group_id: new FormControl(),
    role_type: new FormControl(),
    expires_on: new FormControl()
  };

  @Input() authGroupId;
  @Input() singleGroupId; 
  @Output() doneCreating = new EventEmitter(); 
  @Input() excludeEmails: {email: string, role: UserRoles}[]

  public isTemporary:boolean = false;
  public expiresOn: Date;

  constructor(private authRoles: AuthRolesService,
    public myItems: ItemMakerService,
    private auth: AuthService,
    private lang: LangService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService
    ) { }

  ngOnInit(): void {
    this.authRoleTypes = this.authRoles.getAuthRoles();
    this.myItems.refreshGroupsAsSuper();
  }

  createTestAuthor() {
    this.testAuthorCreateError = null;
    const payload:any = {};
    const errors = [];
    Object.keys(this.testAuthorCreateForm).forEach(key => {
      const val = this.testAuthorCreateForm[key].value;
      if(key === 'group_id') {
        payload[key] = this.singleGroupId || this.authGroupId || val;
      } else {
        payload[key] = val;
      }
      if (!payload[key]) {
        switch (key) {
          case 'emails': errors.push('Email(s)'); break;
          case 'group_id': errors.push(this.lang.tra('test_auth_authoring_group')); break;
          case 'role_type': errors.push('Role'); break;
          case 'expires_on': 
            if(this.isTemporary) {
              errors.push('Expires on'); 
            }
          break;
        }
      }
    });

    if (errors.length > 0) {
      this.testAuthorCreateError = `${this.lang.tra('required_fields_error')}: ${errors.join(', ')}`;
    } else {

      let emails = payload.emails.split(',').map(e => e.trim());
      const excludeEmailObjs = this.excludeEmails || [];
      const excludeEmails = excludeEmailObjs.map(e => e.email);
      const categorizedEmails = this.categorizeEmails(emails, excludeEmails)

      const expires_on_utc = payload.expires_on ? moment(payload.expires_on).utc().format("YYYY-MM-DD HH:mm") : null;
      const newAcctInfo:INewAccountInfo = {
        email: categorizedEmails.acceptedEmails,
        role_type: payload.role_type,
        isAutoEmail: true,
        isAssessment: !!this.singleGroupId,
        expires_on: expires_on_utc
      }

      const message = this.generateMessage(categorizedEmails)

      this.loginGuard.confirmationReqActivate({
        caption: message,
        confirm: !categorizedEmails.acceptedEmails.length ? undefined : () => {
          this.createAccount(newAcctInfo, payload.group_id)
              .then(() => {
                this.isTestAuthorCreateSaving = true;
                this.doneCreating.emit();
              });
        },
        close: () => {
          this.isTestAuthorCreateSaving = false;
        }
      })
    }
  }

  createAccount(newAcctInfo: INewAccountInfo, group_id: number) {
    if (newAcctInfo.isAutoEmail) {
      newAcctInfo.domain = getFrontendDomain();
      newAcctInfo.langCode = this.lang.c();
    }
    const perms = {
      query:{
        auth_group_ids: this.singleGroupId? [group_id, this.authGroupId] : group_id,
      }
    };
    return this.auth
        .apiCreate(
            this.routes.TEST_AUTH_ACCOUNTS_ACCESS,
            {
              ... newAcctInfo,
              domain: window.location.origin + '/',
              group_id
            },
            perms
        );
  }

  /**
   * Categorize and filter raw emails in 3 categories: invalid, excluded, filtered
   * @param emails All email(s) inputted by user
   * @param emailsToExclude Emails that already have access granted
   * @returns { Object } { acceptedEmails, excludedEmails, invalidEmails }
   */
  categorizeEmails(emails:string[], emailsToExclude:string[]){
    
    let acceptedEmails:string[] = new Array();
    let excludedEmails:string[] = new Array();
    let invalidEmails:string[] = new Array();

    emails.forEach((email:string) => {
      if(!isEmailValid(email)) {
        invalidEmails.push(email)
      }else if (!emailsToExclude.includes(email)) {
        if(!acceptedEmails.includes(email)){
          acceptedEmails.push(email)
        }
      }else {
        excludedEmails.push(email)
      }
    })

    return {
      acceptedEmails,
      excludedEmails,
      invalidEmails
    }
  }

  /**
   * Tranform array of emails into text format
   * @param emails Array of emails
   * @param roles Show access level
   * @returns { string } message segment
   */
  transformEmailsToMessage(emails:string[], showRoles:boolean = false):string {
    let emailMsgSegment = ""
    if(showRoles){
      const accessLevelsCaption = this.authRoles.getCaptionMap();
      const emailToRole = {};
      if(this.excludeEmails) {
        for(const individual of this.excludeEmails) {
          emailToRole[individual.email] = individual.role;
        }
      }
      for(const email of emails) {
        emailMsgSegment += `- ${email} (${accessLevelsCaption[emailToRole[email]] ? accessLevelsCaption[emailToRole[email]] : accessLevelsCaption[this.testAuthorCreateForm.role_type.value]}) \n`;
      }
    }else{
      emailMsgSegment += `- ${emails.join('\n- ')} \n`;
    }
    return emailMsgSegment + '\n';
  }

  /**
   * Generate message according to email types
   * @param categorizedEmails Object of categorized emails
   * @returns { string } Finalized message content in full
   */
  generateMessage(categorizedEmails:any):string {
    let message:string = '';
    const messageTemplate = {
      isEmailInvalid: 'The following email(s) are invalid:\n\n',
      isEmailExcluded: 'The following email(s) already have access to this assessment through the authoring group:\n\n',
      isEmailFiltered: 'Press <b>OK</b> to proceed to share with the following email(s):\n\n',
    }
    if(categorizedEmails.invalidEmails.length) {
      message += messageTemplate.isEmailInvalid + this.transformEmailsToMessage(categorizedEmails.invalidEmails)
    }
    if(categorizedEmails.excludedEmails.length) {
      message += messageTemplate.isEmailExcluded + this.transformEmailsToMessage(categorizedEmails.excludedEmails, true)
    }
    if(categorizedEmails.acceptedEmails.length){
      message += messageTemplate.isEmailFiltered + this.transformEmailsToMessage(categorizedEmails.acceptedEmails)
    }

    return message
  }

}
