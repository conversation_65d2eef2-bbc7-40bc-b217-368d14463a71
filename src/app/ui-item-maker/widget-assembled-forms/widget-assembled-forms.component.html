<div>
    <div class="pre-table-strip">
      <div>
          <button 
          class="button  has-icon" 
          (click)="previewCtrl.previewMsCat();" 
          >
          <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
          <span>Sample as Test Taker</span>
        </button>
        <button
          class="button has-icon"
          (click)="toggleItemExposure()"
          [class.is-info]="isItemExposureActive()"
        >
          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
          <span>Item Exposure</span>
        </button>
        <button
          class="button has-icon"
          (click)="panelCtrl.activateAllAssembledFormItems()"
          [class.is-info]="panelCtrl.activePanel && panelCtrl.filterToUsed"
        >
          <span class="icon"><i class="fa fa-filter" aria-hidden="true"></i> </span>
          <span><tra slug="auth_filter_assessment_to_used_list"></tra></span>
        </button>
        <button
          class="button"
          (click)="viewLegend = !viewLegend"
          [class.is-info]="viewLegend"
        >
          <span><tra slug="auth_toggle_assembled_panel_legend"></tra></span>
        </button>
        <button
          class="button"
          (click)="panelCtrl.refreshItemCount()"
        >
          <span><tra slug="lbl_sl_refresh_data"></tra></span>
        </button>
      </div>
      <div>
        <mat-slide-toggle [(ngModel)]="isEditPanelIds">
          Edit Panel IDs
        </mat-slide-toggle>
        <button 
        [disabled]="isReadOnly()"
        class="button is-small has-icon" 
        (click)="clearSourceForms()"
        >
        <span class="icon"><i class="far fa-trash" aria-hidden="true"></i> </span>
        <span>Clear Forms/Panels</span>
      </button>
      <button 
        [disabled]="isReadOnly()"
        class="button is-small" 
        (click)="loadFromSourceForms()"
      >
        <span>Load Published Panels</span>
      </button>
      <button 
        [disabled]="isReadOnly()"
        class="button is-small is-success" 
        (click)="newPanel()"
      >
        <span>New Panel</span>
      </button>
      <button 
        [disabled]="isReadOnly()"
        class="button is-small is-success" 
        (click)="exportAssembledPanels()"
      >
        <span>Export Panels</span>
      </button>
    </div>
  </div>
  <div *ngIf="viewLegend" class="assembled_panels_legend">
    <table>
      <tr>
        <th><tra slug="title_section"></tra></th>
        <th><tra slug="ie_description"></tra></th>
        <th><tra slug="ie_quadrant"></tra>(s)</th>
      </tr>
      <tr *ngFor="let section of frameworkCtrl.asmtFmrk.partitions; let idx = index;">
        <td> <span class="tag" [style.background-color]="testletSectionBackgroundColor[idx]" style="color: white">{{section.id}}</span></td>
        <td>{{section.description}}</td>
        <td>{{getSectionQuadrants(section)}}</td>
      </tr>
    </table>
    <tra slug="auth_assembled_panel_legend_color_hint"></tra><br>
    <tra slug="auth_assembled_panel_legend_hover_hint"></tra>
  </div>
  <div *ngIf="isItemExposureActive()" style="margin-bottom:3em;">
    <table style="width:auto">
      <tr>
        <th>Item ID</th>
        <th>Label</th>
        <th>Panel Presence</th>
        <th>Est. Exposure</th>
        <th>Testlets</th>
        <th>Panels</th>
      </tr>
      <tr *ngFor="let item of itemStats">
        <td>{{item.item_id}}</td>
        <td>{{item.label}}</td> 
        <td>{{item.panelPresence}}</td> 
        <td>{{formatPerc(item.estimatedExposure)}}</td>
        <td>
          <div class="tags">
            <span 
              *ngFor="let testletId of item.testletIds"
              class="tag is-dark"
            >{{testletId}}</span>
          </div>
        </td>
        <td>
          <div class="tags">
            <span 
              *ngFor="let panelId of item.panelIds"
              class="tag is-primary"
            >{{panelId}}</span>
          </div>
        </td>
      </tr>
    </table>
    <button (click)="logItemStats()">Log</button>
    <hr>
  </div>

  <table style="width:auto" *ngIf="frameworkCtrl.asmtFmrk.assembledPanels" style="margin-bottom:2em; width:auto;">
    <tr>
      <th>Panel</th>
      <!-- <th>Created On</th> -->
      <!-- <th>Items Mapped</th> -->
      <th>Language</th>
      <th>Testlets</th>
      <th>Operational Items</th>
      <th>Field Test Items</th>
      <th>Weight Multiplier</th>
      <th>Disabled</th>
      <th>Preview Panel</th>
      <th>Print View</th>
      <th>Remove</th>
    </tr>
    <tr *ngFor="let panel of frameworkCtrl.asmtFmrk.assembledPanels">
      <td>
        <code  *ngIf="!isEditPanelIds"> {{panel.id}} </code>
        <input *ngIf="isEditPanelIds" type="text" [(ngModel)]="panel.id">
      </td>
      <td> <span>{{panel.lang}}</span> </td>
      <td>
        <div class="tags">
          <span 
            *ngFor="let testletId of panel.testletIds"
            [style]="getTestletSectionColor(testletId)"
            [tooltip]="getTestletQuadrant(testletId)"
            class="tag is-dark"
          >{{testletId}}</span>
        </div>
      </td>
      <!-- <td>{{panel.dateCreated}}</td> -->
      <td *ngIf="false"> 
        <button 
        (click)="panelCtrl.activateAssembledFormItems(panel.id)" 
        class="button is-small has-icon" 
        [class.is-info]="panelCtrl.activePanel && panelCtrl.activePanel.id===panel.id && !panelCtrl.activePanel.moduleId"
        >
          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
          <span>List All</span>
        </button> 
        &rarr;
        <button 
          *ngFor="let module of panel.modules"
          (click)="panelCtrl.activatePanelQuestions(panel.id, module.moduleId)" 
          class="button is-small has-icon" 
          [class.is-info]="panelCtrl.activePanel && panelCtrl.activePanel.id===panel.id && panelCtrl.activePanel.moduleId===module.moduleId"
        >
          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
          <span>Module {{module.moduleId}}</span>
        </button> 
      </td>
      <td>
       {{panel.numOperationalItems || 0}}
      </td>
      <td>
        {{panel.numFieldTestItems || 0}}
      </td>
      <td [ngSwitch]="currentPanelEdit === panel">
        <div *ngSwitchCase="false" (click)="currentPanelEdit = panel">
          {{panel.multiplier || 1}}
        </div>
        <div *ngSwitchCase="true">
          <input [(ngModel)]="panel.multiplier" type="number" style="width:5em;">
          <button (click)="confirmMultiplier(panel)">OK</button>
        </div>

      </td>
      <td>
        <button (click)="panel.isExcluded = !panel.isExcluded" class="button is-small has-icon" [class.is-info]="panel.isExcluded">
          <span class="icon"><i class="fas fa-ban" aria-hidden="true"></i> </span>
          <span>Disable</span>
        </button>
      </td>
      <td>
        <button (click)="previewCtrl.previewTloft(panel)" class="button is-small has-icon">
          <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
          <span>Preview</span>
        </button>
      </td>
      <td>
        <button (click)="printViewCtrl.gotoTloftPrintView(panel)" class="button is-small has-icon">
          <span class="icon"><i class="fa fa-print" aria-hidden="true"></i> </span>
          <span><tra slug="auth_print_view"></tra></span>
        </button>        
      </td>
      <td>
        <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.assembledPanels, panel)" class="button is-small is-danger has-icon">
          <span class="icon"><i class="fa fa-archive" aria-hidden="true"></i> </span>
          <span>Archive</span>
        </button>
      </td>
    </tr>
    </table>
  </div>