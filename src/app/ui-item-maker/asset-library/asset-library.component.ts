import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild, 
    ViewEncapsulation,
} from '@angular/core';
import { ALLOWED_ASSET_FILE_TYPES, AssetField, AssetTypes, IAssetGroup, Modes } from './types';
import { AltTextService } from '../alt-text.service';
import { AssetFieldType, ASSET_TYPE_OPTIONS, EAssetField, FIELD_OPTIONS, IAssetFieldType, IAssetLibrary, ILibraryAsset, LANG_OPTIONS, SITTING_OPTIONS, STATUS_OPTIONS, YES_NO_OPTIONS } from '../asset-details/types';
import { AssetLibraryService, AssetTypeFilter} from '../services/asset-library.service';
import { AssignedUsersService } from "../assigned-users.service";
import { AuthRolesService} from "../auth-roles.service";
import { AuthScopeSettingsService} from '../auth-scope-settings.service';
import { AuthService} from '../../api/auth.service';
import { createDefaultElement, generateDefaultElementImage} from '../item-set-editor/models';
import { DomSanitizer} from '@angular/platform-browser';
import { EditingDisabledService} from '../editing-disabled.service';
import { EditSaveComponent} from '../../ui-partial/edit-save/edit-save.component';
import { ElementType, IQuestionConfig} from '../../ui-testrunner/models';
import { ENoteItemType } from '../element-notes/types';
import { FilterSettingMode } from "../../ui-partial/capture-filter-range/capture-filter-range.component";
import { FormControl, FormGroup } from '@angular/forms';
import { ItemBankCtrl} from "../item-set-editor/controllers/item-bank";
import { ItemMakerService} from '../item-maker.service';
import { LangService} from '../../core/lang.service';
import { LoginGuardService} from '../../api/login-guard.service';
import { MemberAssignmentCtrl} from "../item-set-editor/controllers/member-assignment";
import { MemDataPaginated, FilterType} from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { memo } from '../../ui-testrunner/element-render-video/element-render-video.component';
import { MyInstitutionService} from '../../ui-testadmin/my-institution.service';
import { RoutesService} from '../../api/routes.service';
import { IContentElementImage } from '../../ui-testrunner/element-render-image/model';

export const getAllowedAssetFileExtensions = () => {
  let allowedAssetFileExtensions = [];

  for(const k of Object.keys(ALLOWED_ASSET_FILE_TYPES)) {
    allowedAssetFileExtensions = allowedAssetFileExtensions.concat(ALLOWED_ASSET_FILE_TYPES[k].map(i => i.toLowerCase()));
  }

  return allowedAssetFileExtensions;
}

export const genFileExtToType = () => {
  const fileExtToType:{[key:string]: AssetTypes} = {};

  for(const k of Object.keys(ALLOWED_ASSET_FILE_TYPES)) {
    const v = ALLOWED_ASSET_FILE_TYPES[k];
    for(const t of v) {
      fileExtToType[t] = <AssetTypes>k;
    }
  }
  return fileExtToType;
}

@Component({
  selector: 'asset-library',
  templateUrl: './asset-library.component.html',
  styleUrls: ['./asset-library.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AssetLibraryComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  constructor(
    private auth: AuthService,
    private routes:RoutesService,
    private myInst: MyInstitutionService,
    private sanitizer: DomSanitizer,
    private editingDisabled: EditingDisabledService,
    private authScope: AuthScopeSettingsService,
    private altTextService: AltTextService,
    private loginGuard: LoginGuardService,
    public assetLibrary: AssetLibraryService,
    public lang: LangService,
    private myItems: ItemMakerService,
    private assignedUsersService: AssignedUsersService,
    private authRoles: AuthRolesService
  ) { }
  
  @ViewChild('altTextId') altTextElementRef:ElementRef;
  @ViewChild(EditSaveComponent) editSaveComponent:EditSaveComponent;

  @Input() activeElement:IContentElementImage;
  @Input() initEditing:boolean;
  @Input() focusedField:AssetField;
  @Input() itemElement:IQuestionConfig;
  @Input() assetId: number;
  @Input() assetGroupId: number;
  @Input() itemBankCtrl: ItemBankCtrl
  @Input() ignoreDisablingService?: boolean;

  @Output() close = new EventEmitter();
  EAssetField = EAssetField;
  memberAssignmentCtrl: MemberAssignmentCtrl;
  ENoteItemType = ENoteItemType;

  allAssetTypes = [
    {id: AssetTypes.IMAGE,  caption: 'Image'},
    {id: AssetTypes.VIDEO,  caption: 'Video'},
    {id: AssetTypes.AUDIO,  caption: 'Audio'},
    {id: AssetTypes.GRAPHIC, caption: 'Graphic'},
    {id: AssetTypes.TEXT,   caption: 'Text'},
    {id: AssetTypes.ITEM,   caption: 'Item'},
    {id: AssetTypes.SUBTITLE,   caption: 'Subtitle'},
  ];
  availableAssetTypes = [];
  LANG_OPTIONS = LANG_OPTIONS;

  availableModes = [
    {id: Modes.TEMP, caption: this.lang.tra('auth_temp_asset')},
    {id: Modes.NEW, caption: this.lang.tra('auth_new_asset')},
    // {id: Modes.BATCH_NEW, caption: 'Batch Asset Creation'},
    {id: Modes.SEARCH, caption: this.lang.tra('auth_asset_library')},
    {id: Modes.DETAIL, caption: this.lang.tra('auth_asset_information')},
    {id: Modes.NEW_PARAM, caption: this.lang.tra('auth_new_asset_param')}
  ];

  isBatchCreationExpanded:boolean;
  batchCreationJson = new FormControl()

  loadedAssets: ILibraryAsset[] = [];

  Modes = Modes;
  AssetTypes = AssetTypes;
  activeMode:Modes;
  selectedAsset:ILibraryAsset;
  assetGroups: IAssetGroup[] = [];
  selectedAssetGroup: IAssetGroup = null;
  showAssetGroups: boolean = false;
  allowedAssetFileExtensions: string[] = getAllowedAssetFileExtensions();
  fileExtToType: {[key:string]: AssetTypes} = {};

  isCreatingParam: boolean = false;

  assetTypeCaptionMap: {[key:string]: string} = {};
  
  private lower = (s: any): string => String(s).toLowerCase()
  private getOptionCaption = (list: {id: number, caption: string}[], id: any): string => {
    const item = list.filter(o => o.id == id)[0];
    return item ? item.caption : ''
  };
  private filterOptions(asset: ILibraryAsset, field: EAssetField, val: any, options: any[]): boolean {
    val = this.lower(val)
    let propVal = asset[field];
    const searchVal = this.lower(this.getOptionCaption(options, propVal))
    return searchVal.indexOf(val) !== -1
  }
  filterBoolean(asset: ILibraryAsset, field: EAssetField, val: any): boolean {
    if (isNaN(val)) return false;
    else {
      val = Number(val);
      if (val === 0 || val === 1) {
        return asset[field] == !!val || ((asset[field]==undefined || asset[field]==null) && !val)
      } else {
        return false;
      }
    }
  }
  filterNumber(asset: ILibraryAsset, field: EAssetField, val: any): boolean {
    if (isNaN(val)) return false;
    else {
      val = Number(val);
      return asset[field] == val
    }
  }

  libraryAssetsTable = new MemDataPaginated<ILibraryAsset>({
    data: [],
    pageSize: 20,
    sortSettings: {},
    filterSettings: {
      content: (asset: ILibraryAsset, val: any): boolean => {
        val = this.lower(val);
        return !!this.searchableContentFields.filter(field => {
          const propVal = asset[field];
          if (typeof propVal !== 'undefined') {
            const options = FIELD_OPTIONS[field]
            let searchVal = (options && options !== YES_NO_OPTIONS && options !== LANG_OPTIONS) ? this.getOptionCaption(options, propVal) : propVal;
            if (typeof searchVal !== 'undefined' && searchVal !== null) {
              if (Array.isArray(searchVal)) {
                // @ts-ignore
                return !!searchVal.filter(pv => this.lower(pv).indexOf(val) !== -1).length
              } else {
                return this.lower(searchVal).indexOf(val) !== -1
              }
            }
          }
          return false;
        }).length        
      },
      assetGroup: (asset: ILibraryAsset): boolean => {
        return this.selectedAssetGroup.assetIds.indexOf(asset.id) !== -1
      },
      [EAssetField.STATUS]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterOptions(asset, EAssetField.STATUS, val, STATUS_OPTIONS)
      },
      [EAssetField.SITTING_IN]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterOptions(asset, EAssetField.SITTING_IN, val, SITTING_OPTIONS)
      },
      [EAssetField.IS_COPYRIGHT_CHECKED]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterBoolean(asset, EAssetField.IS_COPYRIGHT_CHECKED, val)
      },
      [EAssetField.IS_PLAGIARISM_CHECKED]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterBoolean(asset, EAssetField.IS_PLAGIARISM_CHECKED, val)
      },
      [EAssetField.LANGUAGE]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterOptions(asset, EAssetField.LANGUAGE, val, LANG_OPTIONS)
      },
      [EAssetField.IS_REQUEST_FORM_COMPLETE]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterBoolean(asset, EAssetField.IS_REQUEST_FORM_COMPLETE, val)
      },
      [EAssetField.TOTAL_TEST_TAKERS]: (asset: ILibraryAsset, val: any): boolean => {
        return this.filterNumber(asset, EAssetField.TOTAL_TEST_TAKERS, val)
      },
    },
  });
  
  assetLibraries:IAssetLibrary[] = [];
  
  selectedAssetType = new FormControl();  
  selectedLibrary = new FormControl();
  altText = new FormControl();
  altTextFr = new FormControl();
  description = new FormControl();
  tags = new FormControl();
  assetName = new FormControl();

  
  newParamFg = new FormGroup( {
    name: new FormControl(),
    assetLibrary: new FormControl(),
    location: new FormControl(),
    type: new FormControl()
  });



  
  submittedBy = new FormControl();
  dateOfSubmission = new FormControl(); 
  snapshots = new FormControl();
  originalTitle = new FormControl();
  authorName = new FormControl();
  publicationName = new FormControl();
  publicationAuthor = new FormControl();
  publicationDate = new FormControl();
  publisherName = new FormControl();
  publisherAddress = new FormControl();
  webAddress = new FormControl();
  dateAccessed = new FormControl();
  language = new FormControl();
  copyrightAcqReq = new FormControl();
  bookFrontCover = new FormControl();
  bookCopyright = new FormControl();
  bookAcknowledgement = new FormControl();
  assetLanguageForm = new FormGroup({
    assetLanguage: new FormControl('en')
  });
  newUploadElement:IContentElementImage;
  showNewUpload:boolean = false;
  standAloneElement:IContentElementImage;
  createStatus:string;

  santiziedUrls = new Map();
  contentQuery = new FormControl();
  private searchableContentFields: EAssetField[];

  createFieldTypes: IAssetFieldType[];

  ngOnInit(): void {

    this.fileExtToType = genFileExtToType();
    this.dateOfSubmission.setValue(moment(new Date()).format('YYYY-MM-DD'));
    this.submittedBy.setValue(`${this.auth.user().value.firstName} ${this.auth.user().value.lastName}`);
    this.availableAssetTypes = this.allAssetTypes.filter(type => [
      AssetTypes.IMAGE,
      AssetTypes.VIDEO,
      AssetTypes.AUDIO,
      AssetTypes.TEXT,
    ].indexOf(type.id) !== -1);
    this.createFieldTypes = [
      {
        id: AssetFieldType.TEXT,
        caption: "asset_field_type_text"
      },
      {
        id: AssetFieldType.FILE_UPLOAD,
        caption: "asset_field_type_file_upload"
      },
      {
        id: AssetFieldType.IMAGE_UPLOAD,
        caption: "asset_field_type_img_upload"
      },
      {
        id: AssetFieldType.CHECKBOX,
        caption: "asset_field_type_checkbox"
      },
      {
        id: AssetFieldType.DATE,
        caption: "asset_field_type_date"
      },
      {
        id: AssetFieldType.NUMBER,
        caption: "asset_field_type_number"
      }
    ]
    for(const o of ASSET_TYPE_OPTIONS) {
      this.assetTypeCaptionMap[o.id] = o.caption;
    }
    this.standAloneElement = createDefaultElement(ElementType.IMAGE)
    if (this.itemElement) {
      this.selectedAssetType.setValue(AssetTypes.ITEM)
    } else if (this.activeElement && !this.activeElement.assetId) {
      const url = this.activeElement.url;
      if (url) {
        this.standAloneElement.url = url;
        const fileExt = url.split('.').pop().toUpperCase();
        this.selectedAssetType.setValue(this.fileExtToType[fileExt]);
        const lang = this.lang.getCurrentLanguage();
        this.assetLanguageForm.controls.assetLanguage.setValue(lang)
      }
    }
    this.newUploadElement = _.cloneDeep(this.activeElement);
    this.searchableContentFields = [
      EAssetField.ASSET_NAME,
      EAssetField.ASSET_FILE,
      EAssetField.SUBMITTED_BY,
      EAssetField.SNAPSHOTS,
      EAssetField.TAGS,
      EAssetField.STATUS,
      EAssetField.ORIGINAL_TITLE,
      EAssetField.AUTHOR_NAME,
      EAssetField.DESCRIPTION,
      EAssetField.ASSESSMENT_RELEASE_YEAR,
      EAssetField.ASSESSMENT_RELEASE_STATUS,
      EAssetField.ASSESSMENT_TYPE,
      EAssetField.LANGUAGE,
      EAssetField.POINT_OF_CONTACT,
      EAssetField.BOOK_FRONT_COVER,
      EAssetField.BOOK_COPYRIGHT,
      EAssetField.BOOK_ACKNOWLEDGEMENT,
      EAssetField.ATTRIBUTION,
      EAssetField.SITTING_IN,
      EAssetField.MEDIUM
    ];

    this.loadAllAssetData()
    .then(()=>{

      //only loads if they haven't been loaded already
      this.myItems.loadMyAuthoringGroups(false).then(() => {
        this.myItems.refreshGroupsAsSuper();
        this.memberAssignmentCtrl = new MemberAssignmentCtrl(
            this.auth, 
            this.assignedUsersService, 
            this.authRoles, 
            this.routes
        );
        return this.getAssetLibraries()
      })
      .then(()=> {
        if (this.assetId) {
          const asset = this.loadedAssets.filter(a=>a.id == this.assetId)[0]
          this.viewLibraryAsset(asset);
        }
        this.selectActiveMode();
      })

      this.newUploadElement = _.cloneDeep(this.activeElement);
      if (this.assetId) {
        const asset = this.loadedAssets.filter(a=>a.id === this.assetId)[0]
        this.viewLibraryAsset(asset);
      }
      this.selectActiveMode();
    })
  }

  ngOnDestroy() {
    this.assetLibrary.clearAssetIdFilter();
    if(this.memberAssignmentCtrl) {
      this.memberAssignmentCtrl.destroy();
    }
  }

  ngAfterViewInit() {
    /*setTimeout(() => {
      if(this.editLibraryAsset.isEditing()) {
        const assetFieldToElement = {
          [AssetField.ALT_TEXT]: this.altTextElementRef
        }
        assetFieldToElement[this.focusedField].nativeElement.focus();
      }
    }, 400);*/
  }

  getElementUrl(){
    return this.standAloneElement.url
  }
  
  getHiContrastElementUrl() {
    if (this.standAloneElement.hiContrastImg) {
      return this.standAloneElement.hiContrastImg.url
    }
    return ''
  }

  isImage() {
    const supportedImageExtensions: string[] = ['JPG', 'JPEG', 'PNG', 'GIF'].map(i => i.toLowerCase());
    const url = this.getElementUrl();
    if (url) {
      return supportedImageExtensions.indexOf(url.split('.').pop().toLowerCase()) !== -1        
    }
    return false;
  }

  showModeOption(modeOption:Modes){
    if ((modeOption === Modes.TEMP) && !this.isElementConnected()){
      return false;
    }
    if (modeOption === Modes.DETAIL && !this.selectedAsset) {
      return false;
    }
    if(modeOption === Modes.NEW_PARAM && this.newParamDisabled()) {
      return false;
    }
    return true;
  }

  newParamDisabled() {
    return !this.myItems.hasGroupsAsSuper
  }

  ngOnChanges(changes:SimpleChanges){
    this.selectActiveMode();
  }

  selectDefaultMode(){
    this.activeMode = Modes.SEARCH;
  }
  
  selectActiveMode() {
    if (this.itemElement ) {
      this.activeMode = Modes.NEW;
    } else if (this.assetId) {
      this.activeMode = Modes.DETAIL;
    } else {
      this.selectDefaultMode();
    }
  }
  
  selectLibraryAsset(asset:ILibraryAsset){
    this.selectedAsset = asset;
  }

  selectModeFromMenu(mode: Modes) {
    if (mode !== Modes.DETAIL) {
      this.selectedAsset = null 
      if (this.standAloneElement && (!this.activeElement || this.activeElement.assetId)) {
        this.standAloneElement.url = null 
      }
    }
    this.activeMode = mode;
  }

  viewLibraryAsset(asset:ILibraryAsset){
    if (asset) {
      this.selectLibraryAsset(asset);
      this.activeMode = Modes.DETAIL;
    }
  }

  viewCopyrightRequestForm(asset:ILibraryAsset){
    console.log(`viewCopyrightRequestForm: ${asset.asset_id}`)
    this.loginGuard.quickPopup('This view is unavailable at the moment.')    
  }
  
  canUseSelectedItem(){
    return (this.selectedAsset && this.selectedAsset.asset_type != AssetTypes.ITEM && this.isElementConnected())
  }

  swapInAsset(asset:ILibraryAsset){
    this.selectLibraryAsset(asset);
    this.useLibraryAsset();
  }

  useLibraryAsset(){
    if (this.activeElement && !this.itemElement) {
      console.log(this.activeElement)
      this.activeElement.assetId = this.selectedAsset.asset_id;
      this.activeElement.assetVersionId = this.selectedAsset.current_version_id;
      const urlProp: EAssetField = this.itemBankCtrl.isLang('en') ? EAssetField.ASSET_FILE : EAssetField.ASSET_FILE_FR;
      let propOrder: EAssetField[] = [urlProp, urlProp === EAssetField.ASSET_FILE ? EAssetField.ASSET_FILE_FR : EAssetField.ASSET_FILE];
      const urlPropHC: EAssetField = this.itemBankCtrl.isLang('en') ? EAssetField.ASSET_FILE_HC : EAssetField.ASSET_FILE_FR_HC;
      let propOrderHC: EAssetField[] = [urlPropHC, urlPropHC === EAssetField.ASSET_FILE_HC ? EAssetField.ASSET_FILE_FR_HC : EAssetField.ASSET_FILE_HC];
      
      const hcFunc = function (hc:string, activeElement) {
        if (hc) {
          if (!activeElement.hiContrastImg) {
            activeElement.hiContrastImg = generateDefaultElementImage('image')
          }
          activeElement.hiContrastImg.url = hc
        }
      }
      if(this.showNewUpload){
        this.activeElement.url = this.newUploadElement.url;
        hcFunc(this.newUploadElement.hiContrastImg.url, this.activeElement)
      }else{
        this.activeElement.url = (this.selectedAsset[propOrder[0]] || this.selectedAsset[propOrder[1]]) as string;
        const hc = (this.selectedAsset[propOrderHC[0]] || this.selectedAsset[propOrderHC[1]]) as string;
        hcFunc(hc, this.activeElement)
      }
      if (this.selectedAsset.transcript_en && this.itemBankCtrl.isLang('en')) {
        this.activeElement.transcriptUrl = this.selectedAsset.transcript_en
      }
      if (this.selectedAsset.transcript_fr && this.itemBankCtrl.isLang('fr')) {
        this.activeElement.transcriptUrl = this.selectedAsset.transcript_fr
      }
    } else if (this.isCurrObjItem()) {
      //console.log(this.selectedAsset)
      const id = this.selectedAsset["item_id"]
      this.auth.apiGet(this.routes.TEST_AUTH_QUESTIONS, id).then((res)=>{
        //console.log(JSON.parse(res.config))
        this.itemBankCtrl.itemEditCtrl.currentElementImportExport.setValue(res.config)
        this.itemBankCtrl.itemEditCtrl.currentElement = this.itemBankCtrl.currentQuestion
        this.itemBankCtrl.itemEditCtrl.importFormIntoElement()
      })

    }
    this.close.emit();
  }

  isCurrObjItem() {
    return !this.activeElement && this.itemElement
  }

  loadAllAssetData(){
    this.loadedAssets = []
    return this.auth.apiFind(this.routes.TEST_AUTH_ASSET, {})
    .then((data)=>{
      console.log(data)
      this.assetGroups = data.groupData;

      if(this.assetGroupId) {
        const linkedGroup = this.assetGroups.filter( g => g.id === this.assetGroupId) 
        if(linkedGroup.length) {
          this.toggleAssetGroup(linkedGroup[0]);
        }
      }

      if(this.assetLibrary.assetIdFilter) {
        data.data = data.data.filter(d => this.assetLibrary.assetIdFilter.includes(d.id));
      }

      this.loadedAssets = data.data;
      this.loadedAssets.forEach(asset => {
        if (asset.tags){
          asset._tags = asset.tags.replace(/\s\s+/g,' ').split(' ');
        }
        else{
          asset._tags = [];
        }
        if(asset.custom_fields) {
          asset.custom_fields = JSON.parse(asset.custom_fields);
        }
      });
      if (this.assetLibrary.typeFilter == AssetTypeFilter.EXCLUDE_ITEMS) {
        data.data = data.data.filter((el)=>{
          if (el["asset_type"]==AssetTypes.ITEM) {
            return false;
          }
          return true;
        })
      } else if (this.assetLibrary.typeFilter == AssetTypeFilter.ONLY_ITEMS) {
        data.data = data.data.filter((el)=>{
          if (el["asset_type"]==AssetTypes.ITEM) {
            return true;
          }
          return false;
        })
      }
      this.libraryAssetsTable.injestNewData(data.data);
      if(this.activeElement && this.activeElement.assetId){
        this.selectedAsset = _.find(data.data, {asset_id: this.activeElement.assetId})
        this.activeMode = Modes.SEARCH;
      }
    })
  }

  isUploadActive(){
    return this.isElementConnected() || (this.activeMode === Modes.NEW);
  }
  isNewActive(){
    return (this.activeMode === Modes.NEW);
  }
  isSearchActive(){
    return (this.activeMode === Modes.SEARCH);
  }
  isTempActive(){
    return (this.activeMode === Modes.TEMP);
  }


  isElementConnected(){
    return !!this.activeElement || !!this.itemElement;
  }

   getAssetLibraries(){
    return this.auth.apiFind(this.routes.TEST_AUTH_ASSET_LIBRARIES)
    .then((data)=>{
      this.assetLibraries = data;
    })
  }

  batchCreateAssets(){
    this.createStatus = 'uploading'
    const records = JSON.parse(this.batchCreationJson.value);
    Promise.all(records.map(record => {
      this.auth.apiCreate(
        this.routes.TEST_AUTH_ASSET,       
        { ... record },
        this.myInst.constructPermissionsParams()
      )
    }))
    .then( ()=>{
      console.log('created : ', records);
      this.createStatus = 'success';
      this.loadAllAssetData()
    })
  }

  allParamFieldsFilled() {
    for(const fc of Object.values(this.newParamFg.controls)) {
      if(!fc.value){
        return false;
      }
    }
    return true;
  }

  // from https://gist.github.com/codeguy/6684588
  slugify(caption: string) {
    return caption
        .normalize('NFD') // split an accented letter in the base letter and the acent
        .replace(/[\u0300-\u036f]/g, '') // remove all previously split accents
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9 ]/g, '') // remove all chars not letters, numbers and spaces (to be replaced)
        .replace(/\s+/g, '_') // separator
  }

  createParameter() {
    if(!this.allParamFieldsFilled()) {
      this.loginGuard.quickPopup('required_fields_error');
      return;
    }

    this.isCreatingParam = true;
    const c = this.newParamFg.controls;
    const caption = c.name.value;
    const asset_library_id = c.assetLibrary.value;
    const type = c.type.value;
    const group_slug = c.location.value;

    const slug = this.slugify(caption);

    const data = { 
      slug,
      caption,
      asset_library_id,
      type,
      group_slug
    }
    this.auth.apiCreate(this.routes.TEST_AUTH_ASSET_LIBRARY_FIELDS, data).then(() => {
      this.isCreatingParam = false;
      for(const c of Object.values(this.newParamFg.controls)) {
        c.setValue(undefined);
      }
    });


  }
 
  createSingleAsset(){
    if(this.activeMode === 'NEW'){
      if(!this.selectedLibrary.value || !this.selectedAssetType.value || (!this.getElementUrl() && !this.itemElement)){
        alert('Please enter required parameters')
        return;
      }
      this.createStatus = 'uploading';
      const assetLang = this.assetLanguageForm.controls.assetLanguage.value;
      const urlField: EAssetField = assetLang === 'en' ? EAssetField.ASSET_FILE : EAssetField.ASSET_FILE_FR; 
      const urlFieldHC: EAssetField = assetLang === 'en' ? EAssetField.ASSET_FILE_HC : EAssetField.ASSET_FILE_FR_HC; 
      const itemElement = this.assetLibrary.observer.value["itemElement"];
      const currItemID = itemElement ? itemElement.id : undefined;
      this.auth.apiCreate(
        this.routes.TEST_AUTH_ASSET,
        {
          asset_library_id: this.selectedLibrary.value,
          asset_name: this.assetName.value,
          [urlField]: this.itemElement? '' : this.getElementUrl(),
          [urlFieldHC]: this.itemElement? '' : this.getHiContrastElementUrl(),
          alt_text: this.altText.value,
          alt_text_fr: this.altTextFr.value,
          submitted_by: this.submittedBy.value,
          submitted_on: this.dateOfSubmission.value,
          asset_type: this.selectedAssetType.value,
          snapshots: this.snapshots.value,
          tags: this.tags.value,
          original_title:this.originalTitle.value,
          author_name:this.authorName.value,
          publication_name:this.publicationName.value,
          publication_author:this.publicationAuthor.value,
          publication_date:this.publicationDate.value,
          publisher_name:this.publisherName.value,
          publisher_address:this.publisherAddress.value,
          web_address:this.webAddress.value,
          date_accessed:this.dateAccessed.value,
          description: this.description.value,
          copyright_acquisition: this.copyrightAcqReq.value,
          book_front_cover: this.bookFrontCover.value,
          book_copyright: this.bookCopyright.value,
          book_acknowledgement: this.bookAcknowledgement.value,
          item_id: currItemID
        },
        this.myInst.constructPermissionsParams()
      )
      .then((data)=>{
        console.log('created : ', data)
        this.createStatus = 'success';
        this.loadAllAssetData()
        const asset = data[0].item_asset;
        if(this.activeElement){
          const asset_version = data[0].item_asset_version;
          this.activeElement.assetId = asset.id;
          this.activeElement.assetVersionId = asset_version.id;
          this.activeElement.url = asset_version.url;          
        } else if (this.itemElement) {
          this.itemElement.assetId = asset.id;
        }
      })
      .catch((err)=>{
        this.createStatus = 'failed'
      })
    }
  }
  renderUrl(url:string){
    return memo(this.santiziedUrls, url, url  => this.sanitizer.bypassSecurityTrustResourceUrl(url) );
  }
  
  renderSnapshotsUrl(asset: ILibraryAsset): string {
    const field: EAssetField = this.assetLibrary.getAssetPreviewField(asset);
    return asset[field] as string;
  }
  
  renderDate(date: string | Date){
    return date ? moment.utc(date).format('MMMM DD, YYYY') : '';
  }

  renderStatus(asset: ILibraryAsset) {
    return asset.status ? STATUS_OPTIONS[asset.status].caption : ''
  }

  renderLocation(asset: ILibraryAsset) {
    return asset.sitting_in ? SITTING_OPTIONS[asset.sitting_in].caption : ''
  }
  
  uploadTemp(){
    this.activeElement.altText = this.altText.value;
    
    delete(this.activeElement.assetId);
    delete(this.activeElement.assetVersionId);

    this.activeElement.url = this.getElementUrl();
    this.close.emit();
  }

  editAssetKeyPress(event) {
    if(event.key === 'Enter') {
      this.editSaveComponent.editSave();
      this.close.emit();
    }
  }

  isAssetLocked(asset : ILibraryAsset) {
    return this.editingDisabled.isReadOnly(true) || !!asset.is_locked;
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly()
  }
  
  getAssetTypeDisplay(item: AssetTypes) {
    return this.allAssetTypes.filter(type => type.id === item)[0].caption
  }

  filterByContent() {
    const value = this.contentQuery.value;
    if (value) {
      this.libraryAssetsTable.unionFilters['content'] = {
        mode: FilterSettingMode.VALUE,
        config: { value }
      }
      this.searchableContentFields.forEach((field)=>{
        this.libraryAssetsTable.unionFilters[field] = {
          mode: FilterSettingMode.VALUE,
          config: {value}
        }
      })
    } else {
      delete this.libraryAssetsTable.unionFilters['content']
      this.searchableContentFields.forEach((field)=>{
        delete this.libraryAssetsTable.unionFilters[field]
      })
    }
    this.libraryAssetsTable.refreshFilters("assets");
  }



  async startUpload(fc: FormControl, files: FileList) {
    const allowedFileTypes = this.allowedAssetFileExtensions;
    const uploadPromises = [];
    for(let i = 0; i < files.length; i++) {
      const file = files.item(0);
      // Client-side validation example
      const fileTypeFull = file.type;
      const fileType = fileTypeFull.split('/')[0];
      const fileExt = file.name.split('.').pop();
      if (allowedFileTypes) {
        if (!(_.includes(allowedFileTypes, fileType) || _.includes(allowedFileTypes, fileExt) || 
          (allowedFileTypes.indexOf('Graphics') > -1 && file.type === 'image/svg+xml'))
        ) {
          alert('Unsupported File Type ' + fileType);
          return;
        }
      }
      uploadPromises.push( this.auth
          .uploadFile(file, file.name, 'authoring', true));
    }
    const allUrls = await Promise.all(uploadPromises);
    if(fc) {
      const url = allUrls.filter(res => res.success)[0].url;
      fc.setValue(url);
    }
  }

  isLockAvail() {
    return this.myItems.hasGroupsAsSuper;
  }

  hasAssetSelection(): boolean {
    return !!this.libraryAssetsTable.getCurrentPageData().filter(a => a._isSelected).length
  }

  saveAssetGroup() {
    const name = prompt(`Name this group`);
    if (name) {
      const assetIds: number[] = this.libraryAssetsTable.getCurrentPageData().filter(a => a._isSelected).map(a => a.id);
      const newGroup: Partial<IAssetGroup> = {
        name,
        assetIds
      }
      this.auth.apiCreate(this.routes.TEST_AUTH_ASSET_GROUPS, {name, assetIds})
      .then(() => {
        // @ts-ignore
        this.assetGroups.push(newGroup)
        this.showAssetGroups = true;
      })
    }
  }

  getNumImpressions(asmt) {
    return 1;
  }

  deleteAssetGroup(group: IAssetGroup) {
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure you would like to delete this asset group?',
      confirm: () => {
        this.auth.apiRemove(this.routes.TEST_AUTH_ASSET_GROUPS, group.id)
        .then(() => {
          const index = this.assetGroups.findIndex(g => g.id === group.id)
          this.assetGroups.splice(index, 1)
        })
      }
    });
  }
  
  hasAssetGroups():boolean {
    return !!this.assetGroups.length;
  }

  viewAssetGroupList() {
    this.showAssetGroups = !this.showAssetGroups; 
  }
  
  toggleAssetGroup(group: IAssetGroup) {
    // select the group
    if (this.selectedAssetGroup !== group) {
      this.loadAssetGroupMembers(group);
      this.selectedAssetGroup = group;
      this.libraryAssetsTable.activeFilters['assetGroup'] = {
        mode: FilterSettingMode.VALUE,
        config: {value: 'placeholder'}
      }
      this.libraryAssetsTable.refreshFilters("assets");
    } else { // unselect the group and revert to all assets
      delete this.libraryAssetsTable.activeFilters['assetGroup'];
      this.selectedAssetGroup = null;
      this.libraryAssetsTable.refreshFilters("assets");
      this.clearSelection();
    }
  }

  private async loadAssetGroupMembers(group: IAssetGroup) {
      const promises: Promise<any>[] = [];
      const authGroupIds: Set<number> = new Set();
      group.assetIds.map(id => this.loadedAssets.filter(a => a.id === id)[0]).forEach(asset => {
        const authGroupId = this.assetLibraries.filter(lib => lib.id === asset.library_id)[0].group_id;
        if (!authGroupIds.has(authGroupId)) {
          authGroupIds.add(authGroupId);
          promises.push(this.myItems.loadMyGroupMembers(null, authGroupId))
        }
      })
      const all: any[] = await Promise.all(promises);
      const merged: Map<number, any> = new Map();
      all.forEach(res => {
        res.forEach(member => {
          merged.set(member.id, member);
        })
      });
      const members = Array.from(merged.values());
      this.memberAssignmentCtrl.setGroupMembers(members);
  }
  
  public clearSelection() {
    this.libraryAssetsTable.getCurrentPageData().forEach(a => a._isSelected = false)    
  }

  onAssetUpload(file: File) {
    const fileExt = file.name.split('.').pop().toUpperCase();
    if (!this.itemElement) { // skip when exporting item as asset, for that case we use AssetTypes.ITEM
      this.selectedAssetType.setValue(this.fileExtToType[fileExt]);
    }
  }

  getAssetTypeCaption(type: AssetTypes) {
    if(!type) {
      return '--'
    }
    return this.assetTypeCaptionMap[type]
  }

  saveCheckboxChange($event, asset: ILibraryAsset, prop: EAssetField) {
    // @ts-ignore
    asset[prop] = $event.checked ? 1 : 0;
    this.assetLibrary.saveAsset(asset);
  }

  assignButtonHandler($event) {
    this.memberAssignmentCtrl.openAssignUser({note: $event});
  }

  isCreateNewAssetDisabled(): boolean {
    return ( 
          !((this.activeElement && this.activeElement.url) || (this.standAloneElement && this.standAloneElement.url)) 
          || (this.isReadOnly() && !this.ignoreDisablingService)
        ) && !this.itemElement;
  }
}
