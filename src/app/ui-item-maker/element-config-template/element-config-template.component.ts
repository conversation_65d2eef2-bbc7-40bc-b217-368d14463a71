import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { IContentElementTemplate } from '../../ui-testrunner/element-render-template/model';

@Component({
  selector: 'element-config-template',
  templateUrl: './element-config-template.component.html',
  styleUrls: ['./element-config-template.component.scss']
})
export class ElementConfigTemplateComponent implements OnInit {

  @Input() element: IContentElementTemplate;

  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService
  ) { }

  ngOnInit(): void {
  }

}
