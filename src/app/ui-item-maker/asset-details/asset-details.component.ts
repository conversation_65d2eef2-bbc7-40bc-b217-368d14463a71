import {ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation} from '@angular/core';
import {FormControl} from '@angular/forms';
import {
  ASSESSMENT_RELEASE_STATUS_OPTIONS,
  ASSESSMENT_TYPE_OPTIONS,
  ASSET_TYPE_OPTIONS,
  AssetFieldGroup,
  AssetFieldType,
  EAssetField,
  IAssetField,
  IAssetFieldGroup,
  ILibraryAsset,
  LANG_OPTIONS,
  MEDIUM_OPTIONS,
  SITTING_OPTIONS,
  STATUS_OPTIONS,
  YES_NO_OPTIONS,
  IAssetLibrary
} from './types'
import {AssetTypes, ALLOWED_ASSET_FILE_TYPES, IAssetGroup, AssetField} from '../asset-library/types'
import {memo} from "../../ui-testrunner/element-render-video/element-render-video.component";
import {DomSanitizer} from "@angular/platform-browser";
import * as moment from 'moment-timezone';
import {ItemMakerService} from "../item-maker.service";
import {MemberAssignmentCtrl} from '../../ui-item-maker/item-set-editor/controllers/member-assignment';
import {AuthService} from "../../api/auth.service";
import {AssignedUsersService} from "../assigned-users.service";
import { LangService } from '../../core/lang.service';
import { AssetLibraryService } from '../services/asset-library.service';
import {EditingDisabledService} from "../editing-disabled.service";
import { RoutesService } from '../../api/routes.service';
import { AuthRolesService } from '../auth-roles.service';
import { getAllowedAssetFileExtensions } from '../asset-library/asset-library.component';
import { ENoteItemType } from '../element-notes/types';
import {TwiddleState} from "../../ui-partial/twiddle/twiddle.component";


const getDataObj = (asset: ILibraryAsset, customId?: number) => {
  let obj:any = asset;
  if(customId) {
    if(!asset.custom_fields) {
      asset.custom_fields = {};
    }
    obj = asset.custom_fields;
  }
  return obj;
}

export const getAssetVal = (asset: ILibraryAsset, field: IAssetField) => {
  if (field.config?.getDisplayValue) {
    return field.config.getDisplayValue(asset)    
  } else {
    const obj = getDataObj(asset, field.customId);
    if(field.config && field.config.captureImage && field.customId) {
      if(!obj[field.assetProp]) {
        obj[field.assetProp] = {};
      }
      return obj[field.assetProp].value;
    }
    return obj[field.assetProp];
  }
}

export const getCustomAssetType = (asset: ILibraryAsset, field: {customId, config, assetProp}) => {
  const obj = getDataObj(asset, field.customId);
  if(field.config && field.config.captureImage && field.customId) {
    if(!obj[field.assetProp]) {
      obj[field.assetProp] = {};
    }
    return obj[field.assetProp].assetType;
  }
  return undefined;
}

@Component({
  selector: 'asset-details',
  templateUrl: './asset-details.component.html',
  styleUrls: ['./asset-details.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AssetDetailsComponent implements OnInit, OnDestroy {
  @Input() asset: ILibraryAsset;
  @Input() assetLibraries: IAssetLibrary[];
  @Input() assetGroups: IAssetGroup[];

  public fields: IAssetField[];
  public fieldGroups: IAssetFieldGroup[];
  fieldsById: Map<EAssetField, IAssetField> = new Map();
  AssetTypes = AssetTypes;
  santiziedUrls = new Map();
  isLoaded: boolean = false;
  memberAssignmentCtrl:MemberAssignmentCtrl;
  isSavingLock: boolean = false;
  
  allowedAssetFileExtensions: string[] = getAllowedAssetFileExtensions();
  allowedImgFileExtensions: string[] = ALLOWED_ASSET_FILE_TYPES[AssetTypes.IMAGE].map(i => i.toLowerCase());
  EAssetField = EAssetField;
  ENoteItemType = ENoteItemType;

  currentAsset:ILibraryAsset;
  currentLibrary:IAssetLibrary;

  customFields: IAssetField[];
  groupsForSelectedAsset: {state: any, group: IAssetGroup}[];
  assetCommentsTwiddle = new TwiddleState(false);

  constructor(
      private sanitizer: DomSanitizer,
      public auth: AuthService,
      public assignedUsersService: AssignedUsersService,
      public myItems: ItemMakerService,
      public lang: LangService,
      public assetLibrary: AssetLibraryService,
      private editingDisabled: EditingDisabledService,
      private routes: RoutesService,
      private changeDetector: ChangeDetectorRef,
      private authRoles: AuthRolesService
  ) { }

  ngOnInit(): void {
    this.setAuthoringGroupMembers();    
    this.currentAsset = this.asset;
    this.groupsForSelectedAsset = this.assetGroups.filter(group => group.assetIds.indexOf(this.currentAsset.id) !== -1).map(group => {
      return {
        state: new TwiddleState(false),
        group
      }
    })
    //only loads if they haven't been loaded already
    this.myItems.loadMyAuthoringGroups(false)
      .then( () => {
        this.myItems.refreshGroupsAsSuper();
        return this.initFields().then( () => {
          this.initFieldGroups();
          this.currentLibrary = this.assetLibraries.filter(l => l.id === this.asset.library_id)[0];
          this.myItems.updateRoleFlagsFromAuthGroup(this.currentLibrary.group_id);
          this.memberAssignmentCtrl = new MemberAssignmentCtrl(this.auth, this.assignedUsersService, this.authRoles, this.routes);
          this.isLoaded = true;
        });
      })
  }

  ngOnDestroy(): void {
    if(this.memberAssignmentCtrl) {
      this.memberAssignmentCtrl.destroy();
    }
  }

  private setAuthoringGroupMembers() {
    const lib: IAssetLibrary = this.assetLibraries.filter(lib => lib.id === this.asset.library_id)[0];
    if (lib) {
      this.myItems.loadMyGroupMembers(null, lib.group_id)
        .then(result => {
          this.memberAssignmentCtrl.setGroupMembers(result);
        })
    }
  }
  
  resetter = true;
  reset() {
    this.resetter = false;
    this.changeDetector.detectChanges()
    this.resetter = true;
  }

  public initFields() {
    this.fields = [
      // Asset Description  
      { id: EAssetField.ASSET_NAME, assetProp: EAssetField.ASSET_NAME, caption: 'auth_asset_name', type: AssetFieldType.TEXT, config: {placeholder: 'auth_asset_name'}},
      { id: EAssetField.ASSET_FILE, assetProp: EAssetField.ASSET_FILE, caption: 'auth_asset_en', type: AssetFieldType.FILE_UPLOAD,
        config: {
            captureImage: true, 
            preview: true, 
            fileTypes: this.allowedAssetFileExtensions.join(','),
            assetType: this.currentAsset[EAssetField.ASSET_TYPE]
      }},
      { id: EAssetField.ASSET_FILE_FR, assetProp: EAssetField.ASSET_FILE_FR, caption: 'auth_asset_fr', type: AssetFieldType.FILE_UPLOAD,
        config: {
          captureImage: true,
          preview: true,
          fileTypes: this.allowedAssetFileExtensions.join(','),
          assetType: this.currentAsset[EAssetField.ASSET_TYPE]
        }},
      { id: EAssetField.SUBMITTED_BY, assetProp: EAssetField.SUBMITTED_BY, caption: 'auth_submitted_by', type: AssetFieldType.TEXT, config: {placeholder: 'Submitted by'}, readOnly: true},
      { id: EAssetField.DATE_OF_SUBMISSION, assetProp: EAssetField.DATE_OF_SUBMISSION, caption: 'auth_date_of_submission', type: AssetFieldType.DATE, readOnly: true},
      { id: EAssetField.ASSET_TYPE, assetProp: EAssetField.ASSET_TYPE, caption: 'auth_asset_type', type: AssetFieldType.DROPDOWN, config: {options: ASSET_TYPE_OPTIONS}, readOnly: true},
      { id: EAssetField.SNAPSHOTS, assetProp: EAssetField.SNAPSHOTS, caption: 'auth_snapshots', type: AssetFieldType.FILE_UPLOAD, config: {preview: true, fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.IMAGE}},
      { id: EAssetField.TAGS, assetProp: EAssetField.TAGS, caption: 'ie_tags', type: AssetFieldType.TEXT, config: {placeholder: 'Enter Tags'}},
      { id: EAssetField.ORIGINAL_TITLE, assetProp: EAssetField.ORIGINAL_TITLE, caption: 'auth_asset_original_title', type: AssetFieldType.TEXT, config: {placeholder: 'Original title'}},
      { id: EAssetField.AUTHOR_NAME, assetProp: EAssetField.AUTHOR_NAME, caption: 'auth_asset_author', type: AssetFieldType.TEXT, config: {placeholder: 'Author/artist'}},
      { id: EAssetField.PUBLICATION_NAME, assetProp: EAssetField.PUBLICATION_NAME, caption: 'auth_publication_name', type: AssetFieldType.TEXT, config: {placeholder: 'Publication Name'}},
      { id: EAssetField.PUBLICATION_AUTHOR, assetProp: EAssetField.PUBLICATION_AUTHOR, caption: 'auth_publication_author', type: AssetFieldType.TEXT, config: {placeholder: 'Author/editors'}},
      { id: EAssetField.PUBLICATION_DATE, assetProp: EAssetField.PUBLICATION_DATE, caption: 'auth_publication_date', type: AssetFieldType.DATE},
      { id: EAssetField.PUBLISHER_NAME, assetProp: EAssetField.PUBLISHER_NAME, caption: 'auth_publisher_name', type: AssetFieldType.TEXT, config: {placeholder: 'Publisher\'s Name'}},
      { id: EAssetField.PUBLISHER_ADDRESS, assetProp: EAssetField.PUBLISHER_ADDRESS, caption: 'auth_publisher_address', type: AssetFieldType.TEXT, config: {placeholder: 'Publisher\'s Address'}},
      { id: EAssetField.WEB_ADDRESS, assetProp: EAssetField.WEB_ADDRESS, caption: 'auth_web_address', type: AssetFieldType.TEXT, config: {placeholder: 'Web address'}},
      { id: EAssetField.DATE_ACCESSED, assetProp: EAssetField.DATE_ACCESSED, caption: 'auth_date_accessed', type: AssetFieldType.DATE},
      { id: EAssetField.CAPTIONS, assetProp: EAssetField.CAPTIONS, caption: 'auth_captions', type: AssetFieldType.FILE_UPLOAD, config: { fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.TEXT}},
      // Accessibility
      { id: EAssetField.DECORATOR, assetProp: EAssetField.DECORATOR, caption: 'auth_decorator', type: AssetFieldType.CHECKBOX, config: {}},
      { id: EAssetField.ALT_TEXT_EN, assetProp: EAssetField.ALT_TEXT_EN, caption: 'auth_alt_text_en', type: AssetFieldType.TEXT, config: {}},
      { id: EAssetField.ALT_TEXT_FR, assetProp: EAssetField.ALT_TEXT_FR, caption: 'auth_alt_text_fr', type: AssetFieldType.TEXT, config: {}},
      { id: EAssetField.SUBTITLES_EN, assetProp: EAssetField.SUBTITLES_EN, caption: 'auth_subtitles_en', type: AssetFieldType.FILE_UPLOAD, config: {fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.TEXT}},
      { id: EAssetField.SUBTITLES_FR, assetProp: EAssetField.SUBTITLES_FR, caption: 'Subtitles - French', type: AssetFieldType.FILE_UPLOAD, config: {fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.TEXT}},
      { id: EAssetField.AUDIO_TRANSCRIPT_EN, assetProp: EAssetField.AUDIO_TRANSCRIPT_EN, caption: 'auth_audio_transcript_en', type: AssetFieldType.FILE_UPLOAD, config: {fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.TEXT}},
      { id: EAssetField.AUDIO_TRANSCRIPT_FR, assetProp: EAssetField.AUDIO_TRANSCRIPT_FR, caption: 'auth_audio_transcript_fr', type: AssetFieldType.FILE_UPLOAD, config: {fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.TEXT}},
      { id: EAssetField.SPEAKER_NOTES_EN, assetProp: EAssetField.SPEAKER_NOTES_EN, caption: 'auth_speaker_notes_en', type: AssetFieldType.TEXTAREA, config: {}},
      { id: EAssetField.SPEAKER_NOTES_FR, assetProp: EAssetField.SPEAKER_NOTES_FR, caption: 'auth_speaker_notes_fr', type: AssetFieldType.TEXTAREA, config: {}},
      // Use in Assessments  
      { id: EAssetField.ITEMS, assetProp: EAssetField.ITEMS, caption: 'auth_items', type: AssetFieldType.TEXT, readOnly: true, config: {
        getDisplayValue: (asset: ILibraryAsset): string[] => {
            return asset[EAssetField.ITEMS] ? asset[EAssetField.ITEMS].map(i => `${i.id} - ${i.label}`) : [];
          }
      }},
      { id: EAssetField.ASSESSMENTS, assetProp: EAssetField.ASSESSMENTS, caption: 'auth_assessments_col', type: AssetFieldType.TEXT, readOnly: true},
      { id: EAssetField.ASSESSMENT_RELEASE_STATUS, assetProp: EAssetField.ASSESSMENT_RELEASE_STATUS, caption: 'auth_release_status', type: AssetFieldType.DROPDOWN, config: {options: ASSESSMENT_RELEASE_STATUS_OPTIONS}},
      { id: EAssetField.ASSESSMENT_RELEASE_YEAR, assetProp: EAssetField.ASSESSMENT_RELEASE_YEAR, caption: 'auth_assess_year_release', type: AssetFieldType.TEXT},
      { id: EAssetField.ASSESSMENT_TYPE, assetProp: EAssetField.ASSESSMENT_TYPE, caption: 'auth_assess_type', type: AssetFieldType.DROPDOWN, config: {options: ASSESSMENT_TYPE_OPTIONS}},
      { id: EAssetField.LANGUAGE, assetProp: EAssetField.LANGUAGE, caption: 'auth_language', type: AssetFieldType.DROPDOWN, config: {options: LANG_OPTIONS}},
      { id: EAssetField.DESCRIPTION, assetProp: EAssetField.DESCRIPTION, caption: 'test_auth_description', type: AssetFieldType.TEXTAREA, config: {placeholder: 'Description'}},
      // Copyright Info  
      { id: EAssetField.POINT_OF_CONTACT, assetProp: EAssetField.POINT_OF_CONTACT, caption: 'auth_point_of_contact', type: AssetFieldType.TEXTAREA, config: {placeholder: 'Point of Contact'}},
      { id: EAssetField.COPYRIGHT_ACQUISITION, assetProp: EAssetField.COPYRIGHT_ACQUISITION, caption: 'auth_copyright_acq_req', type: AssetFieldType.DROPDOWN, config: {options: YES_NO_OPTIONS}},
      { id: EAssetField.PRICE_PAID_PREVIOUS_YEARS, assetProp: EAssetField.PRICE_PAID_PREVIOUS_YEARS, caption: 'auth_price_paid_prev_years', type: AssetFieldType.TEXT, config: {placeholder: 'Price'}, secure: true},
      { id: EAssetField.PRICE_MOST_RECENT, assetProp: EAssetField.PRICE_MOST_RECENT, caption: 'auth_most_recent_price', type: AssetFieldType.TEXT, config: {placeholder: 'Price'}, secure: true},
      { id: EAssetField.BOOK_FRONT_COVER, assetProp: EAssetField.BOOK_FRONT_COVER, caption: 'auth_book_front_cover', type: AssetFieldType.FILE_UPLOAD, config: {preview: true, fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.IMAGE}},
      { id: EAssetField.BOOK_COPYRIGHT, assetProp: EAssetField.BOOK_COPYRIGHT, caption: 'auth_copyright', type: AssetFieldType.FILE_UPLOAD, config: {preview: true, fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.IMAGE}},
      { id: EAssetField.BOOK_ACKNOWLEDGEMENT, assetProp: EAssetField.BOOK_ACKNOWLEDGEMENT, caption: 'auth_acknowledgement_page', type: AssetFieldType.FILE_UPLOAD, config: {preview: true, fileTypes: this.allowedAssetFileExtensions.join(','), assetType: AssetTypes.IMAGE}},
      // License Details  
      { id: EAssetField.LICENSE_NOTES, assetProp: EAssetField.LICENSE_NOTES, caption: 'auth_licence_notes', type: AssetFieldType.TEXTAREA, config: {placeholder: 'Notes'}},
      { id: EAssetField.MEDIUM, assetProp: EAssetField.MEDIUM, caption: 'auth_medium', type: AssetFieldType.DROPDOWN, config: {options: MEDIUM_OPTIONS}},
      { id: EAssetField.NUM_PRINT_IMPRESSIONS, assetProp: EAssetField.NUM_PRINT_IMPRESSIONS, caption: 'auth_print_impressions', type: AssetFieldType.NUMBER},
      { id: EAssetField.ESTIMATED_TEST_TAKERS, assetProp: EAssetField.ESTIMATED_TEST_TAKERS, caption: 'auth_estimated_impressions', type: AssetFieldType.TEXT, readOnly: true},
      { id: EAssetField.TOTAL_TEST_TAKERS, assetProp: EAssetField.TOTAL_TEST_TAKERS, caption: 'auth_current_impressions', type: AssetFieldType.TEXT, readOnly: true},
      { id: EAssetField.VALID_FROM, assetProp: EAssetField.VALID_FROM, caption: 'auth_valid_from', type: AssetFieldType.DATE},
      { id: EAssetField.VALID_UNTIL, assetProp: EAssetField.VALID_UNTIL, caption: 'auth_valid_until', type: AssetFieldType.DATE},
      { id: EAssetField.ATTRIBUTION, assetProp: EAssetField.ATTRIBUTION, caption: 'auth_attribution', type: AssetFieldType.TEXT, config: {placeholder: 'Attribution'}},
      { id: EAssetField.STATUS_NOTES, assetProp: EAssetField.STATUS_NOTES, caption: 'auth_status_notes', type: AssetFieldType.TEXTAREA, config: {placeholder: 'Notes'}},
      { id: EAssetField.AMOUNT, assetProp: EAssetField.AMOUNT, caption: 'auth_amount', type: AssetFieldType.TEXT, config: {placeholder: 'Amount'}, secure: true},
      { id: EAssetField.TAX, assetProp: EAssetField.TAX, caption: 'auth_tax', type: AssetFieldType.TEXT, config: {placeholder: 'Tax'}, secure: true},
      { id: EAssetField.TOTAL, assetProp: EAssetField.TOTAL, caption: 'auth_total', type: AssetFieldType.TEXT, config: {placeholder: 'Total'}, secure: true},
      // Metadata Checklist 
      { id: EAssetField.ASSET_COPY_ATTACHED, assetProp: EAssetField.ASSET_COPY_ATTACHED, caption: 'auth_copy_attached', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.FORM_FULLY_COMPLETED, assetProp: EAssetField.FORM_FULLY_COMPLETED, caption: 'auth_form_fully_completed', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.BOOK_COVER_COPIED_ATTACHED, assetProp: EAssetField.BOOK_COVER_COPIED_ATTACHED, caption: 'auth_front_attached', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.BOOK_COPYRIGHT_COPIED_ATTACHED, assetProp: EAssetField.BOOK_COPYRIGHT_COPIED_ATTACHED, caption: 'auth_copyright_attached', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.BOOK_ACKNOWLEDGEMENT_COPIED_ATTACHED, assetProp: EAssetField.BOOK_ACKNOWLEDGEMENT_COPIED_ATTACHED, caption: 'auth_acknow_attached', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.WEB_ADDRESS_PROVIDED, assetProp: EAssetField.WEB_ADDRESS_PROVIDED, caption: 'auth_web_addr_prov', type: AssetFieldType.CHECKBOX},
      // Status
      { id: EAssetField.METADATA_COMPLETE, assetProp: EAssetField.METADATA_COMPLETE, caption: 'auth_meta_complete', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.SENT_FOR_APPROVAL, assetProp: EAssetField.SENT_FOR_APPROVAL, caption: 'auth_sent_approv', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.BC_MINISTRY_APPROVED, assetProp: EAssetField.BC_MINISTRY_APPROVED, caption: 'auth_approv_bc_min', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.LICENSE_HOLDER_PAID, assetProp: EAssetField.LICENSE_HOLDER_PAID, caption: 'auth_lic_hold_paid', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.REIMBURSEMENT_INVOICE_SENT, assetProp: EAssetField.REIMBURSEMENT_INVOICE_SENT, caption: 'auth_reimb_inv_sent', type: AssetFieldType.CHECKBOX},
      { id: EAssetField.SITTING_IN, assetProp: EAssetField.SITTING_IN, caption: 'auth_where_sitting', type: AssetFieldType.DROPDOWN, config: {options: SITTING_OPTIONS}},
      { id: EAssetField.STATUS, assetProp: EAssetField.STATUS, caption: 'auth_status_col', type: AssetFieldType.DROPDOWN, config: {options: STATUS_OPTIONS}},
      // Vretta Internal
      { id: EAssetField.GITLAB, assetProp: EAssetField.GITLAB, caption: 'GitLab', type: AssetFieldType.TEXTAREA, config: {placeholder: 'GitLab'}}
    ];

    return this.auth.apiGet(this.routes.TEST_AUTH_ASSET_LIBRARY_FIELDS, this.asset.library_id).then( (res) => {
      this.customFields = res.map( r => {
        let type = r.type;
        let config;
        console.log(r)
        if(type === AssetFieldType.IMAGE_UPLOAD) {
          type = AssetFieldType.FILE_UPLOAD;
          config = {preview: true, fileTypes: this.allowedImgFileExtensions.join(','), assetType: AssetTypes.IMAGE};
        } else if(type === AssetFieldType.FILE_UPLOAD) {
          type = AssetFieldType.FILE_UPLOAD;
          config = {preview: true, fileTypes: this.allowedAssetFileExtensions.join(','), captureImage: true}
          const assetType = getCustomAssetType(this.currentAsset, {customId: r.id, config, assetProp: r.prop});
          if(assetType) {
            config.assetType = assetType;
          }
        }

        return {
          id: r.slug,
          assetProp: r.prop,
          caption: r.caption,
          type,
          secure: !!r.is_secure,
          config,
          groupSlug: r.group_slug,
          customId: r.id
        }
      } )
      this.fields = this.fields.concat(this.customFields);

      // create field map
      this.fields.forEach(field => this.fieldsById.set(field.id, field))
      
      this.fields.forEach(field => {
        const fc = new FormControl();
        const prop = field.assetProp;
        if (prop) {
          if (field.type === AssetFieldType.DATE) {
            let value = new Date(getAssetVal(this.currentAsset, field));
            fc.setValue(moment(value).format('YYYY-MM-DD'));
          } else {
            fc.setValue(getAssetVal(this.currentAsset, field));
          }
        }
        field.formControl = fc;
        field.isEditing = false;
      })
    })
  }
  
  private initFieldGroups() {
    this.fieldGroups = this.assetLibrary.fieldGroups;

    const fgById : { [key:string]: IAssetFieldGroup} = {};

    for(const fg of this.fieldGroups) {
      fgById[fg.id] = fg;
    }

    fgById[AssetFieldGroup.ASSET_DESCRIPTION].fields = 
    [
          this.fieldsById.get(EAssetField.ASSET_NAME),
          this.fieldsById.get(EAssetField.ASSET_FILE),
          this.fieldsById.get(EAssetField.ASSET_FILE_FR),
          this.fieldsById.get(EAssetField.SUBMITTED_BY),
          this.fieldsById.get(EAssetField.DATE_OF_SUBMISSION),
          this.fieldsById.get(EAssetField.ASSET_TYPE),
          this.fieldsById.get(EAssetField.SNAPSHOTS),
          this.fieldsById.get(EAssetField.TAGS),
          this.fieldsById.get(EAssetField.ORIGINAL_TITLE),
          this.fieldsById.get(EAssetField.AUTHOR_NAME),
          this.fieldsById.get(EAssetField.PUBLICATION_NAME),
          this.fieldsById.get(EAssetField.PUBLICATION_AUTHOR),
          this.fieldsById.get(EAssetField.PUBLICATION_DATE),
          this.fieldsById.get(EAssetField.PUBLISHER_NAME),
          this.fieldsById.get(EAssetField.PUBLISHER_ADDRESS),
          this.fieldsById.get(EAssetField.WEB_ADDRESS),
          this.fieldsById.get(EAssetField.DATE_ACCESSED),
          this.fieldsById.get(EAssetField.CAPTIONS),
    ];

    fgById[AssetFieldGroup.ACCESSIBILITY].fields = [
          this.fieldsById.get(EAssetField.DECORATOR),
          this.fieldsById.get(EAssetField.ALT_TEXT_EN),
          this.fieldsById.get(EAssetField.ALT_TEXT_FR),
          this.fieldsById.get(EAssetField.SUBTITLES_EN),
          this.fieldsById.get(EAssetField.SUBTITLES_FR),
          this.fieldsById.get(EAssetField.AUDIO_TRANSCRIPT_EN),
          this.fieldsById.get(EAssetField.AUDIO_TRANSCRIPT_FR),
          this.fieldsById.get(EAssetField.SPEAKER_NOTES_EN),
          this.fieldsById.get(EAssetField.SPEAKER_NOTES_FR)
    ]

    fgById[AssetFieldGroup.USE_IN_ASSESSMENTS].fields = [
      this.fieldsById.get(EAssetField.ITEMS),
      this.fieldsById.get(EAssetField.ASSESSMENTS),
      this.fieldsById.get(EAssetField.ASSESSMENT_RELEASE_STATUS),
      this.fieldsById.get(EAssetField.ASSESSMENT_RELEASE_YEAR),
      this.fieldsById.get(EAssetField.ASSESSMENT_TYPE),
      this.fieldsById.get(EAssetField.LANGUAGE),
      this.fieldsById.get(EAssetField.DESCRIPTION)
    ]

    fgById[AssetFieldGroup.COPYRIGHT_INFO].fields = [
      this.fieldsById.get(EAssetField.POINT_OF_CONTACT),
      this.fieldsById.get(EAssetField.COPYRIGHT_ACQUISITION),
      this.fieldsById.get(EAssetField.PRICE_PAID_PREVIOUS_YEARS),
      this.fieldsById.get(EAssetField.PRICE_MOST_RECENT),
      this.fieldsById.get(EAssetField.BOOK_FRONT_COVER),
      this.fieldsById.get(EAssetField.BOOK_COPYRIGHT),
      this.fieldsById.get(EAssetField.BOOK_ACKNOWLEDGEMENT)
    ]

    fgById[AssetFieldGroup.LICENSE_DETAILS].fields = [
      this.fieldsById.get(EAssetField.LICENSE_NOTES),
      this.fieldsById.get(EAssetField.MEDIUM),
      this.fieldsById.get(EAssetField.NUM_PRINT_IMPRESSIONS),
      this.fieldsById.get(EAssetField.ESTIMATED_TEST_TAKERS),
      this.fieldsById.get(EAssetField.TOTAL_TEST_TAKERS),
      this.fieldsById.get(EAssetField.VALID_FROM),
      this.fieldsById.get(EAssetField.VALID_UNTIL),
      this.fieldsById.get(EAssetField.ATTRIBUTION),
      this.fieldsById.get(EAssetField.STATUS_NOTES),
      this.fieldsById.get(EAssetField.AMOUNT),
      this.fieldsById.get(EAssetField.TAX),
      this.fieldsById.get(EAssetField.TOTAL)
    ]

    fgById[AssetFieldGroup.METADATA_CHECKLIST].fields = [
      this.fieldsById.get(EAssetField.ASSET_COPY_ATTACHED),
      this.fieldsById.get(EAssetField.FORM_FULLY_COMPLETED),
      this.fieldsById.get(EAssetField.BOOK_COVER_COPIED_ATTACHED),
      this.fieldsById.get(EAssetField.BOOK_COPYRIGHT_COPIED_ATTACHED),
      this.fieldsById.get(EAssetField.BOOK_ACKNOWLEDGEMENT_COPIED_ATTACHED),
      this.fieldsById.get(EAssetField.WEB_ADDRESS_PROVIDED)
    ]

    fgById[AssetFieldGroup.STATUS].fields = [
      this.fieldsById.get(EAssetField.METADATA_COMPLETE),
      this.fieldsById.get(EAssetField.SENT_FOR_APPROVAL),
      this.fieldsById.get(EAssetField.BC_MINISTRY_APPROVED),
      this.fieldsById.get(EAssetField.LICENSE_HOLDER_PAID),
      this.fieldsById.get(EAssetField.REIMBURSEMENT_INVOICE_SENT),
      this.fieldsById.get(EAssetField.SITTING_IN),
      this.fieldsById.get(EAssetField.STATUS)
    ]

    fgById[AssetFieldGroup.VRETTA_INTERNAL].fields = [
      this.fieldsById.get(EAssetField.GITLAB)
    ]

    for(const field of this.customFields) {
      fgById[field.groupSlug].fields.push(field);
    }
  }
  
  groupExpanded(group: IAssetFieldGroup) {
    //console.log(`groupExpanded: ${group.caption}`)
  }

  groupCollapsed(group: IAssetFieldGroup) {
    //console.log(`groupCollapsed: ${group.caption}`)
  }

  renderUrl(url:string){
    return memo(this.santiziedUrls, url, url  => this.sanitizer.bypassSecurityTrustResourceUrl(url) );
  }

  reloadAssetPreview() {
    //console.log(`reloading asset preview...`)
  }

  exportCopyrightReport(){
    window.print()
  }

  getRevision(i) {
    return this.revisions.length-i
  }

  getCurrentRevision() {
    return this.revisions.length
  }

  assignButtonHandler($event) {
    this.memberAssignmentCtrl.openAssignUser({note: $event});
  }

  showAssetHistory = false;
  revisions = []
  loadAssetVersionHistory() {
    this.showAssetHistory = !this.showAssetHistory
    //Load asset versions
    this.auth.apiFind(this.routes.TEST_AUTH_ASSET_VERSIONS, {query: {asset_id: this.currentAsset.asset_id}})
    .then((res)=>{
      this.revisions = []
      for (let i = res.length-1;i>=0;i--) {
        if (i<res.length-1 && res[i]) {
          if (res[i].url == res[i+1].url) {
            continue;
          }
        }
        this.revisions.push(res[i])
      }
    })
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);
  
  loadAssetRevision(revision) {
    this.auth.apiGet(this.routes.TEST_AUTH_ASSET_VERSIONS, revision.id)
    .then((res)=>{
      console.log(this.currentAsset[EAssetField.ASSET_FILE], res[EAssetField.ASSET_FILE])
      this.currentAsset[EAssetField.ASSET_FILE] = res[EAssetField.ASSET_FILE]

    })
  }

  cancel() {
    console.log("cancel")
    if (this.revisions) {
      const obj = this.revisions[0]
      this.currentAsset[EAssetField.ASSET_FILE] = obj[EAssetField.ASSET_FILE]
    }
    this.showAssetHistory = false
  }

  restore() {
    console.log("restore")
    this.assetLibrary.saveAsset(this.currentAsset)
    .then((res)=>{
      console.log(res)
      this.loadAssetVersionHistory()
      this.initFields()
      this.reset()
    })
  }

  getDate(date) {
    const d = new Date(date)
    let str = d.toString()

    const nextStr = function() {
      str = str.substring(str.indexOf(' ')+1)
    }
    const dayOfWeek = str.substring(0,str.indexOf(' '))
    nextStr()
    const month = str.substring(0,str.indexOf(' '))
    nextStr()
    const dayOfMonth = str.substring(0,str.indexOf(' '))
    nextStr()
    const year = str.substring(0,str.indexOf(' '))
    nextStr()
    const time = str.substring(0,str.indexOf(' '))

    return dayOfWeek+" "+month+" "+dayOfMonth+" "+year+" "+time
  }
  
  expandAllGroups() {
    this.fieldGroups.forEach(g => g.isExpanded = true)  
  }
  
  collapseAllGroups() {
    this.fieldGroups.forEach(g => g.isExpanded = false)
  }

  assetTypeChanged() {
    this.fieldsById.get(EAssetField.ASSET_TYPE).formControl.setValue(this.currentAsset[EAssetField.ASSET_TYPE]);
  }

  getGroupId() {
    const library = this.assetLibraries.filter( l => l.id === this.asset.library_id)[0];
    return library.group_id;
  }

}
