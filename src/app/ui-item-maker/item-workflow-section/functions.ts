import {IStageAssignee} from './model'

/**
 * Ren<PERSON> how assignee appears in the menu options chip
 * @param assignee assignee info object
 * @returns string renpresenting HTML content
 */
export function renderAssigneeOption(assignee: IStageAssignee):string{
  return `<strong>${assignee.first_name} ${assignee.last_name}</strong><br/>${assignee.contact_email}`
}

/**
 * Ren<PERSON> how assignee appears in selected chip
 * @param assignee  assignee info object
 * @returns simple string
 */
export function renderAssigneeChip(assignee: IStageAssignee):string{
  return `${assignee.first_name} ${assignee.last_name} (${assignee.contact_email})`
}

/**
 * Returns latest filtered asignee list to show as options, when either typed user input changed or a new assignee is selected
 * @param value input - either string user input, or the assignee object last added as a chip
 * @param potentialAssignees - all availabe assignnee options
 * @returns list of assignee objects to be shown as dropdown options
 */
export function filterAssignees(value:string|IStageAssignee, potentialAssignees:IStageAssignee[]): IStageAssignee[] {
  // If triggered by new selection, return whole potential list
  if (typeof value === 'object') {
    return potentialAssignees
  }
  // Otherwise, process user string input
  // Return as options not yet selected assignees that are a match by name or email
  const filteredAssignees = [];
  const userString = value.toLowerCase();
  for(let potentialAssignee of potentialAssignees) {
    if((potentialAssignee.first_name + " " + potentialAssignee.last_name)?.toLowerCase().includes(userString)) {
      filteredAssignees.push(potentialAssignee);
    }
    else if(potentialAssignee.contact_email?.toLowerCase().includes(userString)) {
      filteredAssignees.push(potentialAssignee);
    }
  }
  return filteredAssignees;
}