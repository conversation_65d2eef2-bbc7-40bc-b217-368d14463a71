.text-area-middle * {
    vertical-align: middle;
}

.flex-gap {
    display: inline-flex;
    gap: 1em;
}

.hasBorder {
    border-style: solid;
    border-width: 0.1em;
}

.ml {
    margin-left: 1em;
}

.mb {
    margin-bottom: 0.5em;
}

.audit-log-list {
    border: none;
    margin: 0.4em 0;
    tr{
        display: flex; 
        border: none;
        padding: 0.2em;
        th, td {
            border: none;
            padding: 0;
        }
        th {
            width: 6em;
        }
        td {
            overflow: wrap;
            font-weight: 400;
        }
    }
}
.is-disabled{
    th{
        opacity: 0.2;
    }
}
.audit-table-run-audit-buttons{
    display: flex;
    flex-direction: column;
    button {
        margin-top: 0.5em;
        &:first-child {
            margin-top: 0;
        }}

}

th{
    border-width: 1px;
}

table{
    margin-top: -0.5em;
}