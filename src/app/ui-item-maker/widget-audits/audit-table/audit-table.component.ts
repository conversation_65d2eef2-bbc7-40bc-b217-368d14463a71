import { Component, Input, OnInit } from '@angular/core';
import { ItemBankAuditor } from '../../item-set-editor/controllers/audits';
import { AuditConfigs, AuditQuestionScope, AuditTarget, AuditType, IAuditConfig } from '../data/audits';
import { AssetLibraryCtrl } from '../../item-set-editor/controllers/asset-library';
import { EditingDisabledService } from '../../editing-disabled.service';

@Component({
  selector: 'audit-table',
  templateUrl: './audit-table.component.html',
  styleUrls: ['./audit-table.component.scss']
})
export class AuditTableComponent implements OnInit {
  
  @Input() auditCtrl:ItemBankAuditor;
  @Input() assetLibraryCtrl:AssetLibraryCtrl;
  @Input() auditType: AuditType;

  auditScopeDictionary: { [key in AuditQuestionScope]: string } = {
    'ITEMS_SCORED': 'audit_assessment',
    'ITEMS_SCORED_MINUS_FT': 'audit_assessment',
    'ITEMS_HUMAN_SCORED': 'audit_assessment',
    'ITEMS_SCORED_MINUS_HS': 'audit_assessment',
    'ITEMS_SURVEY': 'audit_assessment',
    'SCREENS': 'audit_assessment',
    'SCREENS_NONSCORED': 'audit_assessment',
    'SCREENS_PASSAGES': 'audit_assessment',
    'SCREENS_NON_PASSAGES': 'audit_assessment',
    'ITEM_BANK': 'ie_audit_item_bank',
    'ITEM_BANK_SCORED': 'ie_audit_item_bank',
    'SCREENS_INCLUDE_DISABLED': 'audit_assessment_disabled',
    'PASSAGE_ITEMS': 'audit_assessment',
    'ITEM_BANK_PASSAGES': 'ie_audit_item_bank'
  }; // slugs for translation
  audits:IAuditConfig[];
  auditCount = 0;
  autoFixSet = new Set()
  AuditTarget = AuditTarget

  constructor(
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {

    this.audits = AuditConfigs.filter(audit => {
      if(!this.auditType) return !audit.isHidden
      else return audit.auditType === this.auditType && !audit.isHidden
    });
    this.setAuditAutoFixSet();
  }

  setAuditAutoFixSet(){
    this.audits.forEach(audit =>{
      audit.checks?.forEach(c =>{
        if(c.autoFix){
          this.autoFixSet.add(c.id)
        }
      })
    })
  }

  getNumberOfIssues(slug:string){
    return this.auditCtrl.getAuditLog(slug)?.num_issues ?? 0
  }
  
  activateAuditItems(auditSlug:string, resultId:string, auditTarget:AuditTarget){
    const currentItemMemId = this.auditCtrl.activeAuditItemMemId
    // Clear previous values
    this.clearPreviousListedValue();
    // if clicking on same item list clear and return
    const slug = this.renderAuditResultSlug(auditSlug, resultId);
    if(currentItemMemId === slug){
      // this is technically being done in clearPreviousListedValue but adding here to be explicit
      this.auditCtrl.activeAuditItemMemId = null;
      return
    }
    switch(auditTarget){
      case AuditTarget.MSCAT_PANELS:
        this.auditCtrl.activateAuditPanels(slug);
        break;
      case AuditTarget.QUESTIONS:
      default:
        this.auditCtrl.activateAuditQuestions(slug);
        break;

    }
  }

  clearPreviousListedValue(){
    this.auditCtrl.clearAuditQuestions();
    this.auditCtrl.clearAuditPanels();
    this.auditCtrl.auditItemListingMode = null;
  }

  checkActiveQuestionMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.checkActiveQuestionMem(slug)
  }
  renderAuditResultSlug(auditSlug:string, resultId:string){
    return auditSlug+'_'+resultId
  }
  canFixAuditQuestions(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)

  }

  getAuditQMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.auditQuestionMem[slug]
  }
  canSelectAuditQuestions(auditSlug:string, resultId:string){
    const auditQMem = this.getAuditQMem(auditSlug, resultId)
    return (auditQMem && auditQMem.length > 0)
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);
  resetDiffLangs() {
    this.auditCtrl.auditsRan['isDiffEnglish'] = false;
    this.auditCtrl.auditsRan['isDiffFr'] = false;
  }
  
  toggleBothLangs() {
    this.resetDiffLangs();
  }
  toggleDiffEn() {
    this.resetDiffLangs();
    this.auditCtrl.auditsRan['isDiffEnglish'] = !this.auditCtrl.auditsRan['isDiffEnglish'];
  }
  toggleDiffFr() {
    this.resetDiffLangs();
    this.auditCtrl.auditsRan['isDiffFr'] = !this.auditCtrl.auditsRan['isDiffFr'];
  }

}
