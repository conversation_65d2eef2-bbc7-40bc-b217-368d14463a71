<table class="audit-table">
<!-- Brought over from ABED before each audit was hard coded now they are generated programatically -->
  <ng-container *ngFor="let audit of audits; let idx = index">
    <tr *ngIf="!audit.isHidden" [class.is-disabled]="audit.isDisabled">

      <th class="audit-table-col-num"> {{idx + 1}} </th>
      <th class="audit-table-col-name"> 
        {{audit.caption}} 
        <p *ngIf="auditCtrl.auditsWithError[audit.slug]" style="color: red; font-size: x-small;">Error Running</p>
      </th>
      <th class="audit-table-col-btn">
        <div class="audit-table-run-audit-buttons">
          <button
            *ngFor="let scope of audit.itemScope"
            (click)="auditCtrl.runCustomAudit(audit.slug, scope, audit.isLangSensitive, audit.auditTarget)" 
            class="button is-small is-light"
            [class.is-info]="auditCtrl.auditsRunning[audit.slug]"
            [disabled]="audit.isDisabled || auditCtrl.auditsRan[audit.slug] || isReadOnly() || auditCtrl.auditsRunning[audit.slug]" 
          > <tra [slug]="auditScopeDictionary[scope]"></tra> </button>
        </div>
      </th>
      <th style="border-width: 1px; width: 18em">
        <span style="display: flex; flex-direction: column;">
          <span *ngIf="auditCtrl.getAuditLog(audit.slug)">
            <table class="audit-log-list">
              <tr [style.color]="(auditCtrl.getAuditLog(audit.slug).is_new) ? 'green' : 'initial'">
                <th><tra slug="pre_publish_audits_audited_on"></tra></th> 
                <td>
                  {{auditCtrl.getAuditLog(audit.slug).audited_on?.toLocaleString()}}
                </td>
              </tr>
              <tr>
                <th><tra slug="pre_publish_audits_audited_by"></tra></th>
                <td>
                  {{auditCtrl.getAuditLog(audit.slug)?.name}}<br>
                  <a [href]="'mailto:'+ auditCtrl.getAuditLog(audit.slug)?.email">
                    {{auditCtrl.getAuditLog(audit.slug)?.email}}
                  </a>
                </td>
              </tr>
              <tr [ngStyle]="{'color': auditCtrl.getAuditLog(audit.slug).num_issues ? 'red' : 'initial'}">
                <th><tra slug="pre_publish_audits_number_of_issues"></tra></th> 
                <td>{{getNumberOfIssues(audit.slug)}}</td>
              </tr>
            </table>
          </span>
          <span *ngIf="!auditCtrl.getAuditLog(audit.slug)">
            <b><tra slug="pre_publish_audit_no_logs_found"></tra></b>
          </span>
          <span *ngIf="auditCtrl.auditsRan[audit.slug]" style="display: flex; justify-content: center;">
            <button  class="button is-small is-danger" (click)="auditCtrl.refreshAudit([audit.slug])" [disabled]="isReadOnly()" > 
              <span><tra slug="ie_refresh"></tra></span>
            </button>
          </span>
        </span>
      </th>
      <td class="audit-table-col-result">
        <span *ngIf="auditCtrl.auditsRan[audit.slug]">
          <p style="padding-left: 1em;" [ngStyle]="{'color': getNumberOfIssues(audit.slug) ? 'red' : 'initial'}">
            <tra slug="ie_issues_detected"></tra>: {{getNumberOfIssues(audit.slug)}}
          </p>
          <ng-container *ngIf="audit.autoFixes">
            <div>
              <button  
                class="button is-small is-warning has-icon" 
                (click)="auditCtrl.runCustomCleaningPatch(audit, audit.autoFixes.slug)" 
                [disabled]="auditCtrl.auditQuestionMem[audit.autoFixes.slug] || isReadOnly()" 
              > 
                <span class="icon"><i class="fa fa-wrench" aria-hidden="true"></i> </span>
                <span><tra [slug]="audit.autoFixes.caption ? audit.autoFixes.caption : 'ie_apply_auto_fix'"></tra></span>
              </button>
              <span *ngIf="auditCtrl.auditQuestionMem[audit.autoFixes.slug]">
                <tra slug="ie_applying_auto_fix"></tra> {{auditCtrl.auditQuestionMem[audit.autoFixes.slug].i}} / {{auditCtrl.auditQuestionMem[audit.autoFixes.slug].n}}
              </span>
            </div>
          </ng-container>
  
          <ul *ngIf="!audit.isCustomResult">
            <li *ngFor="let result of auditCtrl.auditQuestionMem[audit.slug+'_RESULTS']">
              <button 
                class="button is-small has-icon" 
                (click)="activateAuditItems(audit.slug, result.id, audit.auditTarget)" 
                [class.is-info]="checkActiveQuestionMem(audit.slug, result.id)"
                style="vertical-align: middle;"
                [disabled]="!canSelectAuditQuestions(audit.slug,result.id)"
              >
                <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                <span>{{result.items.length}}
                  <ng-container [ngSwitch]="audit.auditTarget">
                    <ng-container *ngSwitchCase="AuditTarget.MSCAT_PANELS">
                      <tra slug="ie_panel"></tra> 
                    </ng-container>
                    <ng-container *ngSwitchDefault>
                      <tra slug="ie_questions"></tra> 
                    </ng-container>
                  </ng-container>
                </span>
              </button>
              <span [style]="result.items.length && !result.informativeOnly? 'color: red': ''">
                {{result.caption}}
              </span>
              <i *ngIf="autoFixSet.has(result.id)" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
            </li>
          </ul>
  
          <ng-container *ngIf="audit.isCustomResult" >
            <div *ngIf="audit.isContentDiff">
              <h4 style="font-weight: 800; color: red;">{{auditCtrl.auditResultHeader}}</h4>
              <label> Only show different content:
                <!-- todo: consider attaching this model to a different object -->
                <input  type="checkbox" [(ngModel)]="auditCtrl.auditsRan['hideSameContent']">
              </label>
              <div>
                <button class="button is-small" [class.is-info]="!auditCtrl.auditsRan['isDiffEnglish'] && !auditCtrl.auditsRan['isDiffFr']" (click)="resetDiffLangs()">EN | FR</button>
                <button class="button is-small" [class.is-info]="auditCtrl.auditsRan['isDiffEnglish']" (click)="toggleDiffEn()">EN</button>
                <button class="button is-small" [class.is-info]="auditCtrl.auditsRan['isDiffFr']
                " (click)="toggleDiffFr()">FR</button>
              </div>
              <ul>
                <div *ngFor="let qDiff of auditCtrl.auditQuestionMem[audit.slug]" style="margin-bottom: 0.5em;">
                  <ng-container *ngIf = "
                    (auditCtrl.auditsRan['hideSameContent'] ? qDiff.hasDiff : true) &&
                    (
                      !auditCtrl.auditsRan['isDiffEnglish'] && !auditCtrl.auditsRan['isDiffFr'] || 
                      auditCtrl.auditsRan['isDiffEnglish'] && qDiff.lang == 'en' ||
                      auditCtrl.auditsRan['isDiffFr'] && qDiff.lang == 'fr'
                    )
                  ">
                    <widget-item-diff
                      *ngIf="audit.slug == 'CONTENT_DIFF_AUDIT'"
                      [questionId]= "qDiff.questionId"
                      [questionLabel] = "qDiff.questionLabel"
                      [description] = "qDiff.description"
                      [content] = "qDiff.content"
                      [style] = "qDiff.style"
                      [currentContent]="qDiff.currentContent"
                      [previousContent]="qDiff.previousContent"
                      [itemBankCtrl]="auditCtrl.itemBankCtrl"
                      [lang] = "qDiff.lang"
                    >
                    </widget-item-diff>
                  <!-- 18 has not yet been updated, preserving old view -->
                    <expansion-panel
                      *ngIf="audit.slug != 'CONTENT_DIFF_AUDIT'"
                      [title]= "qDiff.title"
                      [subTitle] = "qDiff.subTitle"
                      [description] = "qDiff.description"
                      [content] = "qDiff.content"
                      [style] = "qDiff.style"
                    ></expansion-panel>
                  </ng-container>
                </div>
              </ul>
            </div>
  
            <ng-container [ngSwitch]="audit.slug">
              <div *ngSwitchCase="'QUESTION_UTIL'">
                <ul>
                  <li>
                    <button 
                    class="button is-small has-icon" 
                    (click)="auditCtrl.activateAuditQuestions('UTIL_QUAD')" 
                    [class.is-info]="auditCtrl.checkActiveQuestionMem('UTIL_QUAD')"
                    [disabled]="auditCtrl.auditQuestionMem['NUM_Q_NO_QUAD'] == 0 || isReadOnly()"
                    >
                      <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                      <span>{{auditCtrl.auditQuestionMem['NUM_Q_NO_QUAD']}} <tra slug="audit_pages"></tra></span>
                    </button>  
                    <tra slug="ie_do_not_belong_2"></tra>
                  </li>
                  <li>
                    <button 
                    class="button is-small has-icon" 
                    (click)="auditCtrl.activateAuditQuestions('UTIL_TESTLET')" 
                    [class.is-info]="auditCtrl.checkActiveQuestionMem('UTIL_TESTLET')"
                    [disabled]="auditCtrl.auditQuestionMem['NUM_Q_NO_TESTLET'] == 0 || isReadOnly()"
                    >
                      <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                      <span>{{auditCtrl.auditQuestionMem['NUM_Q_NO_TESTLET']}} <tra slug="audit_pages"></tra></span>
                    </button>
                    <tra slug="ie_do_not_appear_2"></tra>
                  </li>
                </ul>
              </div>
              <div *ngSwitchCase="'QUESTION_LABEL'">
                <button 
                  class="button is-small has-icon" 
                  (click)="auditCtrl.activateAuditQuestions('Q_LABEL_DUPE')" 
                  [class.is-info]="auditCtrl.checkActiveQuestionMem('Q_LABEL_DUPE')"
                  [disabled]="auditCtrl.auditQuestionMem['NUM_Q_LABEL_DUPE'] == 0 || isReadOnly()"
                >
                  <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                  <span>{{auditCtrl.auditQuestionMem['NUM_Q_LABEL_DUPE']}} <tra slug="audit_pages"></tra> </span>
                </button>
                <tra slug="ie_overlapping_label"></tra>
              </div>
              <div *ngSwitchCase="'EXP_ANS'">
                <ul *ngIf="auditCtrl.auditQuestionMem['EXP_ANS_CATEGORIES']">
                  <li *ngFor="let category of auditCtrl.auditQuestionMem['EXP_ANS_CATEGORIES']">  
                    <button 
                    class="button is-small has-icon" 
                    (click)="auditCtrl.activateAuditQuestions(category.key)" 
                    [class.is-info]="auditCtrl.checkActiveQuestionMem(category.key)"
                    [disabled]="auditCtrl.auditQuestionMem['NUM_'+category.key] == 0 || isReadOnly()"
                    >
                    <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                    <span>{{auditCtrl.auditQuestionMem['NUM_'+category.key]}} <tra slug="auth_questions"></tra></span>
                  </button> 
                  <span>{{category.description}}</span>
                  <i *ngIf="category.isFixable" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
                </li>
                </ul>
              </div>
              <div *ngSwitchCase="'CLONES'">
                <ul *ngIf="auditCtrl.auditQuestionMem['CLONES_CATEGORIES']">
                  <li *ngFor="let category of auditCtrl.auditQuestionMem['CLONES_CATEGORIES']">  
                    <button 
                    class="button is-small has-icon" 
                    (click)="auditCtrl.activateAuditQuestions(category.key)" 
                    [class.is-info]="auditCtrl.checkActiveQuestionMem(category.key)"
                    [disabled]="auditCtrl.auditQuestionMem[category.key].length == 0 || isReadOnly()"
                    >
                    <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                      <span>{{auditCtrl.auditQuestionMem[category.key].length}} <tra slug="auth_questions"></tra></span>
                    </button> 
                    <span>{{category.description}}</span>
                    <i *ngIf="category.isFixable" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
                  </li>
                </ul>
              </div>
              <div *ngSwitchCase="'ENTRY_ID'">
                <ul *ngIf="auditCtrl.auditQuestionMem['ENTRY_ID_CATEGORIES']">
                  <li *ngFor="let category of auditCtrl.auditQuestionMem['ENTRY_ID_CATEGORIES']">  
                    <button 
                    class="button is-small has-icon" 
                    (click)="auditCtrl.activateAuditQuestions(category.key)" 
                    [class.is-info]="auditCtrl.checkActiveQuestionMem(category.key)"
                    [disabled]="auditCtrl.auditQuestionMem['NUM_'+category.key] == 0 || isReadOnly()"
                    >
                    <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                    <span>{{auditCtrl.auditQuestionMem['NUM_'+category.key]}} <tra slug="auth_questions"></tra></span>
                  </button> 
                  <span>{{category.description}}</span>
                  <i *ngIf="category.isFixable" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
                </li>
              </ul>
              </div>
              <div *ngSwitchCase="'VOICEOVER'">
                <ul *ngIf="auditCtrl.auditQuestionMem['VOICEOVER_CATEGORIES']">
                  <li *ngFor="let category of auditCtrl.auditQuestionMem['VOICEOVER_CATEGORIES']">  
                    <button 
                      class="button is-small has-icon" 
                      (click)="auditCtrl.activateAuditQuestions(category.key)" 
                      [class.is-info]="auditCtrl.checkActiveQuestionMem(category.key)"
                      [disabled]="auditCtrl.auditQuestionMem['NUM_'+category.key] == 0 || isReadOnly()"
                    >
                      <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                      <span>{{auditCtrl.auditQuestionMem['NUM_'+category.key]}} <tra slug="auth_questions"></tra></span>
                    </button> 
                    <span>{{category.description}}</span>
                   <i *ngIf="category.isFixable" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
                  </li>
                </ul>
              </div>
              <div *ngSwitchCase="'COMMENTS'">
                <ul>
                  <li>
                    <div style="display: flex; align-items: center">
                      <button 
                      [class.is-info]="auditCtrl.checkActiveQuestionMem('QS_W_OUTSTANDING_CMTS')"
                      (click)="auditCtrl.activateAuditQuestions('QS_W_OUTSTANDING_CMTS')"
                      class="button is-small has-icon" 
                      [disabled]="auditCtrl.auditQuestionMem['NUM_QS_W_OUTSTANDING_CMTS'] == 0 || isReadOnly()"
                      >
                        <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                        <span>{{auditCtrl.auditQuestionMem['NUM_QS_W_OUTSTANDING_CMTS']}} items</span>
                      </button>  
                      <span [ngStyle]="{'color': getNumberOfIssues(audit.slug) ? 'red' : 'initial'}">have outstanding comments</span>
                    </div>
                  </li>
                  <li>
                    <div style="display: flex; align-items: center">
                      <button 
                      class="button is-small has-icon" 
                      (click)="assetLibraryCtrl.openAssetLibraryToIds(auditCtrl.auditQuestionMem['ASSETS_W_OUTSTANDING_CMTS'])"
                      [disabled]="auditCtrl.auditQuestionMem['NUM_ASSETS_W_OUTSTANDING_CMTS'] == 0 || isReadOnly()"
                      >
                        <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                        <span>{{auditCtrl.auditQuestionMem['NUM_ASSETS_W_OUTSTANDING_CMTS']}} assets</span>
                      </button>  
                      <span [ngStyle]="{'color': getNumberOfIssues(audit.slug) ? 'red' : 'initial'}" >have outstanding comments</span>
                    </div>
                  </li>
                </ul>
              </div>
              <div *ngSwitchCase="'MULTIMEDIA_ASSET_AUDIT'">
                <h4 style="font-weight: 800; color: red;">{{auditCtrl.auditResultHeader}}</h4>
                <ul>
                  <div *ngFor="let remodeledResults of auditCtrl.auditQuestionMem[audit.slug]" style="margin-bottom: 0.5em;">
                        <expansion-panel
                        [title]= "remodeledResults.title"
                        [subTitle] = "remodeledResults.subTitle"
                        [description] = "remodeledResults.description"
                        [content] = "remodeledResults.content"
                        [style] = "remodeledResults.style"
                      ></expansion-panel>
                  </div>
                </ul>
              </div>
            </ng-container>
  
          </ng-container>
        </span>
      </td>
    </tr>
  </ng-container>

</table>
