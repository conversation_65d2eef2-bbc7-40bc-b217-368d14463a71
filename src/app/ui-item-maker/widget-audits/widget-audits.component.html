<twiddle class="top-twiddle" [isSectionHeader]='true' caption="ie_audit_data_validation" [state]="dataAuditState"></twiddle>
<audit-table
*ngIf="dataAuditState.value"
[auditCtrl]="auditCtrl"
[assetLibraryCtrl]="assetLibraryCtrl"
auditType="DATA"
></audit-table>
<twiddle [isSectionHeader]='true' caption="ie_audit_content_validation" [state]="contentAuditState"></twiddle>
<audit-table
*ngIf="contentAuditState.value"
[auditCtrl]="auditCtrl"
[assetLibraryCtrl]="assetLibraryCtrl"
auditType="CONTENT"
></audit-table>
<div style="margin-top: 2em;">
    <widget-mscat-panels
    *ngIf="auditCtrl.activeAuditPanelIds"
    [previewCtrl]="previewCtrl"
    [panelCtrl]="panelCtrl"
    [printViewCtrl]="printViewCtrl"
    [frameworkCtrl]="frameworkCtrl"
    [showTableOnly]="true"
    [activeIds]="auditCtrl.activeAuditPanelIds"
    >
    </widget-mscat-panels>
</div>
