import {Component, Input, OnInit} from '@angular/core';

// app services
import {EditingDisabledService} from '../editing-disabled.service';
import {ItemSetPreviewCtrl} from '../item-set-editor/controllers/preview';
import {ItemBankSaveLoadCtrl} from '../item-set-editor/controllers/save-load';
import {AssetLibraryCtrl} from '../item-set-editor/controllers/asset-library';
import {ItemSetFrameworkCtrl} from '../item-set-editor/controllers/framework';
import {ItemBankAuditor} from '../item-set-editor/controllers/audits';
import {ItemEditCtrl} from '../item-set-editor/controllers/item-edit';
import {ItemBankCtrl} from '../item-set-editor/controllers/item-bank';
import {MemberAssignmentCtrl} from '../item-set-editor/controllers/member-assignment';
import {ItemFilterCtrl} from '../item-set-editor/controllers/item-filter';
import {PanelCtrl} from '../item-set-editor/controllers/mscat';
import {ItemSetPrintViewCtrl} from '../item-set-editor/controllers/print-view';
import {ItemSetPublishingCtrl} from '../item-set-editor/controllers/publishing';
import {FrameworkQuadrantCtrl} from '../item-set-editor/controllers/quadrants';
import {TestFormGen} from '../item-set-editor/controllers/testform-gen';
import {TestletCtrl} from '../item-set-editor/controllers/testlets';
import {IAuditConfig, AuditConfigs, AuditQuestionScope, EAuditElementType} from './data/audits';
import { FormControl } from '@angular/forms';


// File brought over fully from ABED
@Component({
  selector: 'widget-audits',
  templateUrl: './widget-audits.component.html',
  styleUrls: ['./widget-audits.component.scss']
})
export class WidgetAuditsComponent implements OnInit {


  @Input() auditCtrl:ItemBankAuditor
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() itemEditCtrl: ItemEditCtrl
  @Input() previewCtrl: ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  
  dataAuditState = new FormControl(true);
  contentAuditState = new FormControl(true);

  // This is to allow item scopes to be changed to a tra slug so it can be displayeed
  auditScopeDictionary: { [key in AuditQuestionScope]: string } = {
    'ITEMS_SCORED': 'audit_assessment',
    'ITEMS_SCORED_MINUS_FT': 'audit_assessment',
    'ITEMS_HUMAN_SCORED': 'audit_assessment',
    'ITEMS_SCORED_MINUS_HS': 'audit_assessment',
    'ITEMS_SURVEY': 'audit_assessment',
    'SCREENS': 'audit_assessment',
    'SCREENS_NONSCORED': 'audit_assessment',
    'SCREENS_PASSAGES': 'audit_assessment',
    'SCREENS_NON_PASSAGES': 'audit_assessment',
    'ITEM_BANK': 'ie_audit_item_bank',
    'ITEM_BANK_SCORED': 'ie_audit_item_bank',
    'SCREENS_INCLUDE_DISABLED': 'audit_assessment_disabled',
    'PASSAGE_ITEMS': 'audit_assessment',
    'ITEM_BANK_PASSAGES': 'ie_audit_item_bank'
  }; // slugs for translation
  audits:IAuditConfig[];

  constructor(
  ) { }

  ngOnInit(): void {
    this.auditCtrl.getAuditLogs();
  }
}
