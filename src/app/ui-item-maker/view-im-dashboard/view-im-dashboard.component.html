<div (click)="hideAllDropdowns()" class="page-body">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    ></header>


    <div class="page-content is-fullpage" >
      <h2><tra slug="auth_assessment_authoring"></tra></h2>

        <div *ngIf="isNotGroupView()" class="search-subpanel">
          <h4>
            <tra slug="auth_content_search"></tra>
          </h4>
          <div class="space-between">
            <div>
              <div>
                <div class="field has-addons" style="margin-bottom: 0; width: 28em">
                  <div class="control" style="width: 80%">
                    <input #tagInput 
                    matInput 
                    (keydown.enter)="contentSearch()" 
                    class="input" 
                    type="text" 
                    [placeholder]="lang.tra('Search items for tags/content')" 
                    [formControl]="contentSearchQuery"
                    [matAutocomplete]="auto">
                  </div>
                  <div class="control" style="width: 20%">
                    <a class="button is-info" (click)="contentSearch()">
                      <i class="fa fa-search"></i>
                    </a>
                  </div>
                </div>
                <mat-autocomplete #auto="matAutocomplete">
                  <mat-option *ngFor="let tag of filteredTags | async" (click)="addTag(tag)" [value]="tag.caption">
                    {{tag.caption}}
                  </mat-option>
                </mat-autocomplete>
              </div>
              <div style="display: flex; flex-wrap: wrap">
                <ng-container *ngFor="let tag of searchTags"> 
                    <item-tag (delete)="deleteTag(tag)" [tag]="tag"></item-tag>
                </ng-container>
              </div>
            </div>
            <div> 
              <label>
                <input type="checkbox" [(ngModel)]="isItemSearchById"> Search by Item ID (primary key used for psych and data)
              </label>
              <br>
              <label>
                <input type="checkbox" [(ngModel)]="isTestDesignSearchById"> Search by Test Design Published ID
              </label>
            </div>
          </div>
          <ng-container *ngIf="searchItems && searchItems.length">
            <table class="table is-hoverable" style="width:auto; margin-top:1em;">
              <tr>
                <th colspan="2"><tra slug="auth_cs_asmt"></tra></th>
                <th><tra slug="auth_cs_page"></tra></th>
              </tr>
              <tr *ngFor="let item of searchItems">
                <td>
                  {{item.asmt_slug}}
                </td>
                <td>
                  {{item.asmt_name}}
                </td>
                <td>
                  <a [routerLink]="getItemRoute(item)">{{item.question_label}}</a>
                </td>
              </tr>
            </table>
          </ng-container>
          <hr>
        </div>


        <div *ngIf="view && isNotGroupView()" >
          <div style="margin-bottom:0.5em">
            Which assessment authoring group are you working on?
          </div>
          <div class="buttons">
            <button *ngFor="let group of getAuthoringGroups()"
              class="button is-small"
              [class.is-info]="isGroupToggled(group.group_id)"
              (click)="toggleGroupFilter(group.group_id)"
            >
              {{group.description}}
            </button>
          </div>
          <hr/>
        </div>

        <div *ngIf="!view">
          You can use this site to <a>create and edit assessment designs and forms</a> or <a>create an edit item sets</a>.
          <p>
            <tra slug="test_auth_asset_libraries_description"></tra>
          </p>
          <div>
            <a routerLink="/{{lang.c()}}/test-auth/asset-library"><tra slug="test_auth_manage_libraries"></tra></a>
          </div>
          <div>
            <a [routerLink]="getCommentsRoute()"><tra slug="test_auth_outstanding_issues"></tra></a>
          </div>
        </div>

        <div *ngIf="view == 'designs' ">
          <div class="space-between">
            <h2>Assessment Designs & Forms</h2>
            <div class="input-container">
              <input class="input" type="text" placeholder="Filter" [(ngModel)]="itemBankDescrFilter" (input)="applyItemSetFilters()" (change)="applyItemSetFilters()">
              <i class="fa fa-search input-icon"></i>
            </div>
          </div>
          <div style="margin-top:1em;">
            <label>
              <input type="checkbox" [(ngModel)]="isShowDetails"> <tra slug="auth_ass_details"></tra>
            </label>
          </div>
          <table *ngIf='!viewArchivedLoading' class="table is-hoverable" style="width:auto; margin-top:1em;">
            <tr>
              <th><tra slug="test_auth_name_col"></tra></th>
              <th><tra slug="test_auth_description_col"></tra></th>
              <th><tra slug="test_auth_group"></tra></th>
              <th></th>
            </tr>
            
            <tr *ngFor="let questionSet of frameworks">
              <td> <strong>{{questionSet.slug}}</strong> </td>
              <td>
                <p>
                  <a [class.is-disabled]="viewArchived || viewArchivedLoading" [routerLink]="getFrameworkRoute(questionSet.id)" >{{questionSet.name}}</a>
                </p>
                <div class="asmt-detail" [class.is-hidden]="!isShowDetails" [class.is-tables-active]="isShowDetailsTable">
                  <tra-md [slug]="questionSet.description"></tra-md>
                </div>
              </td>
              <td>
                <span [ngSwitch]="checkPersonalQuestionSet(questionSet)">
                  <span *ngSwitchCase="false" class="tag">{{getGroupNameById(questionSet.group_id)}}</span>
                  <span *ngSwitchCase="true" class="tag is-warning"><em><tra slug="test_auth_personal"></tra></em></span>
                </span>
              </td>
              <td>
                <div *ngIf="viewArchived && !viewArchivedLoading">
                  <button class="dropdown" [disabled]="!checkQuestionSetSuperRole(questionSet)" (click)="recoverItemBank(questionSet)" [class.is-disabled]="isArchivingItemBank">Recover</button> <!--TODO Add translation Slug-->
                </div>
                <div *ngIf="!viewArchived && !viewArchivedLoading" class="dropdown" [class.is-active]="isActionDropdownActive(questionSet.id)">
                  <div class="dropdown-trigger">
                    <button (click)="toggleActionDropdown(questionSet.id, $event)" class="button is-info" aria-haspopup="true" aria-controls="dropdown-menu">
                      <span>Action</span>
                      <span class="icon is-small">
                        <i class="fas fa-angle-down" aria-hidden="true"></i>
                      </span>
                    </button>
                  </div>
                  <div class="dropdown-menu" id="dropdown-menu" role="menu">
                    <div class="dropdown-content">
                      <a class="dropdown-item" [routerLink]="getQuestionSetRoute(questionSet.id)">View/Edit</a>
                      <a class="dropdown-item" *ngIf="checkQuestionSetSuperRole(questionSet)" (click)="openItemBankAccessModal(questionSet)"><tra slug="auth_manage_access"></tra></a> 
                      <a class="dropdown-item" [routerLink]="getNotificationsRouterLink(questionSet)"><tra slug="auth_view_notifications"></tra></a>
                      <a class="dropdown-item" *ngIf="checkQuestionSetSuperRole(questionSet)" (click)="archiveItemBank(questionSet)" [class.is-disabled]="isArchivingItemBank">Archive</a>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </table>
          <div style="margin-top: 0.5em;">
            <button class="button"  (click)="toggleNewTestDesignCreator()" [class.is-info]="isCreatingNewTestDesign" [class.is-success]="!isCreatingNewTestDesign">
              <tra slug="auth_create_composite_assessment"></tra>
            </button>
            <button [disabled]="viewArchivedLoading" class="button " (click)="toggleViewArchive(false)" [class.is-info]="false">
              <p *ngIf="viewArchived" style="text-align: center; white-space: nowrap; margin-top: auto;">View Active Assesments</p><!--TODO Add translation Slug-->
              <tra *ngIf="!viewArchived" slug="auth_view_archived_assessments"></tra>
            </button>
          </div> 
          <div *ngIf="isCreatingNewTestDesign">
            <p><tra slug="New Assessment Design"></tra></p>
            <table class="table" style="width:auto; margin-top:1em;">
              <tr>
                <th> <tra slug="test_auth_short_name"></tra> </th>
                <td> <input [formControl]="testDesignCreateForm.slug"  type="text" maxlength="12" class="input is-small" > </td>
              </tr>
              <tr>
                <th> <tra slug="test_auth_title_description"></tra> </th>
                <td> 
                  <input [formControl]="testDesignCreateForm.name" type="text" maxlength="45" class="input is-small" >
                  <textarea [formControl]="testDesignCreateForm.description"  class="textarea is-small"></textarea>
                </td>
              </tr>
              <tr>
                <th> <tra slug="test_auth_authoring_group"></tra> </th>
                <td> 
                  <div class="select is-small">
                    <select [formControl]="testDesignCreateForm.group_id">
                      <option *ngFor="let group of myItems.authGroupsAsSuper" [value]="group.group_id">{{getGroupNameById(group.group_id)}} ({{group.group_id}})</option>
                    </select>
                  </div>
                </td>
              </tr>
              <tr>
                <th> <tra slug="Use Template?"></tra> </th>
                <td> 
                  <div style="display: flex; flex-direction: row;">
                    <input class="input is-small" style="width:4em;" [(ngModel)]="compositeAsmtTemplateQsId">
                    <div class="select is-small">
                      <select [(ngModel)]="compositeAsmtTemplateQsId">
                        <option *ngFor="let questionSet of frameworks" [value]="questionSet.id">
                          [{{questionSet.id}}] {{questionSet.slug}} | {{questionSet.name}}
                        </option>
                      </select>
                    </div>
                  </div>
                </td>
              </tr>
              <tr *ngIf="testDesignCreateError">
                <td colspan=2>
                  <div class="notification is-warning">
                    {{testDesignCreateError}}
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan=2>
                  <button  [disabled]="isTestDesignCreateSaving && !testDesignCreateForm.group_id.value" class="button is-success is-fullwidth" (click)="createNewTestDesign()" >
                    {{lang.tra('auth_create_into') + ' ' + getGroupNameById(testDesignCreateForm.group_id.value)}}
                  </button>
                </td>
              </tr>
            </table>
            
          </div>
          <hr/>
        </div>

        <div *ngIf="view == 'sets' ">
          <div class="space-between">
            <h2>Item Sets</h2>
            <div class="input-container">
              <input class="input" type="text" placeholder="Filter" [(ngModel)]="itemBankDescrFilter" (input)="applyItemSetFilters()" (change)="applyItemSetFilters()">
              <i class="fa fa-search input-icon"></i>
            </div>
          </div>
          <table *ngIf='!viewArchivedLoading' class="table is-hoverable" style="width:auto; margin-top:1em;">
            <tr>
              <th><tra slug="test_auth_name_col"></tra></th>
              <th><tra slug="test_auth_description_col"></tra></th>
              <th><tra slug="test_auth_num_items_col"></tra></th>
              <th><tra slug="test_auth_group"></tra></th>
              <th></th>
            </tr>
            <tr *ngFor="let questionSet of questionSets" [class.is-hidden]="questionSet.is_test_design">
              <td> <strong>{{questionSet.slug}}</strong> </td>
              <td>
                <p>
                  <a  [class.is-disabled]="viewArchived || viewArchivedLoading" [routerLink]="getQuestionSetRoute(questionSet.id)" >{{questionSet.name}}</a>
                </p>
                <div class="asmt-detail" [class.is-hidden]="!isShowDetails" [class.is-tables-active]="isShowDetailsTable">
                  <tra-md [slug]="questionSet.description"></tra-md>
                </div>
              </td>
              <td style="text-align:center;">
                {{questionSet.num_items}}
              </td>
              <td>
                <span [ngSwitch]="checkPersonalQuestionSet(questionSet)">
                  <span *ngSwitchCase="false" class="tag">{{getGroupNameById(questionSet.group_id)}}</span>
                  <span *ngSwitchCase="true" class="tag is-warning"><em>Personal</em></span>
                </span>
              </td>
              <td>
                <div *ngIf="viewArchived && !viewArchivedLoading">
                  <button class="dropdown" [disabled]="!checkQuestionSetSuperRole(questionSet)" (click)="recoverItemBank(questionSet)" [class.is-disabled]="isArchivingItemBank">Recover</button> <!--TODO Add translation Slug-->
                </div>
                <div *ngIf="!viewArchived && !viewArchivedLoading" class="dropdown" [class.is-active]="isActionDropdownActive(questionSet.id)">
                  <div class="dropdown-trigger">
                    <button (click)="toggleActionDropdown(questionSet.id, $event)" class="button is-info" aria-haspopup="true" aria-controls="dropdown-menu">
                      <span>Action</span>
                      <span class="icon is-small">
                        <i class="fas fa-angle-down" aria-hidden="true"></i>
                      </span>
                    </button>
                  </div>
                  <div class="dropdown-menu" id="dropdown-menu" role="menu">
                    <div class="dropdown-content">
                      <a class="dropdown-item" [routerLink]="getQuestionSetRoute(questionSet.id)"><tra slug="test_auth_view_edit"></tra></a>
                      <a class="dropdown-item" *ngIf="checkQuestionSetSuperRole(questionSet)" (click)="openItemBankAccessModal(questionSet)"><tra slug="auth_manage_access"></tra></a> 
                      <a class="dropdown-item" [routerLink]="getNotificationsRouterLink(questionSet)"><tra slug="auth_view_notifications"></tra></a>
                      <a class="dropdown-item" *ngIf="checkQuestionSetSuperRole(questionSet)" (click)="archiveItemBank(questionSet)" [class.is-disabled]="isArchivingItemBank"><tra slug="test_auth_archive"></tra></a>
                      <a class="dropdown-item" [class.is-disabled]="true"><tra slug="test_auth_export"></tra></a>
                    </div>
                  </div>
                </div>
              </td>
              <!-- <td *ngIf="hasGroupsAsSuper">
                <div style="display: flex; flex-direction: row; justify-content: center; width: 100%; height: 100%">
                  <button style="margin-right: 0" (click)="openItemBankAccessModal(questionSet)" class="button is-small is-success" style="margin-right: 0">Manage</button>
                </div>
              </td>
              <td><a [routerLink]="getQuestionSetRoute(questionSet.id)" class="button is-small is-info">View/Edit</a></td>
              <td><button [disabled]="true" class="button is-small">Export</button></td>
              <td> <button *ngIf="checkQuestionSetSuperRole(questionSet)" (click)="archiveItemBank(questionSet)" [disabled]="isArchivingItemBank" class="button is-small is-danger">Archive</button></td> -->
            </tr>
          </table>
          <div class="buttons" style="margin-top:0.5em">
            <button class="button" (click)="toggleNewQuestionSetCreator()" [class.is-info]="isCreatingNewQuestionSet" [class.is-success]="!isCreatingNewQuestionSet">
              <tra slug="Create New Item Set"></tra>
            </button>
            <!-- <button class="button" (click)="toggleNewQuestionSetCreator()" [class.is-info]="isCreatingNewQuestionSet">
              Import
            </button> -->
            <button [disabled]="viewArchivedLoading" class="button " (click)="toggleViewArchive()" [class.is-info]="false">
              <p *ngIf="viewArchived" style="text-align: center; white-space: nowrap; margin-top: auto;">View Active Assesments</p><!--TODO Add translation Slug-->
              <tra *ngIf="!viewArchived" slug="auth_view_archived_assessments"></tra>
            </button>
          </div>
          <div *ngIf="isCreatingNewQuestionSet">
            <p>Every new item set needs to be assigned to an authoring group.</p>
            <table class="table" style="width:auto; margin-top:1em;">
              <tr>
                <th> <tra slug="test_auth_short_name"></tra> </th>
                <td> <input [formControl]="itemSetCreateForm.slug"  type="text" maxlength="12" class="input is-small" > </td>
              </tr>
              <tr>
                <th> <tra slug="test_auth_title_description"></tra> </th>
                <td> 
                  <input [formControl]="itemSetCreateForm.name" type="text" maxlength="45" class="input is-small" >
                  <textarea [formControl]="itemSetCreateForm.description"  class="textarea is-small"></textarea>
                </td>
              </tr>
              <tr>
                <th> <tra slug="test_auth_authoring_group"></tra> </th>
                <td> 
                  <div class="select is-small">
                    <select [formControl]="itemSetCreateForm.group_id">
                      <option *ngFor="let group of myItems.authGroupsAsSuper" [value]="group.group_id">{{getGroupNameById(group.group_id)}} ({{group.group_id}})</option>
                    </select>
                  </div>
                </td>
              </tr>
              <tr *ngIf="itemSetCreateError">
                <td colspan=2>
                  <div class="notification is-warning">
                    {{itemSetCreateError}}
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan=2>
                  <button  [disabled]="isItemSetCreateSaving && !itemSetCreateForm.group_id.value" class="button is-success is-fullwidth" (click)="createNewItemBank()" >
                    {{lang.tra('auth_create_into') + ' ' + getGroupNameById(itemSetCreateForm.group_id.value)}}
                  </button>
                </td>
              </tr>
            </table>
            
          </div>
        </div>
        
        <div *ngIf="view == 'groups' ">
          <h4><tra slug="authoring_groups_header"></tra></h4>
          <p>
            <tra slug="authoring_groups_description_1"></tra> <strong><tra slug="authoring_groups_description_2"></tra></strong> <tra slug="authoring_groups_description_3"></tra> 
          </p>
          <p>
            <tra slug="personal_group_description_1"></tra> <span class="tag is-small is-warning"><em><tra slug="test_auth_personal"></tra></em></span> <tra slug="personal_group_description_3"></tra>
          </p>
          <table style="width:auto; margin-top:1em;">
            <tr>
              <th><tra slug="test_auth_admin_col"></tra></th>
              <th><tra slug="test_auth_group"></tra></th>
              <th *ngIf="false"><tra slug="test_auth_members_col"></tra></th>
              <th><tra slug="test_auth_leave_col"></tra></th>
            </tr>
            <tr *ngFor="let group of getAuthoringGroups()">
              <td> 
                <div class="dropdown" [class.is-active]="isGroupDropdownActive(group.group_id)">
                  <div class="dropdown-trigger">
                    <button [disabled]="!group.isSuper" (click)="toggleActionDropdown(group.group_id, $event, true)" class="button is-small" aria-haspopup="true" aria-controls="dropdown-menu">
                      <i class="fas fa-ellipsis-h" aria-hidden="true"></i>
                    </button>
                  </div>
                  <div class="dropdown-menu" id="dropdown-menu" role="menu">
                    <div class="dropdown-content">
                      <a *ngIf="group.isSuper && !group.isPersonal" class="dropdown-item" (click)="openAuthGroupOptionsModal(group)"><tra slug="auth_manage_access"></tra></a> 
                      <a *ngIf="group.isSuper" class="dropdown-item" (click)="openAuthGroupTagsModal(group)"><tra slug="auth_manage_tags"></tra></a> 
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <span [ngSwitch]="!!group.isPersonal">
                  <span *ngSwitchCase="false" class="tag">{{group.description}}</span>
                  <span *ngSwitchCase="true" class="tag is-warning"><em><tra slug="test_auth_personal"></tra></em></span>
                </span>
              </td>
              <td *ngIf="false"> 5 member(s) </td>
              <td> <button *ngIf="!group.isPersonal" [disabled]="true" class="button is-small is-danger"><tra slug="test_auth_leave_group"></tra></button> </td>
            </tr>
          </table>
          <div class="buttons" style="margin-top:0.5em">
            <button class="button is-success" (click)="toggleNewAuthGroupCreator()" [class.is-info]="isCreatingNewAuthGroup" [class.is-success]="!isCreatingNewAuthGroup">
              <tra slug="create_auth_group"></tra>
            </button>
            <button *ngIf="hasGroupsAsSuper()" class="button is-success" (click)="toggleNewTestAuthorCreator()" [class.is-info]="isCreatingNewTestAuthor" [class.is-success]="!isCreatingNewTestAuthor">
              {{isCreatingNewTestAuthor ? lang.tra('auth_cancel_share') : lang.tra('auth_share_access')}}
            </button>
          </div>
          <div *ngIf="isCreatingNewAuthGroup">
            <p>Every new assessments needs to be assigned to an authoring group.</p>
            <table class="table" style="width:auto; margin-top:1em;">
              <tr>
                <th> <tra slug="test_auth_name_col"></tra> </th>
                <td> <input [formControl]="groupCreateForm.description" type="text" class="input is-small" > </td>
              </tr>
              <tr *ngIf="authGroupCreateError">
                <td colspan=2>
                  <div class="notification is-warning">
                    {{authGroupCreateError}}
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan=2>
                  <button  [disabled]="isAuthGroupCreateSaving" class="button is-success is-fullwidth" (click)="createNewAuthGroup()" >
                    <tra slug="test_auth_create"></tra>
                  </button>
                </td>
              </tr>
            </table>
            
          </div>
          <share-access *ngIf="isCreatingNewTestAuthor" (doneCreating)="isCreatingNewTestAuthor = false;"></share-access>
        </div>
             
    </div>
    <br/>
  </div>
  <footer [hasLinks]="true"></footer>
</div>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="AuthoringModal.MANAGE_GROUP">
            <manage-access [authoringGroupId]="cmc().authoringGroupId" [groupName]="cmc().groupName"></manage-access>
              <!-- <sa-modal-session [savePayload]="cmc()" saveProp="payload" (isDateValid)="setDateValidity($event)"></sa-modal-session> -->
          </div>
          <div *ngSwitchCase="AuthoringModal.MANAGE_ITEM_BANK">
            <manage-access [itemSet]="cmc().questionSet" [itemSetId]="cmc().itemSetId" [singleGroupId]="cmc().singleGroupId" [authoringGroupId]="cmc().authoringGroupId" [groupName]="cmc().groupName"></manage-access>
          </div>
          <div *ngSwitchCase="AuthoringModal.MANAGE_GROUP_TAGS">
            <manage-tags [authGroupId]="cmc().authGroupId" [groupName]="cmc().groupName"></manage-tags>
          </div>
      </div>
      <modal-footer [confirmButton]="false" closeMessage="btn_close" [pageModal]="pageModal" ></modal-footer>
  </div>
</div>

<!--
  <div class="custom-modal" *ngIf="openAuthGroupOptions" style="z-index: 100;">
    <div class="modal-contents">
      <div class="simple-content-bounds">
        <h2><strong>{{ openAuthGroupOptions.description }}</strong> Configuration</h2>
        <hr/>
        <p>stuff</p>
        <hr/>
        <button (click)="closeAuthGroupOptionsModal()" class="button is-danger">Cancel</button>
      </div>
    </div>
  </div>
-->
