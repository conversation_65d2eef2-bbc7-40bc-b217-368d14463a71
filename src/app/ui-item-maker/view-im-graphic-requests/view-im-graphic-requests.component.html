<div class="page-body ">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    ></header>
    <div class="page-content is-fullpage" >
      <h4><tra slug="lbl_graphic_requests"></tra></h4>
      <menu-bar 
        [menuTabs]="requestTabs"
        [tabIdInit]=initialRequestTab
        (change)="selectRequestTab($event)">
      </menu-bar>

      <div>
        <div style="margin-bottom:0.5em">
          Which assessment authoring group are you working on?
        </div>
        <div class="buttons">
          <button *ngFor="let group of getAuthoringGroups()"
            class="button is-small"
            [class.is-info]="isGroupToggled(group.group_id)"
            (click)="toggleGroupFilter(group.group_id)"
          >
            {{group.description}}
          </button>
        </div>
        <hr/>
      </div>

      <table>
        <tr>
          <th *ngFor="let col of requestCols" class="hover-header">
            <div class ="table-header-container">
              <tra [slug]="col.caption"></tra>
            </div>
            <a *ngIf="col.prop" (click)="changeFilter($event, col.prop, col.caption)" style="white-space: nowrap;">
              <i class="fa fa-search"></i>
              {{filters.userInput[col.prop]}}
            </a>
          </th>
        </tr>
        <tr *ngFor="let request of activeRecords?.data">
          <td *ngFor="let col of requestCols">
            <div [ngSwitch]="col.id">
              <div *ngSwitchCase="ERequestCol.ITEM_BANK">
                <span class="tag">{{requestTableValue(request, col.id)}}</span>
              </div>

              <div *ngSwitchCase="ERequestCol.ITEM">
               <a [routerLink]="generateRequestItemLink(request)">{{requestTableValue(request, col.id)}}</a>
              </div>
              <!--? The request card is being called over here  -->
              <div *ngSwitchCase="ERequestCol.REQUEST">
                  <graphics-request 
                    [request]="request"
                    [memberAssignmentCtrl]="memberAssignmentCtrl"
                    [itemBankGroupId]="request.set_group_id"
                    [itemBankSingleGroupId]="request.set_single_group_id"
                  ></graphics-request>
              </div>

              <div *ngSwitchCase="ERequestCol.STATUS">
                <span *ngIf="request.status" class="tag"
                  [class.is-danger]="request.status == GraphicReqStatus.TO_DO"
                  [class.is-info]="request.status == GraphicReqStatus.IN_PROGRESS"
                  [class.is-dark]="request.status == GraphicReqStatus.PENDING"
                  [class.is-success]="request.status == GraphicReqStatus.COMPLETED"
                >
                  <tra [slug]="requestTableValue(request, col.id)"></tra>
                </span>
              </div>

              <div *ngSwitchDefault>
                {{requestTableValue(request, col.id)}}
              </div>

            </div>
          </td>
        </tr>
      </table>
      
      <div style="margin-top: 0.5em; display: flex; align-items: center">
        <button class="button is-small" (click)="prevPage()" [disabled]="filters.currentPage == 1"> <i class="fa fa-caret-left" aria-hidden="true"></i> </button>
        <button class="button is-small" (click)="nextPage()" [disabled]="filters.currentPage >= getMaxPages()"> <i class="fa fa-caret-right" aria-hidden="true"></i> </button>
        Page {{filters.currentPage}} of {{getMaxPages()}}
        <div style="margin-left:0.2em; display:flex; align-items: center">
          <button class="button is-small" title="Entries per page" (click)="isShowingPageLength = !isShowingPageLength"> <i class="fas fa-cog" aria-hidden="true"></i> </button>
          <div style="display:flex; align-items: center">
              <form *ngIf="isShowingPageLength" [formGroup]="pageLengthG" (ngSubmit)="retrieveRequests()" >
                <label>
                    &nbsp;&nbsp;Entries per page &nbsp;&nbsp;
                  <input type="number" style="width:5em" [formControl]="pageLength" />
                </label>
              </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    <widget-assign-user 
      *ngIf="memberAssignmentCtrl.openedAssignWindowRequest"
      [memberAssignmentCtrl]="memberAssignmentCtrl"
    ></widget-assign-user>
  </div>
  <footer [hasLinks]="true"></footer>
</div>