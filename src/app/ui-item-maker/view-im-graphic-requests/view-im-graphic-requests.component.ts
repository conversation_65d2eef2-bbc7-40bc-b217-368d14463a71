import { Component, OnInit } from '@angular/core';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { testAuthPanels } from './../../core/main-nav/panels/test-auth';
import { ItemMakerService } from '../item-maker.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IMenuTabConfig } from 'src/app/ui-partial/menu-bar/menu-bar.component';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { IActiveRecords } from 'src/app/ui-trans/ui-trans-db/ui-trans-db.component';
import { MemberAssignmentCtrl, AssignTargetType } from '../item-set-editor/controllers/member-assignment';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthRolesService } from '../auth-roles.service';
import * as moment from 'moment-timezone';
import { LangService } from 'src/app/core/lang.service';
import { requestStatusOptions } from './../graphics-workflow-section/graphics-request/graphics-request.component'
import { GraphicReqStatus } from './../graphics-workflow-section/types'

enum RequestTab {
  SIGNED_OFF = 'SIGNED_OFF',
  NOT_SIGNED_OFF = 'NOT_SIGNED_OFF'
}

interface IFilterSettings {
  currentPage: number;
  pageLength: number;
  userInput: {
    req_id: string,
    assigned_name: string,
    created_by_name: string
    status: string,
    set_name: string,
    question_label: string
  }
  config: {
    req_id: string,
    set_group_ids: number[],
    assigned_name: string,
    created_by_name: string,
    status: string[],
    set_name: string,
    is_signed_off: number,
    question_label: string
  };
}


enum ERequestCol {
  ID = 'ID',
  TYPE = 'TYPE',
  REQUEST = 'REQUEST', // The content
  ITEM_BANK = 'ITEM_BANK',
  ITEM = 'ITEM',
  CREATED_ON = 'CREATED_ON',
  CREATED_BY = 'CREATED_BY',
  ASSIGNED_TO = 'ASSIGNED_TO',
  STATUS = 'STATUS'
}


interface RequestHeader {
  id: ERequestCol,
  caption: string,
  prop?: string
}


@Component({
  selector: 'view-im-graphic-requests',
  templateUrl: './view-im-graphic-requests.component.html',
  styleUrls: ['./view-im-graphic-requests.component.scss']
})
export class ViewImGraphicRequestsComponent implements OnInit {

  constructor(
    private sidePanel: SidepanelService,
    private breadcrumbsService: BreadcrumbsService,
    private itemMaker: ItemMakerService,
    private router: Router,
    private route: ActivatedRoute,
    private routes: RoutesService,
    private auth: AuthService,
    private assignedUsersService: AssignedUsersService, //
    public authRoles: AuthRolesService,
    private lang: LangService
 
  ) { }
  GraphicReqStatus = GraphicReqStatus;
  memberAssignmentCtrl = new MemberAssignmentCtrl(this.auth, this.assignedUsersService, this.authRoles, this.routes);

  ERequestCol = ERequestCol;
  public requestCols: RequestHeader[] = [
    {
      id: ERequestCol.ID,
      caption: 'ie_id',
      prop: 'req_id'
    },
    {
      id: ERequestCol.TYPE,
      caption: 'ie_type',
    },
    {
      id: ERequestCol.REQUEST,
      caption: 'Request',
    },
    {
      id: ERequestCol.ITEM_BANK,
      caption: 'item_banks_header',
      prop: 'set_name'
    },
    {
      id: ERequestCol.ITEM,
      caption: 'ie_item',
      prop: 'question_label'
    },
    {
      id: ERequestCol.CREATED_ON,
      caption: 'auth_created'
    },
    {
      id: ERequestCol.CREATED_BY,
      caption: 'Created by',
      prop: 'created_by_name'
    },
    {
      id: ERequestCol.ASSIGNED_TO,
      caption: 'auth_assigned_to',
      prop: 'assigned_name'
    },
    {
      id: ERequestCol.STATUS,
      caption: 'lbl_status',
      prop: 'status'
    }
  ]

  public breadcrumb = [];
  activeRecords: IActiveRecords;
  subscription: Subscription = new Subscription();
  routeSub: Subscription;


  filters: IFilterSettings = {
    currentPage: 1,
    pageLength: 10,
    userInput: {
      req_id: null,
      assigned_name: null,
      created_by_name: null,
      question_label: null,
      status: null,
      set_name: null
    },
    config: {
      req_id: null,
      set_group_ids: [],
      assigned_name: null,
      created_by_name: null,
      question_label: null,
      set_name: null,
      is_signed_off: 0,
      status: []
    }
  };

  public filterSettings = {};

  public isShowingPageLength;
  public pageLength = new FormControl(10);
  public pageLengthG = new FormGroup({});

  requestTabs: IMenuTabConfig<RequestTab>[] = [
    {id: RequestTab.NOT_SIGNED_OFF, caption: 'auth_request_not_signed_off'},
    {id: RequestTab.SIGNED_OFF, caption: 'auth_request_signed_off'}
  ];
  initialRequestTab = RequestTab.NOT_SIGNED_OFF;

  selectRequestTab(id){
    this.filters.config.is_signed_off = id === RequestTab.SIGNED_OFF ? 1 : 0
    this.updateRouteFilter();
  }

  ngOnInit(): void {
    this.sidePanel.activate(testAuthPanels)
    this.itemMaker.loadMyAuthoringGroups();
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
      this.breadcrumbsService._CURRENT('lbl_graphic_requests', this.router.url),
    ];
    this.routeSub = this.route.params.subscribe(routeParams => {
      this.reloadFilters(routeParams['filters']);
      this.initFilterFormControls();
      this.retrieveRequests();
    });
  }
  
  /** Load group members - people with access to the question sets of the requests. Set the assignees for each request. */
  async refreshGroupMembers(){    
    const uniqueSetIds = new Set();
    const mergedUsers: Map<number, any> = new Map();
    const promises: Promise<any>[] = [];
    // Iterate over requests currently in the table to find distinct question sets that they belong to
    this.activeRecords?.data?.forEach(graphicReq => {
      const { set_id, set_group_id, set_single_group_id } = graphicReq;
      if (uniqueSetIds.has(set_id)) return;
      // Find the group members of the question set
      const newPromise = this.itemMaker.loadMyGroupMembers(set_single_group_id, set_group_id)
      .then(res => {
        // Add the member to the merged list of all members, indicate which set(s) they have access to
        res.forEach(member => {
          const existingMember = mergedUsers.get(member.id)
          if (existingMember) {
            existingMember.set_ids.push(set_id)
          }
          else {
            mergedUsers.set(member.id, {...member, set_ids: [set_id]});
          }
        })
      })
      promises.push(newPromise)
    })
    await Promise.all(promises);
    const members = Array.from(mergedUsers.values());
    // Save the group members in the service
    this.memberAssignmentCtrl.setGroupMembers(members);
    // Set the assignees for the requests in the table
    this.activeRecords?.data?.forEach(record => {
      if (record.assigned_uid) this.assignedUsersService.setupGraphicRequest(record)
    })
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }


  getAuthoringGroups(){
    return this.itemMaker.getAuthoringGroups();;
  }

  /** Update the requests filters based on user input and refresh requests */
  changeFilter(event, prop: string, caption) {
    event.stopPropagation();
    const currentInput = this.filters.userInput[prop];
    const newInput = prompt('Filter ' + caption, currentInput == null ? "" : currentInput) || undefined;

    let newConfig;
    // For status, make a list of ones that fit the user input filter
    if (prop == 'status' && currentInput){
      newConfig = requestStatusOptions.filter(option => {
        return this.lang.tra(option.caption).toLowerCase().includes(newInput?.toLowerCase())
    }).map(option => option.value)
    } else {
      newConfig = newInput
    }
    this.filters.userInput[prop] = newInput
    this.filters.config[prop] = newConfig
    this.updateRouteFilter();
  }

  //** Toggle selecting an authoring group to filter requests */
  toggleGroupFilter(groupId){
    if (this.filters.config.set_group_ids.includes(groupId)){
      this.filters.config.set_group_ids = this.filters.config.set_group_ids.filter(id => id != groupId)
    }
    else {
      this.filters.config.set_group_ids.push(groupId)
    }
    this.updateRouteFilter();
  }

  isGroupToggled(groupId){
    return this.filters.config.set_group_ids.includes(groupId)
  }


  /** Decode and save filter settings from string encoded in route params */
  reloadFilters(encodedFilterStr:string) {
    if (encodedFilterStr) {
      try {
        this.filters = JSON.parse( atob(encodedFilterStr) ) ;
        if (!this.filters.pageLength) { this.filters.pageLength = 10; }
        this.pageLength.setValue(this.filters.pageLength);
        this.filters.currentPage = (this.filters.currentPage * 1) || 1 ;
      } catch (e) {}
    }
  }

  initFilterFormControls() {
    this.subscription.add(this.pageLength.valueChanges.subscribe(v => { if(this.filters.pageLength !== v) { this.filters.pageLength = v; this.updateRouteFilter();} } ));
  }

  prevPage() {
    if (this.filters.currentPage > 1) {
      this.filters.currentPage -= 1;
      this.retrieveRequests();
    }
  }

  nextPage() {
    if (this.filters.currentPage < this.getMaxPages()) {
      this.filters.currentPage += 1;
      this.retrieveRequests();
    }
  }

  getMaxPages() {
    if (this.activeRecords) {
      return Math.ceil(this.activeRecords.total / this.filters.pageLength);
    }
    return 0;
  }

  /** Load requests according to filters */
  retrieveRequests() {
    const query: any = {
      $limit: this.filters.pageLength,
      $skip: (this.filters.currentPage - 1) * this.filters.pageLength ,
    };

    for(const prop in this.filters.config) {
      if(this.filters.config[prop] != null) {
        query[prop] = this.filters.config[prop];
      } 
    }
    return this.auth.apiFind(this.routes.TEST_AUTH_QUESTION_GRAPHIC_REQUESTS, {query})
      .then(res => {
        if(res.data){

          res.data.forEach(request => {

            this.assignedUsersService.setupGraphicRequest(request)

          })

        }
        this.activeRecords = res;
        // Reload group members after the graphic requests are refreshed
        this.refreshGroupMembers();
      })
      .catch(e => {
        console.error(e);
      });
  }

  /** Store chosen filters in route params */
  updateRouteFilter() {
    this.router.navigate([
      `en`,
      `test-auth`,
      `graphic-requests`,
      btoa(JSON.stringify(this.filters))
    ]);
  }


  /** Return link to the item the request belongs to */
  generateRequestItemLink(request){
    const lang = request.lang == "fr" ? "fr" : "en"
    return [
      '/',
      lang,
      'test-auth',
      'item-set-editor',
      request.set_id,
      encodeURIComponent(request.question_label)
    ]
  }

  timeFrom(date) {
    if (date) {
      return moment(date).fromNow();
    }
    return '';
  }

  /** Render value in table cell */
  requestTableValue(request, col: ERequestCol): string | undefined {
    switch(col) {
      case ERequestCol.ID:
        return request.id;
      case ERequestCol.TYPE:
        return this.lang.tra(request.is_revision ? "auth_graphic_req_new_revision" : "auth_graphic_req_new_image")
      case ERequestCol.ITEM_BANK:
        return request.set_name;
      case ERequestCol.ITEM:
        return request.question_label;
      case ERequestCol.CREATED_ON:
        return this.timeFrom(request.created_on);
      case ERequestCol.CREATED_BY:
        return request.created_by_name;
      case ERequestCol.STATUS:
        return requestStatusOptions.find(option => option.value == request.status)?.caption
      case ERequestCol.ASSIGNED_TO:
        return this.getAssignedUserName(request);
      default:
        return "";
    }
  }

  /** Get the name of the user assigned to the request */
  getAssignedUserName(request) { 
    const assignedUser = this.assignedUsersService.getAssignedUser(request.id, AssignTargetType.GRAPHIC_REQUEST);
    if(assignedUser) {
      return assignedUser.name;
    }
  }

}
