import { Component, Input, OnInit } from "@angular/core";
import { IContentElementValidator, IValidatorCombinationProp, ValidatorMode } from "../../ui-testrunner/element-render-validator/model";
import { ElementType } from "../../ui-testrunner/models";
import { elementTypes } from "../../ui-testrunner/models/ElementTypeDefs";
import { LangService } from "../../core/lang.service";
import { AuthScopeSetting, AuthScopeSettingsService } from "../auth-scope-settings.service";
import { EditingDisabledService } from "../editing-disabled.service";

@Component({
  selector: "element-config-validator",
  templateUrl: "./element-config-validator.component.html",
  styleUrls: ["./element-config-validator.component.scss"]
})
export class ElementConfigValidatorComponent implements OnInit {
  @Input() element: IContentElementValidator;
  elementTypes = elementTypes.filter(element => !element.isDisabled );
  eleWithTragetIds = new Set([ElementType.GROUPING])

  constructor(private authScopeSettings: AuthScopeSettingsService, private editingDisabled: EditingDisabledService, public lang: LangService) { }
  modes = [
    { id: ValidatorMode.NORMAL, caption: "Single" },
    { id: ValidatorMode.COMBINATION, caption: "Combination" }
  ];

  ngOnInit(): void {
    if (!this.element.mode) {
      this.element.mode = ValidatorMode.NORMAL;
    }
  }

  isScoreWeightEnabled = () => this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_SCORE_WEIGHT);

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  isSingleMode() {
    return this.element.mode == ValidatorMode.NORMAL;
  }

  isComboMode() {
    return this.element.mode == ValidatorMode.COMBINATION;
  }

  addCombination() {
    if (!this.element.combinations) this.element.combinations = [];
    this.element.combinations.push([this.getElementProps()]);
  }

  deleteCombination(combinationIdx) {
    if (!this.element.combinations) return;
    this.element.combinations.splice(combinationIdx, 1);
  }

  addCombinationElement(combinationIdx) {
    if (!this.element.combinations) return;
    this.element.combinations[combinationIdx].push(this.getElementProps());
  }

  deleteCombinationElement(combinationIdx, elementIdx) {
    if (!this.element.combinations) return;
    let combination = this.element.combinations[combinationIdx];
    combination.splice(elementIdx, 1);
  }

  private getElementProps() {
    let elementProps: IValidatorCombinationProp = {
      validateId: -1,
      elementType: "",
      correctValue: "",
      dndElements: []
    };
    return elementProps;
  }

  addTarget(combinationIdx, elementIdx) {
    let combination = this.element.combinations[combinationIdx];
    let element = combination[elementIdx];
    if(!element.dndElements) element.dndElements = []
    element.dndElements.push(this.getDndEleProps())
  }

  removeTarget(combinationIdx, elementIdx, targetIdx){
    let combination = this.element.combinations[combinationIdx];
    let element = combination[elementIdx];
    if(!element.dndElements) return;
    element.dndElements.splice(targetIdx, 1);
  }

  private getDndEleProps(){
    return {
      targetId: '-1',
      correctValue: "",
      isOrderImp: false
    }
  }

  hasTargetIds(element: ElementType): boolean{
    return !!this.eleWithTragetIds.has(element)
  }

  getCorrectType(elementType) {
    switch (elementType) {
      case ElementType.MCQ:
        return "ID";
      default:
        return "value";
    }
  }
}
