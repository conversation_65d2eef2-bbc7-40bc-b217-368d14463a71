<fieldset [disabled]="isReadOnly()">
    <div class="simple-form-container" style="display: flex; flex-direction: column" >
        <div *ngIf="isSingleMode()">
            <label style="width: 50%">Validate ID <input type="number" [(ngModel)]="element.validateId" /> </label>
            <label style="width: 50%">Validate Property <input type="text" [(ngModel)]="element.validateProp"> </label>
            <label style="width: 50%">Correct Value <input type="text" [(ngModel)]="element.correctValue"> </label>
        </div>
        <div *ngIf="isComboMode()">
            <button (click)="addCombination()" class="button is-primary" type="submit">Add Combination </button>
            <div class="combo-info">
                <p><strong>Info!</strong> Please set elements weight to 0 explicitly.</p>
            </div>              
            <ng-container *ngFor="let combinations of element.combinations; let combinationIdx = index">
                <hr/>
                <button (click)="addCombinationElement(combinationIdx)" class="button is-primary is-small" style="margin: 0.3em;">Add element</button>
                <ng-container *ngFor="let combination of combinations; let elementIdx = index">
                    <label style="width: 50%">Validate ID <input type="number" [(ngModel)]="combination.validateId" /> </label>
                    <label style="width: 50%">Element Type 
                        <!-- <input type="text" [(ngModel)]="combination.elementType"> -->
                        <select [(ngModel)]="combination.elementType" style="margin-left: 1em;">
                            <option *ngFor="let elementType of elementTypes" [ngValue]="elementType.id">
                              {{lang.tra(elementType.caption)}}
                            </option>
                        </select>
                    </label>
                <ng-container [ngSwitch]="hasTargetIds(combination.elementType)">
                    <label 
                        *ngSwitchCase="false"
                        style="width: 50%">
                        Correct {{getCorrectType(combination.elementType)}} 
                        <input type="text" [(ngModel)]="combination.correctValue"> 
                    </label>
                    <ng-container *ngSwitchCase="true">
                        <button (click)="addTarget(combinationIdx,elementIdx)" class="button is-primary is-small" style="margin: 0.3em;">Add Target</button>
                        <ng-container *ngFor="let dndElement of combination.dndElements let targetIdx = index">
                            <div>
                                <label style="width: 50%">order matters?: <input type="checkbox" [(ngModel)]="dndElement.isOrderImp"></label>
                                <label style="width: 50%">Target Id: <input type="text" [(ngModel)]="dndElement.targetId"></label>
                                <label style="width: 75%">Correct {{getCorrectType(combination.elementType)}} <input type="text" [(ngModel)]="dndElement.correctValue"> </label>
                                <button (click)="removeTarget(combinationIdx, elementIdx, targetIdx)" style="margin: 0.3em;" class="button is-danger is-small" >Delete Target</button>
                            </div>
                        </ng-container>
                    </ng-container>
                </ng-container>
                <button (click)="deleteCombinationElement(combinationIdx,elementIdx)" style="margin: 0.3em;" class="button is-danger is-small" >Delete Element</button>
                </ng-container><br>
                <div *ngIf="combinations.length>0">
                    <label style="width: 50%">Score <input type="number" [(ngModel)]="combinations[0].score" /> </label>
                </div>
                <button (click)="deleteCombination(combinationIdx)" class="button is-danger" style="margin-top: 0.3em;">Delete Combination</button>
            </ng-container>
        </div>
        <hr/>
        <div class="form-row" *ngIf="isScoreWeightEnabled()">
            <div class="form-row-label">
                Score Weight
            </div>
            <div class="form-row-input">
                <input type="text" class="input" [(ngModel)]="element.scoreWeight">
            </div>
        </div>
    </div>
    <div class="control is-expanded">
        <div [class.is-disabled]="isReadOnly()" class="select is-fullwidth">
          <select [(ngModel)]="element.mode">
            <option *ngFor="let mode of modes; let index = index" [value]="mode.id">
              {{mode.caption}}
            </option>
          </select>
        </div>
      </div>
</fieldset>
