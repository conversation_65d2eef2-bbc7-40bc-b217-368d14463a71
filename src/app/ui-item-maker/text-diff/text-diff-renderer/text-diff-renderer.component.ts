import { Component, Input, OnInit } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ExpansionPanelContentType, IExpansionPanelContent } from 'src/app/ui-partial/expansion-panel/expansion-panel.component';

@Component({
  selector: 'text-diff-renderer',
  templateUrl: './text-diff-renderer.component.html',
  styleUrls: ['./text-diff-renderer.component.scss']
})
export class TextDiffRendererComponent implements OnInit, ICellRendererAngularComp {
  @Input() content: any;
  ExpansionPanelContentType = ExpansionPanelContentType

  ngOnInit(): void {}

  // Required method for cellRendererFramework
  agInit(params: any): void {
    this.content = params?.value;  // Or set any other data here from params
  }

  // Optional: for refreshing the component, can be used to update data
  refresh(params: any): boolean {
    return false;  // If you need to refresh the component (true), you can implement here
  }
}
