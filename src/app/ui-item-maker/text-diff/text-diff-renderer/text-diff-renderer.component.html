<section *ngFor="let c of content" class="content-block">
    <ng-container [ngSwitch]="c.type">
        <div *ngSwitchCase="ExpansionPanelContentType.JSON_VIEW">
            <label class="content-label">{{c.label}} </label>
            <div style="margin-left: 0.5em;">
                <ngx-json-viewer [json]="c.data" [expanded]="false"></ngx-json-viewer>
            </div>
        </div>
        <div *ngSwitchCase="ExpansionPanelContentType.TEXT_AREA">
            <div class="flex-gap">
                <span class="text-area-middle">
                    <label class="content-label">{{c.label}} </label>
                    <textarea style="margin-left:0.5em" [(ngModel)]="c.data" [readonly]="true"></textarea>
                </span>
            </div>
        </div>
        <div *ngSwitchCase="ExpansionPanelContentType.SPAN_LIST">
            <label class="content-label">{{c.label}} </label>
            <div>
                <ng-container *ngFor="let span of c.data">
                    <span [ngStyle]="span.style">{{ span.text }}</span>
                </ng-container>
            </div>
        </div>
    </ng-container>
</section>