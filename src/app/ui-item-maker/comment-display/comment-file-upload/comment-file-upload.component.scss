.upload-container{
  display:flex;
  flex-direction: column;
  gap:10px;

  .upload-btns{
    margin-top: 5px;
    display:flex;
    flex-direction: row;
    gap: 5px;
  }
}
.image-container {
  display: inline-block;
  background-color: white;
  overflow: auto; 
}

.image-container img {
  display: block;
  width: 100%; 
  height: auto;
}
.modal-contents{
  display: flex;
  flex-direction: column;
}
.is-hi-contrast{
  filter: invert(1);
}
// to group the close and the high contrast button
.modal-footer{
  width: 100%;
  display:flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.highContrastButton{
  margin-right: 1em;
  margin-top: 2.3em;
  padding: 1em;

  &.isToggled{
    background-color: #153d57;
  }

  &:hover{
    border: black 1px;
    background-color: #4687b3;
  }
}