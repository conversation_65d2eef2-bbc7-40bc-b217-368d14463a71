<div class="upload-container">
  <div *ngFor="let file of uploads; let i = index">
    <div *ngIf="file.file_url">
      <div>
        <a [href]="file.file_url" target="_blank">{{getUploadedMeta(file.file_url).filename}}</a>
      </div>
      <div class="upload-btns">
        <button class="button is-small is-link is-light" *ngIf="isFileImage(file)" (click)="startImgPreviewModal(file)"><tra slug="ie_preview"></tra></button>
        <button class="button is-small is-danger is-hovered" *ngIf="isCurrentUserAuthor" (click)="removeUpload(file)"><tra slug="ie_delete_param"></tra></button>
      </div>
    </div>

    <div *ngIf="!file.file_url">
      <div class="dropzone" dropZone (hovered)="toggleHover($event)" (dropped)="uploadFile($event, i)" [class.hovering]="isHovering">
        <p><tra slug="ie_drag_drop_file"></tra></p>
        <div class="file">
          <label class="file-label">
            <input class="file-input" type="file" (change)="uploadFile($event.target.files, i)">
            <span class="file-cta">
              <span class="file-icon">
                <i class="fa fa-upload"></i>
              </span>
              <span class="file-label">
                <tra slug="ie_choose_file"></tra>
              </span>
            </span>
          </label>
        </div>
      </div>
    </div>

  </div>
</div>


<div class="custom-modal" *ngIf="cModal()">

  <div *ngIf="cModal().type == CommentModalType.IMG_UPLOAD_PREVIEW" class="modal-contents" style="width: 30vw;">
    <h2>{{cmc().file.file_name}}</h2>
    <div class="image-container" [class.is-hi-contrast]="isHighContrast">
      <img src="{{cmc().file.file_url}}">
    </div>
    <span class="modal-footer">
      <button class="button highContrastButton is-small is-info" [class.isToggled]="isHighContrast" style="margin-right:1em" (click)="toggleHiContrast()">
        <span><tra slug="btn_hi_contrast"></tra></span>
      </button>
      <modal-footer [confirmButton]="false" [closeMessage]="'btn_close'" [pageModal]="pageModal"></modal-footer>
    </span>
  </div>

</div>