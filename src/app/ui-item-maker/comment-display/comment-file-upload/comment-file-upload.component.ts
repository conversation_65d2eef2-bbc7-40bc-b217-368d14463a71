import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import {IItemAuthNote, IItemAuthNoteFile} from '../../element-notes/element-notes.component';
import {memo} from '../../../ui-testrunner/element-render-video/element-render-video.component';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {CommentModalType} from "./../../element-notes/element-notes.component"
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';

@Component({
  selector: 'comment-file-upload',
  templateUrl: './comment-file-upload.component.html',
  styleUrls: ['./comment-file-upload.component.scss']
})
export class CommentFileUploadComponent implements OnInit {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private pageModalService: PageModalService,
    private lang: LangService,
    private loginGuard: LoginGuardService
  ) { }

  @Input() isCurrentUserAuthor: boolean = false;
  @Input() uploads = [];
  @Input() test_question_auth_note_id: number;

  @Output() updateComment = new EventEmitter();

  isHighContrast: boolean = false;
  CommentModalType = CommentModalType;
  pageModal: PageModalController;
  uploadedMeta = new Map();
  isHovering: boolean;
  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  
  toggleHover(event: boolean) {
    this.isHovering = event;
  }

  getUploadedMeta(file_url) {
    return memo(this.uploadedMeta, file_url, (url) => {
      if (url) {
        const path = url.split('/');
        const filename = path.pop().split('?')[0];
        const ext = filename.split('.').pop();
        return {filename, ext};
      }
    });
  }

  /** Check if file is an image */
  isFileImage(file:IItemAuthNoteFile){
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];
    const extension = file.file_url.split('.').pop().toLowerCase();
    return imageExtensions.includes(extension);
  }
  
  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  /** Upload a file */
  uploadFile(event: FileList, i: number) {

    const file = event.item(0);

    this.auth
    .uploadFile(file, file.name, 'authoring', true)
    .then(res => {
      const file_url = res.url;
      const file_name = file.name;

      const newUpload: IItemAuthNoteFile = {
        test_question_auth_note_id: this.test_question_auth_note_id,
        file_name,
        file_url
      };

      // Update db if this was for an existing comment ID
      if (this.test_question_auth_note_id){
        this.auth.apiCreate(this.routes.TEST_AUTH_NOTE_FILES, newUpload)
        .then((savedUpload) => {
          this.uploads[i] = savedUpload;
        })
      } else {
        this.uploads[i] = newUpload;
      }

    });
  }


  /** Remove an uploaded file */
  removeUpload(file: IItemAuthNoteFile) {

    const delLocalUpload = (file) => {
      const arr = this.uploads;
      const i = arr.indexOf(file);
      if (i !== -1) {
        arr.splice(i, 1);
      }
    }

    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('comment_remove_upload_conf'),
      confirm: () => {
        // Update db if this was for an existing comment ID
        if (this.test_question_auth_note_id){
          this.auth.apiRemove(this.routes.TEST_AUTH_NOTE_FILES, file.id)
          .then(() => {
            delLocalUpload(file)
          })
        } else {
          delLocalUpload(file)
        }

      }
    })
  }

  toggleHiContrast() {
    this.isHighContrast = !this.isHighContrast;
  }
  startImgPreviewModal(file:IItemAuthNoteFile){
    const config = {file}
    this.pageModal.newModal({
      type: CommentModalType.IMG_UPLOAD_PREVIEW,
      config,
      cancel: () => {},
      finish: () => {}
    })
  }
}
