@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/registration-or-login-form.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_modal.scss';

.page-body {
    @extend %page-body;
    .page-content {
    }
}

.table-header-container { 
    display: flex; 
    flex-direction: row; 
    justify-content: space-between;
}

.hover-header {
    &:hover {
        background-color: lightblue;
    }
}

.is-error {
    color: #FF6666;
}

.is-success {
    color: #30854e;
}

.flex-column {
    display: flex; 
    flex-direction: column; 
}

.flex-row {
    display: flex;
    flex-direction: row;
    gap: 1em;
}

.card {
    background-color: #fff;
    box-shadow: 0 .5em 1em -.125em rgba(10,10,10,.1),0 0 0 1px rgba(10,10,10,.02);
    color: #4a4a4a;
    max-width: 100%;
    position: relative;
    padding: 1.5em;
    border-radius: .5em;
    border: none;
}