@import '../../ui-testrunner/test-runner/test-runner.component.scss';
@import '../../../styles/partials/_modal.scss';

$selectedColor: #209cee;
$darkGrey: #555;
$lightGrey: #d0d0d0;

.track-changes-color {
    color: #ffb340
}

.editing-color {
    color: #ff4040
}

.review-stage-color {
    color: #ffb340
}

.editing-stage-color {
    color: #ff4040
}

.btn_close {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    i {
        cursor: pointer;
    }
}

//This scss is intentionally not nested in styles.scss so the cdkDragPreview can work correctly
.question-block {
    width: 95%;
    height: 2em;
    min-height: 1.5em;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    margin-bottom: 0.15em;
    margin-top: 0.15em;
    background-color: white;
    cursor: pointer;

    .question-block-indicator {
        position: absolute;
        top: 0px;
        right: 3px;
        font-size: 12px;

        .is-confirmed {
            color: #83e8bb;
        }

        .is-unconfirmed {
            color: #f1f1f1;
        }
    }

    .question-block-selected-indicator {
        position: absolute;
        left: 6px;
        font-size: 12px;
    }

    .question-block-label {
        font-size: 0.5em;
    }

    .question-block-lock {
        position: absolute;
        top: 0.35em;
        left: 0.35em;
        font-size: 0.36em;
    }

    &.is-info-slide {
        height: 1em;
        justify-content: flex-start;
        padding: 0.2em;
        background-color: #ececec;

        .question-block-label {
            font-size: 0.4em;
            font-weight: 600;
            text-align: left;
        }
    }

    &:hover {
        border: 1px solid $selectedColor;
    }

    &.is-active {
        border: 1px solid $darkGrey;
        color: #fff;
        background-color: $selectedColor;

        &.is-manual-edit {
            background-color: #b72323;
        }
    }

    &.is-manual-edit {
        cursor: not-allowed;
    }


}

.item-selected {
    background-color: #f1f1f1;
    color: #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1em;
    padding: 0.3em 0.5em;
    font-weight: 600;
    margin-bottom: 0.5em;

    &.is-selected-by-other-user,
    &.is-websocket-disconnected {
        background-color: rgb(214, 78, 78);
        color: #fff;
    }
}

.sequence-block {
    width: 95%;
    font-size: 2em;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    margin-bottom: 0.3em;
    margin-top: 0.3em;
    background-color: white;

    &.is-active {
        border: 1px solid $darkGrey;
        color: #fff;
        background-color: $selectedColor;
    }
}

.drag-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: $lightGrey;
    transition: 200ms;
    padding-left: 0.5em;
    padding-right: 0.5em;
    cursor: pointer;

    &:hover {
        background-color: lighten($selectedColor, 0.2);
        color: #fff;
    }
}

.question-list-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    user-select: none;
    overflow: auto;

}

.is-disabled {
    color: lightgray;
    cursor: default;
    pointer-events: none;
}

.sequence-block.is-active .question-block {
    color: initial !important;
    background-color: white !important;
}

.sequence-block.is-active .drag-header {
    background-color: $selectedColor !important;
    color: white !important;
}

.sequence-block.is-active {
    background-color: white !important;
}

.sequence-block.cdk-drag-preview .drag-header {
    color: white !important;
    background-color: $selectedColor !important;
}

.question-block.cdk-drag-preview .question-block-label {
    font-size: 0.66666em !important; //It's missing some font-size application somewhere down the line but can't find it.
}

.is-v-hidden {
    visibility: hidden;
}

.single-item-mode {
    .panel-questions {
        justify-content: flex-start;
    }
}

.comments-toggle {
    margin-top: 15px;

    label {
        cursor: pointer;
        font-size: 120%;
        font-weight: 900;
        color: #555;
        display: flex;
        align-items: center;
        width: fit-content;

        input[type=checkbox] {
            margin-right: 0.5em;
        }
    }
}

.stack-editing-container {
    background-color: #fff;
    // margin: 2em;
    padding: 0.5em;
}

.stack-editing-element {
    margin: 0.5em;
    border: 2px solid #DCDCDC;
    box-sizing: border-box;
    background-color: #fff;
    overflow: hidden;
    color: #DCDCDC;
    padding: 12px 15px;
    
    font-size: 16px;
    font-weight: 700;
    line-height: 9px;
    letter-spacing: 0em;
    text-align: left;
}

.accept-all-changes-button {
    height: 50px;
    width: 100%;
    border-radius: 3px;
    
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 12px;
    letter-spacing: 0em;
    text-align: center;
}

.accept-all-changes-button:hover {
    background-color: #2592f8;
    cursor: pointer;
}


.edit-view-mode-selector {
    background-color: rgba(0, 0, 0, 0.6);
    box-shadow: 0 0 1em 0.5em rgba(0, 0, 0, 0.1);
    border-radius: 0.5em;
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    width: fit-content;
    height: fit-content;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0.5em;
    z-index: 1; // Buttons should stay above any question content
}

.mat-header-cell {
    font-weight: 800;
    font-size: 1em;
}

.mat-elevation-z8 {
    box-shadow: none;
}

.is-hi-contrast {
    // filter: invert(100%);
    // -ms-high-contrast: white-on-black;
    background-color: #000;
    color: #fff;

    .test-nav {
        background-color: orange;
        color: purple;

        .test-questions button.question {
            color: #000;
        }
    }

    .section-progress-marker {
        background-color: blue;
    }
}

.item-status {
    display: flex;
    justify-content: flex-start;
    gap: 10px;

    .item-status-label {
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 0.7em;
        gap: 0.7em;
        padding: 0.6em 1em 0.6em 0.7em;
        border: 2px solid lightgrey;
        font-size: 85%;

        .workflow-stage-order {
            background: #673ab7;
            color: white;
            height: 24px;
            width: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .pending-grpahics-icon {
            font-size: 20px;
            color: burlywood;
        }

        .status-text {
            font-weight: bold;
        }
    }
}
.split-question-container{
    display:flex; 
    flex-direction: column;
    justify-content: center;
}
.panel-preview-split-view{
    left: 0;
}
.lang-button {
    display: flex;
    align-items: center; 
  }
.lang-btn-container-split-view{
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translate(-0.85em, 0.5em)
}
.question-container-split-view{
    height: 100%;
    display: flex;
    justify-content: space-around;
}
.question-runner-left,
.question-runner-right {
  flex: 1;
  box-sizing: border-box;
  overflow: auto; 
  height: 35rem;
  padding-bottom: 1.5em;
  padding-right: 1.5em;
}
.question-runner-left{
    border-right: solid #888;
}
.divider-container {
    display: flex;
    margin-top: 1em;
  }
  
.divider {
    display: flex;
    align-items: center;
    flex: 1;
    flex-direction: column
}
.vertical-line {
    border-left: 1px solid #888;
    height: 1em; 
    margin: 0.3em 0.5em;
    
}

.revision-lang-filter{
    display: inline-flex;
    align-items: center;
    margin-right: 0.5em;
    input{
        margin-right: 0.25em;
    }
}


// this css is so that the recover button isn't opaque like the rest
.background-overlay-recover-btn {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(41, 124, 179, 1);
    /* Your RGBA color */
    z-index: -1;
}
::ng-deep .panel-preview{
    .is-hi-contrast{
            .runner-container{
                background-color: #fff;
                .question-container{
                    background-color: #000;
                    box-shadow: 0em 0em 1em rgba(0, 0, 0, 0.8);
                }
        }
    }
    .runner-container{
       .question-container{
           width: $pageContentWidth; // todo:CONSTANT figure out why this is not exactly 40-2*1em, also if 40 is the standard width, use a constant for all instances of its use.
           box-shadow: 0em 0em 1em rgba(0, 0, 0, 0.1);
           min-height: 10em;
           .question-panels{
            padding: 1em;
           }
       }
   }
}

.revision-load-message {
    text-align: center;
    margin: auto;
}

.revision-view-message {
    margin: 1em 0em -2em 1em;
}
.is-info {
    &:hover {
      background-color: #4388b7;
    }
}

.sunk-in-container {
    border: 1px solid #ccc; /* Light border */
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), 
                inset -2px -2px 5px rgba(255, 255, 255, 0.5); /* Inner shadow for sunk effect */
    border-radius: 4px; /* Optional: Rounded corners */
    padding: 16px; /* Optional: Space inside the container */
    background-color: #f9f9f9; /* Light background for contrast */
}

.no-background {
    background-color: transparent; 
    border: none
}

.display-review-config {
    margin-left: 0.5em; 
    flex: 1; 
    overflow: auto;  
    height: 35vh; 
    padding: 1em; 
}

.review-comment-options{
    display: flex;
    justify-content: space-between;
    padding: 0em 1em;
    margin-top: 1em;

    div{
        display: flex;
        align-items: center; 
    }

    label{
        margin-left: 0.5em;
    }
}
