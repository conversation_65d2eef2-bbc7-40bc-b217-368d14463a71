
<header 
  *ngIf="this.itemBankCtrl.isPsychometricViewEnabled && !previewCtrl.sampleTestForm" 
  [isLogoutHidden]="true"
  [breadcrumbPath]="breadcrumb" >
</header>

<div (click)="dropdown.hideAllDropdowns()" class="item-set-editor">

  <!--  *ngif="isInited" -->

  <div [ngStyle]="{display: itemBankCtrl.editModeItemId ? 'none' : 'block'}">
    <test-runner
            *ngIf="previewCtrl.sampleTestForm"
            [testFormType]="frameworkCtrl.testFormConstructionMethod.value"
            [testFormId]="previewCtrl.currentTestFormId"
            [currentTestDesign]="previewCtrl.sampleTestForm.currentTestDesign"
            [isTestletPreview]="previewCtrl.sampleTestForm.isTestletPreview"
            [frameWorkTags]="frameworkCtrl.asmtFmrk.tags"
            [questionSrcDb]="previewCtrl.sampleTestForm.questionSrcDb"
            [questionStates]="previewCtrl.sampleTestForm.questionStates"
            [testLang]="previewCtrl.sampleTestForm.testLang"
            [testTakerName]="previewCtrl.getTestTakerName()"
            [sectionIndexInit]="0"
            [questionIndexInit]="0"
            [regularTimeRemaining]="999"
            [testSessionId]="-1"
            [isPreview]="true"
            [isPrintMode]="previewCtrl.isTestFormPrintView"
            [isTimeEnabled]="!frameworkCtrl.asmtFmrk.isTimerDisabled"
            [documentItems]="frameworkCtrl.asmtFmrk.referenceDocumentPages"
            [helpPageItem]="frameworkCtrl.asmtFmrk.helpPageId"
            [rubricDownloadLink]="frameworkCtrl.asmtFmrk.rubricDownloadLink"
            [asmtFmrk]="frameworkCtrl.asmtFmrk"
            [isShowQuestionLabel]="true"
            [saveQuestion]="previewCtrl.saveQuestionResponse"
            [submitTest]="previewCtrl.submitTest"
            [postSubmit]="previewCtrl.showResults"
            [isExitEnabled]="true"
            [isShowingResults]="previewCtrl.isShowingResults"
            [exitResults]="previewCtrl.exitSampleTestForm"
            [ignoreDevTools]="true"
            [isQuestionnaire]="frameworkCtrl.asmtFmrk.isQuestionnaireAssessment"
            (exit)="previewCtrl.exitSampleTestForm()"
            (onEditItem)="editItem($event)"
            (onProcessLogsModal) = "openProcessLogsModal($event)"
    ></test-runner>
  </div>

  <div *ngIf="(getUser() | async) && (!previewCtrl.sampleTestForm || (previewCtrl.sampleTestForm && itemBankCtrl.editModeItemId))">
    <ng-container [ngSwitch]="!!printViewCtrl.isPrintModeActive()">
      <widget-simple-print-view *ngSwitchCase="true"
      [itemBankCtrl]="itemBankCtrl"
      [itemEditCtrl]="itemEditCtrl"
      [printViewCtrl]="printViewCtrl"
      [frameworkCtrl]="frameworkCtrl"
      ></widget-simple-print-view>
      <ng-container *ngSwitchCase="false">
        <ng-container [ngSwitch]="!itemBankCtrl.isPsychometricViewEnabled">
          <widget-authoring-main *ngSwitchCase="true" 
          [assetLibraryCtrl]="assetLibraryCtrl"
          [frameworkCtrl]="frameworkCtrl"
          [auditCtrl]="auditCtrl"
          [itemBankCtrl]="itemBankCtrl"
          [itemEditCtrl]="itemEditCtrl"
          [itemFilterCtrl]="itemFilterCtrl"
          [memberAssignmentCtrl]="memberAssignmentCtrl"
          [panelCtrl]="panelCtrl"
          [previewCtrl]="previewCtrl"
          [printViewCtrl]="printViewCtrl"
          [publishingCtrl]="publishingCtrl"
          [quadrantCtrl]="quadrantCtrl"
          [saveLoadCtrl]="saveLoadCtrl"
          [testFormGen]="testFormGen"
          [testletCtrl]="testletCtrl"
          ></widget-authoring-main>
          <widget-framework-main 
            *ngSwitchCase="false" 
            [previewCtrl]="previewCtrl"
            [frameworkCtrl]="frameworkCtrl"
            [assetLibraryCtrl]="assetLibraryCtrl"
            [auditCtrl]="auditCtrl"
            [itemBankCtrl]="itemBankCtrl"
            [itemEditCtrl]="itemEditCtrl"
            [itemFilterCtrl]="itemFilterCtrl"
            [memberAssignmentCtrl]="memberAssignmentCtrl"
            [panelCtrl]="panelCtrl"
            [printViewCtrl]="printViewCtrl"
            [publishingCtrl]="publishingCtrl"
            [quadrantCtrl]="quadrantCtrl"
            [saveLoadCtrl]="saveLoadCtrl"
            [testFormGen]="testFormGen"
            [testletCtrl]="testletCtrl"
          ></widget-framework-main>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
  <widget-item-params-import 
    *ngIf="frameworkCtrl.isImportOpen" 
    [frameworkCtrl]="frameworkCtrl"
  ></widget-item-params-import>
  <widget-element-import 
    *ngIf="itemEditCtrl.isElementImportExportOpen" 
    [itemBankCtrl]="itemBankCtrl"
    [itemEditCtrl]="itemEditCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
  ></widget-element-import>
  <widget-element-restore 
    *ngIf="itemEditCtrl.isElementRestoreOpen" 
    [itemBankCtrl]="itemBankCtrl"
    [itemEditCtrl]="itemEditCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
  ></widget-element-restore>
  <widget-assign-user 
    *ngIf="memberAssignmentCtrl.openedAssignWindowNote || memberAssignmentCtrl.openedAssignWindowRequest"
    [memberAssignmentCtrl]="memberAssignmentCtrl"
  ></widget-assign-user>
  <div class="custom-modal" *ngIf="assetLibraryCtrl.isAssetLibraryOpen" style="z-index: 100;">
    <div class="modal-contents is-max-size">
      <!-- <div class="simple-content-bounds">
        <asset-library [initEditing]="assetLibraryCtrl.initAssetEditing" [focusedField]="assetLibraryCtrl.focusedAssetEditField" [activeElement]="assetLibraryCtrl.currentAsset" (close)="assetLibraryCtrl.closeAssetLibrary()"></asset-library>
        <button (click)="assetLibraryCtrl.closeAssetLibrary()" style="position: absolute; bottom: 1em; left: 1em;" class="button is-danger">Close</button> -->
      <div>
        <asset-library 
            [initEditing]="assetLibraryCtrl.initAssetEditing" 
            [focusedField]="assetLibraryCtrl.focusedAssetEditField" 
            [activeElement]="assetLibraryCtrl.currentAsset" 
            [itemElement]="assetLibraryCtrl.itemElement"
            [assetId]="assetLibraryCtrl.assetId"
            [itemBankCtrl]="itemBankCtrl"
            (close)="assetLibraryCtrl.closeAssetLibrary()">
        </asset-library>
        <button (click)="assetLibraryCtrl.closeAssetLibrary()" style="position: absolute; bottom: 1em; left: 1em;" class="button is-danger"><tra slug="ie_close_modal"></tra></button>
      </div>
    </div>
  </div>

</div>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type" [ngStyle]="{'width': cModal().type === IseModal.PROCESS_LOGS ? '80vw' : 'auto'}">
        <div *ngSwitchCase="IseModal.PROCESS_LOGS" style="height: 75vh; overflow-y: scroll;">
          <table>
            <tr>
                <th>event</th>
                <th>localTimestamp</th>
                <th>section_index</th>
                <th>question_index</th>
                <th style="width:8em">info</th>
                <th>isSimulated</th>
                <th>counter</th>
            </tr>
            <tr *ngFor="let log of cmc()">
                <td><code>{{log.slug}}</code></td>
                <td>{{log.data.localTimestamp}}</td>
                <td>{{log.data.state.section_index}}</td>
                <td>{{log.data.state.question_index}}</td>
                <td>
                  <ngx-json-viewer [json]="log._info" [expanded]="false" ></ngx-json-viewer>
                </td>
                <td>{{log.data.isSimulated}}</td>
                <td>{{log.counter}}</td>
            </tr>
          </table>
        </div>
      </div>
      <modal-footer [pageModal]="pageModal"></modal-footer>
  </div>
</div>