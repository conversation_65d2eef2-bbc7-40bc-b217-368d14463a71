import * as _ from 'lodash';
// app services
import { AuthService } from '../../../api/auth.service';
import { LangService } from '../../../core/lang.service';
import { RoutesService } from '../../../api/routes.service';
import { ScriptGenService } from '../../script-gen.service';
import { ItemFilterCtrl } from './item-filter';
import { ItemBankCtrl } from './item-bank';
import {
  collectAndEnsureEntryIds,
  getQuestionConfiguredEA,
  identifyQuestionResponseEntries
} from '../models/expected-answer';
import { ItemSetFrameworkCtrl } from './framework';
import { LangId, possibleLanguages} from '../models/constants';
import { ElementType, getElementWeight, IContentElement,  IQuestionConfig, ScoringTypes, } from '../../../ui-testrunner/models';
import { PARAM_SPECIAL_FLAGS } from '../../framework-dimension-editor/model';
import { FrameworkQuadrantCtrl } from './quadrants';
import { getElementChildren, ExpectedAnswer} from '../models';
import { EStatus, ILibraryAsset} from '../../asset-details/types'
import { IPanelModulesConfig, TestFormConstructionMethod } from '../models/assessment-framework';
import { IContentElementMcq, IEntryStateMcq, McqDisplay, mcqOptionLabels } from '../../../ui-testrunner/element-render-mcq/model';
import { IContentElementInput, InputFormat } from '../../../ui-testrunner/element-render-input/model';
import { ElementTypeDefs } from '../../../ui-testrunner/models/ElementTypeDefs';
import { TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { Destroyable } from './destroyable';
import { ItemEditCtrl } from './item-edit';

import { IContentElementSelectionTable } from '../../../ui-testrunner/element-render-selection-table/model';
import { IContentElementMoveableDragDrop } from '../../../ui-testrunner/element-render-moveable-dnd/model';
import { IContentElementDndDraggable } from '../../../ui-testrunner/element-render-dnd/model';
import { IContentElementGroup } from '../../../ui-testrunner/element-render-grouping/model';
import { IContentElementInsertion } from '../../../ui-testrunner/element-render-insertion/model';
import { IContentElementVideo } from '../../../ui-testrunner/element-render-video/model';
import { IContentElementImage } from '../../../ui-testrunner/element-render-image/model';
import { IContentElementAudio } from '../../../ui-testrunner/element-render-audio/model';
import { IContentElementMcqOption } from 'src/app/ui-testrunner/element-render-mcq/model';
import { AuditConfigs, IAuditConfig, EAuditElementType, IAuditFilterScopedQuestions, AuditQuestionScope, IAuditPatch, ItemTypesParam, ITEM_MAP_MODULE_META_AUDIT_CONST_LABELS, SubAuditExcludedItems, AuditTarget, } from 'src/app/ui-item-maker/widget-audits/data/audits'
import { processCheck, IAuditResult, AuditResultCore, AuditResultMeta, proccessInformativeOnly } from 'src/app/ui-item-maker/widget-audits/util/checks';
import { applyItemFilter, extractTestDesignScopedquestion, getQuestionsByScope, getScreens, IContext } from 'src/app/ui-item-maker/widget-audits/util/item-scopes';
import { itemContentDiff, legacyItemContentDiff } from 'src/app/ui-item-maker/widget-audits/util/content-diff';
import { checkPropCondVal, countKeyValues, getQuestionContent, getQuestionDeepElements, getQuestionPoints, getQuestionRespondableDeep, isNullOrEmptyOrUndefined } from 'src/app/ui-item-maker/widget-audits/util/content-analysis';
import { processPatch } from '../../widget-audits/util/patches';
import { IExpansionPanelContent,ExpansionPanelContentType } from 'src/app/ui-partial/expansion-panel/expansion-panel.component';
import { IContentElementTemplate } from 'src/app/ui-testrunner/element-render-template/model';
import getAssetSize from 'src/app/ui-item-maker/widget-audits/util/get-asset-size';
import { scoreMatrixElementTypes } from '../../config-score-matrix/models';
import { QuestionView } from '../item-set-editor.component';

export interface IAuditFormattedResult {
  key: string
  description: string;
  isAutoFixable?: boolean;
  items: any[];
  num_issues: number;
}
export interface IAssetResult {
  size: number;
  url?: string;
  entryId?: number; 
  type?: ElementType | string;
}
interface IQuestionResult {
  qid: number;
  results: IAssetResult[];
  totalSize: number;
  imageSize: number;
  audioSize: number;
}

enum AuditErrors {
  INVALID_ASSESMENT_TYPE = 'INVALID_ASSESMENT_TYPE',
  NO_PANELS_TO_AUDIT = 'NO_PANELS_TO_AUDIT'
}

export class ItemBankAuditor implements Destroyable {
  
  voiceoverAuditResults;
  
  auditsRan: {[key: string]: boolean} = {};
  auditsFixProgress: {[key: string]: {count: number, total: number}} = {};
  auditQuestionMem: {[memId: string]: any} = {};
  activeAuditItemMemId: string;
  auditResultHeader: string;
  auditItemListingMode:AuditTarget;

  auditFilterScopedQuestions: IAuditFilterScopedQuestions = {
    ITEMS_SCORED: false,
    ITEMS_SCORED_MINUS_FT: false,
    ITEMS_HUMAN_SCORED: false,
    ITEMS_SURVEY:false,
    SCREENS: false,
    SCREENS_NONSCORED: false,
    SCREENS_PASSAGES: false,
  };

  customIterators:{[auditSlug:string] : (auditResultsModel:AuditResultCore, skipDisabled?:boolean) => Promise<AuditResultMeta>} = {};
  customAutoFixes:{[patchSlug:string] : (auditSlug:string, patchSlug:string) => void} = {};
  auditLogs: any[];
  auditsRunning: {[key: string]: boolean} = {};
  auditsWithError: {[key: string]: boolean} = {};
  
  constructor(
    public scriptGen: ScriptGenService,
    public itemFilterCtrl:ItemFilterCtrl,
    public itemBankCtrl:ItemBankCtrl,
    public frameworkCtrl:ItemSetFrameworkCtrl,
    public quadrantCtrl: FrameworkQuadrantCtrl,
    public auth: AuthService,
    public routes: RoutesService,
    private lang: LangService,
    public itemEditCtrl: ItemEditCtrl
  ){
    this.initAudits()
    this.initAuditAutoFixes()
  }

  destroy() {

  }

  async runCustomCleaningPatch(audit:IAuditConfig, patchSlug:string){
    const auditSlug = audit.slug;
    if (audit.isCompleteFix){
      const patch = audit.autoFixes;
      // const {questions} = await this.renderAuditResultsModel(auditSlug); // todo: sub optimal. should be getting the list of questions from the parent check
      this.runQuestionAutoFixes( patchSlug, [patch.checkCompositeSlug], async (questionFixing: IQuestionConfig) => {
        await processPatch(audit, patch, questionFixing, {
          itemBankCtrl: this.itemBankCtrl,
          frameworkCtrl: this.frameworkCtrl,
        })
      })
    }
    else {
      if (typeof this.customAutoFixes[patchSlug] === 'function') {
        await this.customAutoFixes[patchSlug](auditSlug, patchSlug);
      }
      else {
        alert('Cleaning fix is not configured for '+patchSlug)
      }
    }
  }
  
    
  async runCustomAudit(auditSlug:string, scope:AuditQuestionScope, isLangSensitive:boolean = true, auditTarget:AuditTarget){
    this.markAuditRunning(auditSlug, true);
    try{
      const auditResultsModel = await this.renderAuditResultsModel(auditSlug, scope, isLangSensitive);
      let skipResultsRollUp = false
      // console.log(auditSlug, typeof this.customIterators[auditSlug])
      if (typeof this.customIterators[auditSlug] === 'function') {
        const {isCustomResults} = await this.customIterators[auditSlug](auditResultsModel);
        skipResultsRollUp = isCustomResults
      }
      else if (!auditResultsModel.audit.isCompleteCheck) { // skip 
        alert('Audit is not configured for ' + auditSlug)
      }

      const commonCheckProps = auditResultsModel.audit.commonCheckProps || {};
      const checks = auditResultsModel.audit?.checks?.map(check => _.merge({}, commonCheckProps, check)) || []; // side effect, breaks symbolic link
      for (let check of checks){
        if (check.checkType){
          await processCheck(check, auditResultsModel.auditResultsMap, auditResultsModel.questions, {
            itemBankCtrl: this.itemBankCtrl,
            frameworkCtrl: this.frameworkCtrl,
          })
        }
      }
      if (!skipResultsRollUp){
        this.populateResultsInAuditItemMem(auditResultsModel.checkIds, auditSlug, auditResultsModel.auditResults, auditResultsModel.auditResultsMap, auditTarget);
        const results = this.getFormattedResults(auditResultsModel.checkIds, auditResultsModel.auditResultsMap);  
        const isTestletOnly = false;
        const isRan = true;
        this.markAuditAsRan(auditSlug, isTestletOnly, isRan, results); 
        if(this.auditsWithError[auditSlug] = true){
          this.auditsWithError[auditSlug] = false
        }
      }
    } catch(e){
      console.error(e);
      this.auditsWithError[auditSlug] = true
    }
    this.markAuditRunning(auditSlug, false);
  }

  
  private getAuditBySlug(auditSlug:string) : IAuditConfig | null {
    for (let audit of AuditConfigs){ // todo: this should be coming from the DB
      if (audit.slug === auditSlug){
        return audit;
      }
    }
    return null;
  }

  private async renderAuditResultsModel(auditSlug:string, scope:AuditQuestionScope, isLangSensitive:boolean = true) : Promise<AuditResultCore> {
    const audit = this.getAuditBySlug(auditSlug);
    let checkIds = [];
    const auditResults:IAuditResult[] = [];
    if (audit && audit.checks){
      checkIds = audit.checks.map(check => check.id);
      const {excludedItems = [], informativeOnly} = audit.commonCheckProps ?? {};
      for (let check of audit.checks){
        auditResults.push({
          id: check.id, 
          caption: check.caption,
          typeCaption: check.typeCaption,
          type: check.checkElType || audit?.commonCheckProps?.checkElType || 'QUESTION',
          excludedItems: check?.excludedItems ?? excludedItems,
          informativeOnly: proccessInformativeOnly(check?.informativeOnly, informativeOnly, this.getContext()),
          items: [], 
          autoFixContext: []
        })
      }
    }
    const questions = (await this.getQuestionsByScope(scope, isLangSensitive)).filter(q => !!q);
    const auditResultsMap: Map<string, IAuditResult> = new Map();
    auditResults.forEach(result => auditResultsMap.set(result.id, result))
    const auditResultCore:AuditResultCore =  {
      audit, // should be read only, please
      auditSlug,
      checkIds,
      questions,
      auditResults, 
      auditResultsMap,
    }
    switch(audit.auditTarget){
      case AuditTarget.MSCAT_PANELS:
        auditResultCore.mscatPanels = this.frameworkCtrl.asmtFmrk.panels.filter(panel => !panel.isDisabled);
        if(!auditResultCore.mscatPanels?.length && this.frameworkCtrl.asmtFmrk.testFormType === TestFormConstructionMethod.MSCAT){
          alert("Please ensure assesment is MSCAT and there are enabled panels to audit")
          throw new Error(AuditErrors.NO_PANELS_TO_AUDIT)
        }
        break;
      case AuditTarget.QUESTIONS:
        break;
    }
    return auditResultCore
  }

  private populateResultsInAuditItemMem(checkIds:string[], auditSlug, auditResults, auditResultsMap, auditTarget:AuditTarget) {
    this.auditQuestionMem[auditSlug+"_RESULTS"] = auditResults;
    for (let checkId of checkIds){
      // .filter(key => !isNaN(Number(CheckId[key])))
      const result = auditResultsMap.get(checkId);
      let items;
      switch(auditTarget){
        case AuditTarget.MSCAT_PANELS:
          items = result.items;
          break;
        default:
        case AuditTarget.QUESTIONS:
          items = applyItemFilter(result.items, result.excludedItems)?.map((i => this.itemBankCtrl.getQuestionById(i.id)));
          break;
      }
      this.auditQuestionMem[auditSlug+"_"+checkId] = items;
      this.auditQuestionMem[auditSlug+"_"+checkId + '_AUTOFIX_CONTEXT'] = result.autoFixContext ?? [];
      this.auditQuestionMem[auditSlug+"_RESULTS"].find(subAudit => subAudit.id === checkId).items = items;
    }
  }
  
  private getFormattedResults(checkIds:string[], auditResultsMap) {
    const results : IAuditFormattedResult[] =  []
    for (let checkId of checkIds){
      // .filter(key => !isNaN(Number(CheckId[key])))
      const result = auditResultsMap.get(checkId);
      const items = applyItemFilter(result.items, result.excludedItems)
      results.push({
        key: checkId,
        description: result.caption,
        isAutoFixable: result.isAutoFixable || false,
        items: items?.map(i => this.itemBankCtrl.getQuestionById(i?.id)),
        num_issues: result.informativeOnly ? 0 : result.items?.length ?? 0
      })
    };
    return results;
  }
  
  private async markAuditAsRan(auditSlug: string, isTestletsOnly: boolean, isRan: boolean= true, results?: any) {
    if (isTestletsOnly) {
      this.auditsRan[auditSlug + '/TESTLETS'] = isRan;
    }
    let num_issues = 0
    results.forEach(result =>{
      num_issues += result.num_issues ?? result.items?.length ?? 0;
    })
    const formattedResults:any = {
      item_set_id: this.itemBankCtrl.customTaskSetId, 
      audit_slug: auditSlug, 
      audit_results: JSON.stringify(results),
      lang: this.lang.c(),
      num_issues
    }
    // log
    formattedResults.audited_on = new Date();
    formattedResults.audited_by_uid = this.auth.getUid();
    formattedResults.email = this.auth.u().email;
    formattedResults.name = this.auth.getDisplayName();
    this.updateAudit(auditSlug, formattedResults);
    this.auditsRan[auditSlug] = isRan;
    await this.auth.apiCreate(this.routes.TEST_AUTH_ITEM_SET_AUDITS, formattedResults);
  }

  updateAudit(auditSlug:string, formattedResults:any){
    let auditIndex = this.auditLogs.findIndex(log => log.audit_slug == auditSlug);
    if(auditIndex === -1){
      auditIndex = this.auditLogs.length ?? 0;
    }
    this.auditLogs[auditIndex] = formattedResults;
  }

  // resets the state to all listed audits so that they look "ready to run" as opposed to "already run"
  refreshAudit(auditSlugs:string[]) {
    if(auditSlugs.some(slug => this.activeAuditItemMemId?.includes(slug))){
      this.clearAuditPanels();
      this.clearAuditQuestions();
    }
    auditSlugs.forEach(slug => {
      if(this.auditQuestionMem.hasOwnProperty(slug)){
        delete this.auditQuestionMem[slug];
      }
      if(this.auditsRan.hasOwnProperty(slug)){
        delete this.auditsRan[slug];
      }
    });
  }
  
  // generalized autofix algorithm that runs in series for any process that requires it (this cannot function without a process fix)
  runQuestionAutoFixes(fixFlag:string, questionMems:string[], processFix:(question:IQuestionConfig)=>Promise<any> ) {
    return new Promise( (resolve, reject) => {
      this.auditQuestionMem[fixFlag] = {i:0, n:1};
      let questions:IQuestionConfig[] = [];
      questionMems.forEach(questionMemKey => {
        questions = questions.concat(this.auditQuestionMem[questionMemKey]);
      })
      this.auditQuestionMem[fixFlag].n = questions.length - 1 
      const questionLabels = [];
      questions.forEach(question => {
        if (questionLabels.indexOf(question?.label) === -1){
          questionLabels.push(question?.label);
        }
      })
      let currentQuestionIndex = 0;
      let currentQuestion;
      const fixNextQuestion = () => {
        this.auditQuestionMem[fixFlag].i = currentQuestionIndex
        if (currentQuestionIndex >= questionLabels.length){
          this.itemBankCtrl.selectQuestion(currentQuestion, true)
          this.auditQuestionMem[fixFlag] = false;
          alert('All questions have been fixed where possible. Please refresh the page in order to run another audit.');
          return;
        }
        const questionLabel = questionLabels[currentQuestionIndex];
        currentQuestion = this.itemBankCtrl.getQuestionByLabel(questionLabel);
        this.itemBankCtrl.selectQuestion(currentQuestion).then(()=>{
          currentQuestion = this.itemBankCtrl.getQuestionByLabel(questionLabel);
          processFix(currentQuestion).then(() => {
            currentQuestionIndex++;
            fixNextQuestion();
          })
        })
      }
      if (questionLabels.length > 0){
        fixNextQuestion()
      }
      else{
        this.auditQuestionMem[fixFlag] = false;
        resolve(undefined);
      }
    })
  }
      
  afterQuestionAutoFixes(){
    alert('There are no questions that can be auto-fixed.');
  }
          
  checkActiveQuestionMem(auditMemId: string) {
    if (this.activeAuditItemMemId === auditMemId) {
      return true;
    }
  }

    activateAuditQuestions(auditMemId: string) {
    this.auditItemListingMode = AuditTarget.QUESTIONS;
    this.activeAuditItemMemId = auditMemId;
    this.itemBankCtrl.currentItemListPage = 1;
    this.itemFilterCtrl.updateItemFilter();
    this.frameworkCtrl.scrollToQuestionListing();
  }
  
  clearAuditQuestions(){
    this.activateAuditQuestions(null);
  }

  activeAuditPanels: IPanelModulesConfig[];
  activeAuditPanelIds: (number | string)[];
  activateAuditPanels(auditMemId: string){
    this.auditItemListingMode = AuditTarget.MSCAT_PANELS;
    this.activeAuditItemMemId = auditMemId;
    this.activeAuditPanels = this.auditQuestionMem[auditMemId];
    this.activeAuditPanelIds = this.activeAuditPanels?.map((panel) => panel.id);
  }

  clearAuditPanels(){
    this.activateAuditPanels(null);
  }
  
  // brought over from ABED
  initAuditAutoFixes(){

    this.customAutoFixes['CLONES_IS_AUTOFIXING'] = async (auditSlug:string, patchSlug:string) => {
      const cloneLinkParam = this.frameworkCtrl.identifySpecialParams(PARAM_SPECIAL_FLAGS.CLONE_LINKER)[0];
      this.runQuestionAutoFixes(
        'CLONES_IS_AUTOFIXING',
        [
          'Q_CLONES_MISSING_PARAM',
          'Q_CLONES_INVALID_PARAM',
        ],
        (questionFixing: IQuestionConfig) => {
          const question = questionFixing;
          const getCloneLabelCore = (label: string) => label.split('/')[0];
          const labelCore = getCloneLabelCore(question.label);
          question.meta[cloneLinkParam.code] = labelCore;
          return Promise.resolve();
        }
      )
      .then(() => this.afterQuestionAutoFixes() )
    }

    this.customAutoFixes['VOICEOVER_IS_AUTOFIXING'] = async (auditSlug:string, patchSlug:string) => {
      // const auditResultsMap = this.voiceoverAuditResults;
      enum LangId { EN = 'en', FR = 'fr' }
      enum CheckId { 'MISSING_OVERALL_VOICE', 'MISSING_OPTION_VOICE', 'CONTAIN_DUPLICATED_OPTION_VOICE' };
      const langs = [LangId.EN, LangId.FR];
      const renderAuditId = (checkId:CheckId, langId:LangId) => CheckId[checkId]+'/'+langId;
      const busyFlag = patchSlug;
      Promise.all( 
        langs.map( langId => Promise.all([
          this.runQuestionAutoFixes( busyFlag, [renderAuditId(CheckId.MISSING_OVERALL_VOICE, langId)], (questionFixing:IQuestionConfig) => {
            let question = questionFixing;
            if (langId === 'fr'){
              question = question.langLink;
            }
            let script = this.scriptGen.autoGenQuestionVoiceover(question, langId, true);
            return this.scriptGen.uploadNewVoice(script, question.voiceover, langId)
          }),
          this.runQuestionAutoFixes( busyFlag, [renderAuditId(CheckId.MISSING_OPTION_VOICE, langId), renderAuditId(CheckId.CONTAIN_DUPLICATED_OPTION_VOICE, langId)], (questionFixing:IQuestionConfig) => {
            let question = questionFixing;
            if (langId === 'fr'){
              question = question.langLink;
            }
            console.log('fixing options', langId, questionFixing.label, question.voiceover);
            const entries = identifyQuestionResponseEntries(question.content, []);
            return Promise.all(
              entries.map(entry => {
                if (entry.elementType === ElementType.MCQ){
                  const mcqEntry:IContentElementMcq = <any> entry;
                  if (mcqEntry.options){
                    return Promise.all(
                      mcqEntry.options.map((option, option_index) => {
                        const scriptMeta:any = {
                          optionScripts: [],
                          useOldScripts: true,
                          useOldScriptsDecision: true,
                        }
                        return this.scriptGen.extractScriptFromMcqNodeAsync(mcqEntry, langId, scriptMeta, []);
                      })
                    )
                  }
                }
              })
              )
            }),
          ]))
        )
      .then(() => this.afterQuestionAutoFixes() )
    }

    this.customAutoFixes['EXP_ANS_IS_AUTOFIXING'] = async(auditSlug:string, patchSlug:string) => {
      this.auditQuestionMem['EXP_ANS_IS_AUTOFIXING'] = {n:0 , i:0}
      const auditsQuestionMemo = this.auditQuestionMem['EXP_ANS_IS_AUTOFIXING'];
      const ranCode = 'EXP_ANS';
      const singleMcqMissingSimulatedSub = ranCode + '_' + 'Q_EXP_ANS_MISSING';
      const mcqMisMatchSimulatedSub = ranCode + '_' + 'Q_EXP_ANS_MISMATCH';
      
      const lang = this.lang.c();
      const questionsMissingExpectedAns: IQuestionConfig[] = this.auditQuestionMem[singleMcqMissingSimulatedSub];
      const questionsMismatchedExpectedAns: IQuestionConfig[] = this.auditQuestionMem[mcqMisMatchSimulatedSub];
      const missMatchedExpectedAns = this.auditQuestionMem[auditSlug+"_"+ 'Q_EXP_ANS_MISMATCH' + '_AUTOFIX_CONTEXT']
      const MCQ_OPTION_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
      auditsQuestionMemo.i = 0;
      auditsQuestionMemo.n = (questionsMissingExpectedAns.length ?? 0 ) + (missMatchedExpectedAns?.length ?? 0);

      const failedAddingSimulatedSub = [];
      const successAddingSimulatedSubQs = new Set();
      const failedAddingCorrectSimulatedSubQs = new Set();
      const failedToRemoveMisConfigQs = new Set();
      const removedMisConfigQs = new Set();

      const getMcqEntryState = (): IEntryStateMcq => {
        return {
          type: 'mcq',
          isCorrect: false,
          isStarted: false,
          isFilled: false,
          isResponded: false,
          selections: [],
          alreadyScrambled: undefined,
          score:  0,
          weight: 0,
          scoring_type: ScoringTypes.AUTO, 
        }
      };

      const promises: Promise<any>[] = []
      
      
      for (const qConfig of questionsMissingExpectedAns) {
        const contents = this.getQuestionContent(qConfig);
        const scorableContent = this.getQuestionContentEntryElements(contents, true);
        const el = (<IContentElementMcq>scorableContent[0]);
        
        const expectedAnswers = (await this.itemEditCtrl.findAllExpectedAnswer(+qConfig.id))?.filter(ea => ea.lang === lang) ;
        
        const expectedAnswersSet = new Set();
        expectedAnswers.forEach(ans => { expectedAnswersSet.add(ans.formatted_response.trim()) });
        
        let noSpecifiedResponse = true;
        const mcqOptions: IContentElementMcqOption[] = el.options;
        const weight = getElementWeight(el);
        mcqOptions?.forEach((option, i) => {
          const currentOptionAsAns = MCQ_OPTION_MAP[i];
          const isCorrect = option.isCorrect;
          if(isCorrect) noSpecifiedResponse = false;
          if(!expectedAnswersSet.has(currentOptionAsAns)){
            // add to formatted resp
            const entryState = getMcqEntryState();
            entryState.isFilled = true;
            entryState.isResponded = true;
            entryState.isStarted = true;
            entryState.isCorrect = isCorrect;
            entryState.score = isCorrect ? weight : 0;
            entryState.weight = weight
            entryState.selections = [{
                i,
                id: option.optionId,
                elementType: option.elementType,
                content: option.elementType === ElementType.IMAGE ?  option.url : option.content                 
            }]

            const response = { [el.entryId] : entryState }                
            const p = this.addFormattedAnswerToSimulatedSubmission(qConfig, JSON.stringify(response))
            .then((res) => successAddingSimulatedSubQs.add(+qConfig.id))                 
            .catch((e) => failedAddingSimulatedSub.push({label: qConfig.label , id: qConfig.id, option}))
            .finally(() => auditsQuestionMemo.i++);
            promises.push(p);
          }
        });
        if(noSpecifiedResponse) failedAddingCorrectSimulatedSubQs.add(+qConfig.id);
        
      }

      for (const context of missMatchedExpectedAns){
        const {item_id, formatted_response, lang = this.lang.c()} = context
        const p = this.auth.apiRemove(this.routes.ITEM_SET_EXPECTED_ANS, item_id, {query:{formatted_response, lang}})
          .then(() => removedMisConfigQs.add(+item_id))
          .catch(() => failedToRemoveMisConfigQs.add(+item_id))
          .finally(() => auditsQuestionMemo.i++);
            promises.push(p);
      }
      
      await Promise.all(promises);

      this.auditQuestionMem[singleMcqMissingSimulatedSub] = questionsMissingExpectedAns?.filter((q) => !successAddingSimulatedSubQs.has(+q.id) && !failedAddingCorrectSimulatedSubQs.has(+q.id));
      this.auditQuestionMem[mcqMisMatchSimulatedSub] = questionsMismatchedExpectedAns?.filter((q) => failedToRemoveMisConfigQs.has(+q.id));
      let completionAlert = "Completed Running Autofix:"
      if(successAddingSimulatedSubQs.size){
        completionAlert += '\n - Succesfully added simulated responses';
      }
      if(failedAddingCorrectSimulatedSubQs.size){
        completionAlert += '\n - Fix no response entry audit so that correct missing simulated expected answer can be fixed';
      }
      if(failedAddingSimulatedSub.length){
        completionAlert += `\n - Unkown error generating response for following questions: ${failedAddingSimulatedSub.join(', ')}`;
      }
      if(removedMisConfigQs.size){
        completionAlert += `\n - Succesfully removed misconfigured responses`;
      }
      if(failedToRemoveMisConfigQs.size){
        completionAlert += `\n - Failed to removed misconfigured responses for following questions: ${Array.from(failedToRemoveMisConfigQs).join(', ')}`;
      }
      alert(completionAlert);
      this.auditQuestionMem['EXP_ANS_IS_AUTOFIXING'] = null;
    }

  }

  private getQuestionContentEntryElements(elements: IContentElement[], isAutoScoreable:boolean) {
    return <IContentElement[]>identifyQuestionResponseEntries(elements, [], isAutoScoreable);
  }
  
  async addFormattedAnswerToSimulatedSubmission(qConfig: IQuestionConfig, response_raw:string) {
      
    const data = {
      response_raw,
      test_question_id: +qConfig.id,
      item_set_id: this.itemBankCtrl.customTaskSetId,
      lang: this.lang.c(),
      create_expected_response: true
    }

    return this.auth.apiCreate(this.routes.SIMULATE_EXTRACT_RESPONSE, data);      
  }

  trackQuestionIssues = (key: string, isFixable: boolean, questionList: IQuestionConfig[], description: string, issueCategories: any[], totalIssues: number) => {
    this.auditQuestionMem[key] = questionList;
    const numIssues = questionList?.length;
    this.auditQuestionMem['NUM_' + key] = numIssues;
    issueCategories.push({
      key,
      isFixable,
      description
    });
    totalIssues += numIssues;
  };

  // This is a function used to initialize all audits so that both audits done through custom logic and audits done through the data structure can work together
  initAudits(){
    
    this.customIterators['QUESTION_UTIL'] = async (auditResultsModel:AuditResultCore) => {
        
      const {checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        UTIL_TESTLET = 'UTIL_TESTLET',
        UTIL_QUAD = 'UTIL_QUAD'
      };

      const quadrantQuestionRef = new Map();
      const testletQuestionRef = new Map();
      this.quadrantCtrl.refreshQuadrantItems();
      this.frameworkCtrl.asmtFmrk.quadrantItems.forEach(quadrantItemSet => {
        quadrantItemSet.questions.forEach(question => {
          quadrantQuestionRef.set(question.label, true);
        });
      });
      this.frameworkCtrl.asmtFmrk.testlets.forEach(testlet => {
        testlet.questions.forEach(question => {
          testletQuestionRef.set(question.label, true);
        });
      });
      this.itemBankCtrl.getItems().forEach(question => {
        if (!quadrantQuestionRef.get(question.label)) {
          auditResultsMap.get(CheckId.UTIL_QUAD).items.push(question)
          // questionNoQuadrant.push(question);
        }
        if (!testletQuestionRef.get(question.label)) {
          auditResultsMap.get(CheckId.UTIL_TESTLET).items.push(question)
          // questionNoTestlet.push(question);
        }
      });
      this.auditQuestionMem['NUM_Q_NO_QUAD'] = auditResultsMap.get(CheckId.UTIL_QUAD).items;
      this.auditQuestionMem['NUM_Q_NO_TESTLET'] = auditResultsMap.get(CheckId.UTIL_TESTLET).items;

      return {}
    }

    
    this.customIterators['QUESTION_LABEL'] = async (auditResultsModel:AuditResultCore) => {
      
      const {questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        Q_LABEL_DUPE = 'Q_LABEL_DUPE'
      };

      const duplicates = [];
      const questionsSeen = new Map();
      const questionsFirst = new Map();
      for (const question of questions){
        const label = question.label;
        if (questionsSeen.has(label)) {
          auditResultsMap.get(CheckId.Q_LABEL_DUPE).items.push(question);
          const previousQuestion = questionsFirst.get(label);
          if (previousQuestion) {
            auditResultsMap.get(CheckId.Q_LABEL_DUPE).items.push(question);
            questionsFirst.set(label, false);
          }
        } else {
          questionsSeen.set(label, true);
          questionsFirst.set(label, question);
        }
      }

      return {}
    }

    
    this.customIterators['EXP_ANS'] = async (auditResultsModel:AuditResultCore) => {
      
      const {questions, auditResultsMap} = auditResultsModel;
      enum CheckId {
        Q_EXP_ANS_MISSING = "Q_EXP_ANS_MISSING",
        Q_EXP_ANS_MISMATCH = "Q_EXP_ANS_MISMATCH",
        Q_EXP_ANS_LANG_MISMATCH = "Q_EXP_ANS_LANG_MISMATCH",
        Q_EXP_ANS_MULTI_ENTRY = "Q_EXP_ANS_MULTI_ENTRY",
        Q_EXP_ANS_NO_ENTRY = "Q_EXP_ANS_NO_ENTRY",
        Q_EXP_ANS_NOT_MCQ = "Q_EXP_ANS_NOT_MCQ",
        Q_EXP_ANS_NO_ACC = "Q_EXP_ANS_NO_ACC",
      }
      const supportedLangs = this.itemBankCtrl.getSupportedLangs();
      const supportedLangsResponseMap = await this.itemBankCtrl.getLangExpectedAnswerMap(supportedLangs);
      const expectedAnswersCurrLang = supportedLangsResponseMap.get(this.lang.c());
      for(let question of questions){
        const failedMap = {};
        const respondableElements = getQuestionRespondableDeep(question);
        const itemTypesCount = countKeyValues(respondableElements, 'elementType', 1);
        const {correct_responses, weight}= expectedAnswersCurrLang.get(question.id)??{weight: null, correct_responses:[]};
        const correct_responses_set = new Set(correct_responses)
        const response_length = correct_responses.length;
        let isMCQ = false;
        if(respondableElements.length && respondableElements.length === ((itemTypesCount[ElementType.MCQ] ?? 0) + (itemTypesCount[ElementType.CUSTOM_MCQ] ?? 0) + (itemTypesCount[ElementType.SELECT_TABLE] ?? 0))){ // check if it's mcq or selection table only
          for(let lang of supportedLangs){
            if(lang === this.lang.c()){
              continue;
            }
            if(supportedLangsResponseMap.get(lang) && supportedLangsResponseMap.get(lang).has(question.id)){
              const expect_ans_lang = supportedLangsResponseMap.get(lang).get(question.id);
              const lang_weight = expect_ans_lang.weight;
              const lang_responses = expect_ans_lang.correct_responses;
              if(lang_responses.length !== response_length || weight !== lang_weight){
                failedMap[CheckId.Q_EXP_ANS_LANG_MISMATCH] = true;
                break;
              } else if (lang_responses.some(lr => !correct_responses_set.has(lr))){
                failedMap[CheckId.Q_EXP_ANS_LANG_MISMATCH] = true;
                break;
              }
            } else if (response_length){
              failedMap[CheckId.Q_EXP_ANS_LANG_MISMATCH] = true;
              break;
            }
          }
        }
        if(respondableElements.length === 1 && (itemTypesCount[ElementType.MCQ] === 1 || itemTypesCount[ElementType.CUSTOM_MCQ] === 1)){ // if only one respondable and it's an mcq
          if(!respondableElements[0].isMultiSelect && !correct_responses?.length){
            failedMap[CheckId.Q_EXP_ANS_MISSING] = true;
          }
        }
        if((respondableElements.length ?? 0) != ((itemTypesCount[ElementType.MCQ] ?? 0) + (itemTypesCount[ElementType.CUSTOM_MCQ] ?? 0))){ // if has respondables but doesn't have mcq
          failedMap[CheckId.Q_EXP_ANS_NOT_MCQ] = true;
        }
        if(respondableElements.length === (itemTypesCount[ElementType.MCQ]??itemTypesCount[ElementType.CUSTOM_MCQ])){
          isMCQ = true;
        } 
        if(respondableElements.length === 0) {
          failedMap[CheckId.Q_EXP_ANS_NO_ENTRY] = true;
        }
        if(respondableElements.length > 1){
          failedMap[CheckId.Q_EXP_ANS_MULTI_ENTRY] = true;
        }
        if(isMCQ){
          const multiSelectMap: {[key:number]: Set<string>} = {};
          const answerPositionMap:{[key:number]: Set<string>} = {}; // this is a map to tie the correct options to the available elements
          for(let [i, element] of respondableElements.entries()){
            multiSelectMap[i] = element.isMultiSelect;
            answerPositionMap[i] = new Set();
            let missingSpecifiedResponse = true;
            element.options?.forEach((option, idx) => {
              if(option.isCorrect){
                answerPositionMap[i].add(mcqOptionLabels[idx]);
                missingSpecifiedResponse = false;
              }
            })
            if(missingSpecifiedResponse){
              failedMap[CheckId.Q_EXP_ANS_NO_ACC] = true;
            }
          }
          for(let ans of correct_responses){
            const indivudalResponse = ans.split(';'); // break up response to individual inputs (i.e if multiple elements answer to each element)
            let responseMismatched = false;
            if(indivudalResponse.length !== respondableElements.length){
              failedMap[CheckId.Q_EXP_ANS_MISMATCH] = true; 
              responseMismatched = true;
            } else {
              for(let [idx, optionSelected] of indivudalResponse.entries()){
                if(multiSelectMap[idx]){
                  const multiOptionsSelected = optionSelected.split(','); // getting all the options selected if multiselect
                  if(multiOptionsSelected.some(option => !answerPositionMap[idx]?.has(option))){ // if one of the selected in multi select isn't correct
                    failedMap[CheckId.Q_EXP_ANS_MISMATCH] = true; 
                    responseMismatched = true;
                    break;      
                  }
                } else if(!answerPositionMap[idx]?.has(optionSelected)){
                  failedMap[CheckId.Q_EXP_ANS_MISMATCH] = true; 
                  responseMismatched = true;
                  break;                  
                }
              }
            }
            if(responseMismatched){
              auditResultsMap.get(CheckId.Q_EXP_ANS_MISMATCH)?.autoFixContext?.push({item_id: question.id, formatted_response: ans, lang: this.lang.c()})
            }
          }
        }
        Object.keys(failedMap).forEach( key =>
          auditResultsMap.get(key)?.items?.push(question)
        );
      }

      return {}
    }

    
    this.customIterators['CLONES'] = async (auditResultsModel:AuditResultCore) => {
      
      const {auditSlug, questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;
      
      const trackingParams = this.frameworkCtrl.identifySpecialParams(PARAM_SPECIAL_FLAGS.CLONE_LINKER);
      if (trackingParams.length === 0) {
        alert('This audit can only run if there is exactly one parameter that is indicated as the Clone Linker');
        // return this.auditsRan['CLONES'] = false;
        throw new Error();
      }
      if (trackingParams.length > 1) {
        alert('This audit is not compatible with frameworks which contain multiple Clone Linker parameters');
        // return this.auditsRan['CLONES'] = false;
        throw new Error();
      }
      const cloneIndicParam = trackingParams[0];
      const questionsNoCloneIndic = [];
      const questionsInvalidCloneIndic = [];
      const clonedQuestionsRef = new Map();
      const seenQuestionsRef = new Map();
      const getCloneLabelCore = (label: string) => label.split('/')[0];
      // this.itemBankCtrl.getItems()
      questions.forEach(question => {
        const labelCore = getCloneLabelCore(question.label);
        if (seenQuestionsRef.get(labelCore)) {
          clonedQuestionsRef.set(labelCore, true);
        }
        seenQuestionsRef.set(labelCore, true);
      });
      this.itemBankCtrl.getItems().forEach(question => {
        const labelCore = getCloneLabelCore(question.label);
        if (clonedQuestionsRef.get(labelCore)) {
          if (!question.meta[cloneIndicParam.code]) {
            questionsNoCloneIndic.push(question);
          } 
          else if (question.meta[cloneIndicParam.code] !== labelCore) {
            questionsInvalidCloneIndic.push(question);
          }
        }
      });
      let totalIssues = 0;
      let issueCategories = [];
      this.trackQuestionIssues('Q_CLONES_MISSING_PARAM', true,  questionsNoCloneIndic, `belong to a clone group but do not have the indicated parameter`, issueCategories, totalIssues);
      this.trackQuestionIssues('Q_CLONES_INVALID_PARAM', true,  questionsInvalidCloneIndic, `belong to a clone group but may not be properly identified/categorized`, issueCategories, totalIssues);
      this.auditQuestionMem['CLONES_CATEGORIES'] = issueCategories;
      this.auditQuestionMem['N_CLONES_ISSUES'] = totalIssues;
      const results = issueCategories.map(category => {
        const items = this.auditQuestionMem[category.key]
        return {
          ...category,
          items
        }
      });
      this.markAuditAsRan(auditSlug, false, true, results);
    
      return {isCustomResults: true}
    }

    
    this.customIterators['ENTRY_ID'] = async (auditResultsModel:AuditResultCore) => {

      const {questions, auditSlug, checkIds, auditResults, auditResultsMap} = auditResultsModel;
      enum CheckId {
        UNTRACKED_ENTRIES = 'UNTRACKED_ENTRIES'
      }
      const questionsWithUntrackedEntries = [];
      questions.forEach(question => {
        const content = this.getQuestionContent(question)
        const tracker = collectAndEnsureEntryIds(content);
        if (tracker.nonIndicatedEntries.length > 0) {
          auditResultsMap.get(CheckId.UNTRACKED_ENTRIES).items.push(question);
        }
      });
      return {}
    }

    
    this.customIterators['VOICEOVER'] = async (auditResultsModel:AuditResultCore) => {

      const {auditSlug, checkIds, auditResultsMap} = auditResultsModel;
      const questions = await getScreens({
        lang: this.lang.c(),
        itemBankCtrl: this.itemBankCtrl, // todo: highly stateful
        frameworkCtrl: this.frameworkCtrl
      }, null, false);
      let {auditResults} = auditResultsModel
      enum CheckId { 
        MISSING_OVERALL_VOICE = 'MISSING_OVERALL_VOICE', 
        MISSING_OPTION_VOICE = 'MISSING_OPTION_VOICE', 
        CONTAIN_DUPLICATED_OPTION_VOICE = 'CONTAIN_DUPLICATED_OPTION_VOICE' 
      };

      const langs = this.itemBankCtrl.getSupportedLangs();
      
      const renderAuditId = (checkId:string, langId:string) => CheckId[checkId]+'/'+langId;
      const getAuditResultList = (checkId:CheckId, langId:string) => {
        const id = renderAuditId(checkId, langId);
        return auditResultsMap.get(id);
      }
      const addAuditResult = (checkId:CheckId, langId:string, question:IQuestionConfig) => {
        getAuditResultList(checkId, langId).list.push(question);
      }
      langs.forEach(langId => {
        checkIds.forEach(checkId => {
          const id = renderAuditId(checkId, langId);
          const list:IAuditResult = {
            id, 
            caption: `${CheckId[checkId].split('_').join(' ').toLowerCase()} (${langId})`,
            list:[]
          };
          auditResults.push(list);
          auditResults = auditResults.filter(result => result.id !== checkId);
          auditResultsMap.set(id, list);
          auditResultsMap.delete(checkId);
        });
      });
      this.voiceoverAuditResults = auditResultsMap;
      
      questions.forEach(question => {
        langs.forEach(langId => {
          // console.log(question.label, 'audit in ', langId)
          let questionTarget = question;
          if (langId === LangId.FR){
            questionTarget = question.langLink;
          }
          if (!questionTarget.voiceover || !questionTarget.voiceover.url){
            addAuditResult(CheckId.MISSING_OVERALL_VOICE, langId, question);
          }
          const entries = identifyQuestionResponseEntries(questionTarget.content, []);
          const voiceUploadMap:Map<string, boolean> = new Map();
          let containsDuplicateVoiceover = false;
          let containsMissingVoiceover = false;
          entries.forEach(entry => {
            if (entry.elementType === ElementType.MCQ){
              const mcqEntry:IContentElementMcq = <any> entry;
              if (mcqEntry.options){
                mcqEntry.options.forEach((option, option_index) => {
                  if (option.voiceover && option.voiceover.url){
                    const url = option.voiceover.url;
                    if (voiceUploadMap.has(url)){
                      containsDuplicateVoiceover = true;
                      console.warn(question.label, 'MC Entry has duplicate option voice', option_index, `(${langId})`)
                    }
                    voiceUploadMap.set(url, true)
                  }
                  else{
                    containsMissingVoiceover = true;
                  }
                })
              }
              else{
                console.warn(question.label, 'MC Entry has no options', entry)
              }
            }
          });
          if (containsDuplicateVoiceover){
            addAuditResult(CheckId.CONTAIN_DUPLICATED_OPTION_VOICE, langId, question);
          }
          if (containsMissingVoiceover){
            addAuditResult(CheckId.MISSING_OPTION_VOICE, langId, question);
          }
        });
      });
      let totalIssues = 0;
      let issueCategories = [];
      auditResults.forEach(auditResult => {
        this.trackQuestionIssues(auditResult.id, true, auditResult.list, auditResult.caption, issueCategories, totalIssues);
      })
      this.auditQuestionMem[auditSlug+'_CATEGORIES'] = issueCategories;
      const results = issueCategories.map(category => {
        const items = this.auditQuestionMem[category.key]
        return {
          ...category,
          items
        }
      });
      const isTestletsOnly = false;
      this.markAuditAsRan(auditSlug, isTestletsOnly, true, results);
    
      return {isCustomResults: true}
    }

    
    this.customIterators['IP_RIGHTS'] = async (auditResultsModel:AuditResultCore) => {

      const {questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        NOT_COMPLETED = 'NOT_COMPLETED', 
        NOT_INCLUDED_EN = 'NOT_INCLUDED_EN',
        NOT_INCLUDED_FR = 'NOT_INCLUDED_FR',
        ALLOWED_IMPRESSIONS_NUM = 'ALLOWED_IMPRESSIONS_NUM' 
      };

      let assetIdSet = new Set<number>();
      let thisAssessmentAssetSet = new Set<number>();
      const elementsWithoutAssetIdMapEn = new Map<IQuestionConfig, IContentElement[]>();
      const elementsWithoutAssetIdMapFr = new Map<IQuestionConfig, IContentElement[]>();
      let elementsWithoutAssetIdEn: IQuestionConfig[] = [];
      let elementsWithoutAssetIdFr: IQuestionConfig[] = [];
      const getAssetIds = (
          elements: IContentElement[], 
          root: IQuestionConfig, 
          assetSet: Set<number>, 
          noAssetIdMap: Map<IQuestionConfig, IContentElement[]>
      ) => {
        if (root.assetId) {
          assetSet.add(root.assetId);
        }
        if (elements) {
          for(const element of elements) {
            if(element.assetId) {
              assetSet.add(element.assetId);
            } else if (element.url && ['image','video','audio'].indexOf(element.elementType) !== -1) {
              let list = noAssetIdMap.get(root);
              if (!list) { list = []; noAssetIdMap.set(root, list)}
              //console.log(`no assetId for elementType "${element.elementType}" with URL: ${element.url} in question ${root.id}`);
              list.push(element);
            }
            getAssetIds(getElementChildren(element), root, assetSet, noAssetIdMap);
          }
        }
      }
      // limit to the assessment scope
      const questionStructs = questions.map(q => {return {
        root: q, 
        contentEn: this.getQuestionContent(q, 'en'), 
        contentFr: this.getQuestionContent(q, 'fr')
      }});
      for(const entry of questionStructs) {
        getAssetIds(entry.contentEn, entry.root, assetIdSet, elementsWithoutAssetIdMapEn);
        getAssetIds(entry.contentFr, entry.root, assetIdSet, elementsWithoutAssetIdMapFr);
      }
      elementsWithoutAssetIdEn = Array.from(elementsWithoutAssetIdMapEn.keys());
      elementsWithoutAssetIdFr = Array.from(elementsWithoutAssetIdMapFr.keys());
      auditResultsMap.get(CheckId.NOT_INCLUDED_EN).items = elementsWithoutAssetIdEn;
      auditResultsMap.get(CheckId.NOT_INCLUDED_FR).items = elementsWithoutAssetIdFr;
      const data = await this.auth.apiFind(this.routes.TEST_AUTH_ASSET, {})
      const assets: ILibraryAsset[] = data.data.filter(d => Array.from(assetIdSet).includes(d.asset_id));
      // CheckId.NOT_COMPLETED
      assets.forEach(asset => {
        if (asset.status !== EStatus.COMPLETED) {
          auditResultsMap.get(CheckId.NOT_COMPLETED).items.push(asset.asset_id)
        }
        if (asset.num_print_impressions && parseInt(asset.num_print_impressions) < asset.totalTestTakers && thisAssessmentAssetSet.has(asset.asset_id)) {
          auditResultsMap.get(CheckId.ALLOWED_IMPRESSIONS_NUM).items.push(asset.asset_id)
        }
      });

      return {}
    }

    
    this.customIterators['ACCESSIBILITY'] = async (auditResultsModel:AuditResultCore) => {

      const {questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        IMAGES_MISSING_ALT_TEXT = 'IMAGES_MISSING_ALT_TEXT', 
        VIDEOS_MISSING_SUBTITLES = 'VIDEOS_MISSING_SUBTITLES', 
        AUDIO_FILES_MISSING_TRANSCRIPTS = 'AUDIO_FILES_MISSING_TRANSCRIPTS',
        ITEMS_USING_KEYBOARD_INACCESSIBLE_BLOCKS = 'ITEMS_USING_KEYBOARD_INACCESSIBLE_BLOCKS',
      };
      
      let inAccessibleQuestionSet = new Set<IQuestionConfig>();
      const isElementAccessible = (element: IContentElement): boolean => {
        let isAccessible = false;
        if (element.elementType) {
          const typeDef = ElementTypeDefs[element.elementType.toUpperCase()];
          if (typeDef) {
              isAccessible = (typeof typeDef.isKeyboardAccessible !== 'undefined') ? typeDef.isKeyboardAccessible : true;  
          } else {
            const accessibleExceptions: ElementType[] = [ElementType.DYNAMIC_IMAGE];
            if (accessibleExceptions.indexOf(element.elementType as ElementType) !== -1) {
              isAccessible = true;
            }
          }
        }
        return isAccessible;
      }
      const checkInaccessibleElements = (
          question: IQuestionConfig, 
          elements: IContentElement[], 
          questionSet: Set<IQuestionConfig>) => {
        if (elements) {
          for(const element of elements) {
            if (!isElementAccessible(element)) {
              questionSet.add(question)
            }
            checkInaccessibleElements(question, getElementChildren(element), questionSet);
          }
        }
      }

      for (const qConfig of questions){
        const qContent = this.getQuestionContent(qConfig);
        let isMissingAltText = false;
        let isMissingVideoSubTitles = false;
        let isMissingTranscript = false
        qContent.forEach(content => {
          switch(content.elementType){
            case ElementType.IMAGE:
              const imageContent =  (<IContentElementImage>content);
              if(!imageContent.altText) isMissingAltText = true;
              if(imageContent.hiContrastImg && !imageContent.hiContrastImg.altText) isMissingAltText = true;
              break;
            case ElementType.VIDEO:
              const videoContent = (<IContentElementVideo>content);
              if(!videoContent.subtitlesUrl) isMissingVideoSubTitles = true;
              break;
            case ElementType.AUDIO:
              const audioContent = (<IContentElementAudio>content);
              if(!audioContent.transcriptUrl) isMissingTranscript = true;
              break
          }
        });
        checkInaccessibleElements(qConfig, qContent, inAccessibleQuestionSet);
        if(isMissingAltText) auditResultsMap.get(CheckId.IMAGES_MISSING_ALT_TEXT).items.push(qConfig);
        if(isMissingVideoSubTitles) auditResultsMap.get(CheckId.VIDEOS_MISSING_SUBTITLES).items.push(qConfig);
        if(isMissingTranscript) auditResultsMap.get(CheckId.AUDIO_FILES_MISSING_TRANSCRIPTS).items.push(qConfig);
      }
      auditResultsMap.get(CheckId.ITEMS_USING_KEYBOARD_INACCESSIBLE_BLOCKS).items = Array.from(inAccessibleQuestionSet);
        
      return {}
    }

    
    this.customIterators['PRINT_VERSION'] = async (auditResultsModel:AuditResultCore) => {

      const {questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        PRINT_FRIENDLY = 'PRINT_FRIENDLY',
      };

      let questionSet = new Set<IQuestionConfig>();
      const isElementPrintFriendly = (element: IContentElement): boolean => {
        let isPrintFriendly = true;
        if (element.elementType) {
          const typeDef = ElementTypeDefs[element.elementType.toUpperCase()];
          if (typeDef) {
            if (typeof typeDef.isPrintFriendly !== 'undefined') {
              isPrintFriendly = typeDef.isPrintFriendly;  
            } else { // special cases
                if (
                  (element.elementType === ElementType.INPUT && element.format === InputFormat.TEXT) ||
                  (element.elementType === ElementType.MCQ && element.displayStyle === McqDisplay.DROPDOWN) ||
                  (element.elementType === ElementType.TEXT && element.paragraphStyle === TextParagraphStyle.ANNOTATION)  
                ) {
                  isPrintFriendly = false;
                }
            }
          }
        }
        return isPrintFriendly;
      }
      const checkPrintFriendlyElements = (question: IQuestionConfig, elements: IContentElement[]) => {
        if (elements) {
          for(const element of elements) {
            if (!isElementPrintFriendly(element)) {
              questionSet.add(question)
            }
            checkPrintFriendlyElements(question, getElementChildren(element));
          }
        }
      }
      questions.forEach(question => {
        checkPrintFriendlyElements(question, this.getQuestionContent(question));
      })
      auditResultsMap.get(CheckId.PRINT_FRIENDLY).items = Array.from(questionSet);

      return {}
    }

    
    // this.customIterators['MOBILE'] 

    
    // this.customIterators['BROWSERLOCK']

    
    this.customIterators['COMMENTS'] = async (auditResultsModel:AuditResultCore) => {

      const {questions, auditResultsMap} = auditResultsModel;

      enum CheckId {
        QS_W_OUTSTANDING_CMTS = 'QS_W_OUTSTANDING_CMTS',
        ASSETS_W_OUTSTANDING_CMTS = 'ASSETS_W_OUTSTANDING_CMTS',
      };
      
      const getAssetIds = (elements: IContentElement[], assetIdSet: Set<number>) => {
        if(!elements) {
          return;
        }
        for(const element of elements) {
          if(element.assetId) {
            assetIdSet.add(element.assetId);
          }
          getAssetIds(getElementChildren(element), assetIdSet);
        }
      }
      let assetIdSet = new Set<number>();
      const allContent = questions.map(q => {
        if (q.assetId) {
          assetIdSet.add(q.assetId)
        }
        return q.content
      });
      for(const content of allContent ) {
        getAssetIds(content, assetIdSet);
      }
      const assetIdArr = Array.from(assetIdSet.values());
      const res = await this.auth.apiGet(this.routes.TEST_AUTH_NOTES_AUDIT, this.itemBankCtrl.customTaskSetId, {query: {
        assetIds: assetIdArr
      }})
      const qIds = (!res || !res.unresolvedQIds) ? [] : res.unresolvedQIds.map( r => r.id);
      const aIds = (!res || !res.unresolvedAssetIds) ? [] : res.unresolvedAssetIds.map( a => a.id );  
      const assessmentQs = questions.filter( q => qIds.includes(q.id)) //this.itemBankCtrl.questions
      this.auditQuestionMem['NUM_QS_W_OUTSTANDING_CMTS'] = assessmentQs.length;
      this.auditQuestionMem['QS_W_OUTSTANDING_CMTS'] =  assessmentQs
      this.auditQuestionMem['NUM_ASSETS_W_OUTSTANDING_CMTS'] = aIds.length;
      this.auditQuestionMem['ASSETS_W_OUTSTANDING_CMTS'] = aIds;
      auditResultsMap.get(CheckId.ASSETS_W_OUTSTANDING_CMTS).items.push(...aIds)
      auditResultsMap.get(CheckId.QS_W_OUTSTANDING_CMTS).items.push(...assessmentQs)

      return {}
    }

    
    // this.customIterators['ASSESSMENT_KEYS']

    
    this.customIterators['COMMON_ITEMS'] = async (auditResultsModel:AuditResultCore) => {

      const {auditSlug, questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        COMMON_ITEMS = 'COMMON_ITEMS',
      }

      // phasing this out, but want to have a warning in case items are being skipped
      const questionsOld = this.itemBankCtrl.frameworkCtrl.getLinearFormQuestions()
      const questionIds = new Set(questions.map(q => q.id));
      const questionsMissed = questionsOld.filter(q => !questionIds.has(q.id));
      if (questionsMissed.length){
        console.warn('COMMON_ITEMS audit might be skipping some items. This may require developer investigation. Item discrepancy:', questionsMissed)
      }
      
      const groupid = this.itemBankCtrl.saveLoadCtrl.getGroupID().toString()
      //console.log(thisAssessment)
      if (!groupid) return;
      const res = await this.auth.apiGet(this.routes.TEST_AUTH_FRAMEWORKS_AUDIT, parseInt(groupid))
      const seenItemIds = new Map()
      const alreadyDuped = new Map()
      for (let i = 0;res[i];i++) {
        const frmwrk = JSON.parse(res[i]["framework"])
        if (!frmwrk) {
          continue;
        }
        const frameworkCopy = <ItemSetFrameworkCtrl>{...this.frameworkCtrl, asmtFmrk: frmwrk}
        const ctx = {itemBankCtrl:this.itemBankCtrl, lang: this.lang.c(), frameworkCtrl:frameworkCopy}
        const questions = await getScreens(ctx)
        for (let q of questions) {
          const id = q.id
          if (id && seenItemIds.get(id) && !alreadyDuped.get(id)) {
            alreadyDuped.set(id, true)
          }
          seenItemIds.set(id, true)
        }
      }
      const totalQuestions = []
      for (let i = 0;i<questions.length;i++) {
        const item = questions[i]
        if (alreadyDuped.has(item.id)) {
          totalQuestions.push(item)
        }
      }
      this.auditQuestionMem[auditSlug] = totalQuestions
      auditResultsMap.get(CheckId.COMMON_ITEMS).items.push(...totalQuestions)

      return {}
    }

    
    this.customIterators['TRAX_PARAM'] = async (auditResultsModel:AuditResultCore) => {
      
      const {questions, checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        ASMT_SESS_NUM = 'ASMT_SESS_NUM' ,
        ASMT_CODE = 'ASMT_CODE' ,
        ASMT_MARK = 'ASMT_MARK' ,
        ASMT_SCALE = 'ASMT_SCALE' ,
        ASMT_SESS_CHAR = 'ASMT_SESS_CHAR' 
      }
  
      questions.forEach((item:IQuestionConfig)=>{
        let asmt_session_numbers = false
        let assmt_code = false
        let mark_value = false
        let scale_factor = false
        let asmt_session_char = false;
        function isDigit(c) {
          return c>='0' && c<='9'
        }
        function isChar(c) {
          return (c>='a' && c<='z') || (c>='A' && c<='Z')
        }
        if (item.meta) {
          const assmt_sess = (item.meta["ASSMT_SESSION"])
          const code = (item.meta["ASSMT_CODE"])
          const mark = (item.meta["MARK_VALUE"])
          const scale = (item.meta["SCALE_FACTOR"])
          const section = (item.meta["ASSMT_SECTION"])
          if (assmt_sess) {
            if (assmt_sess.length==6) {
              asmt_session_numbers = true
              for (let i = 0;i<6;i++) {
                if (!isDigit(assmt_sess.charAt(i))) {
                  asmt_session_numbers = false
                }
              }
            }
          }
          if (code) {
            if (code.length==5) {
              assmt_code = true
              for (let i = 0;i<3;i++) {
                const c = code.charAt(i)
                if (!isChar(c)) {
                  assmt_code = false
                }
              }
              for (let i = 3;i<5;i++) {
                const c = code.charAt(i)
                if (!isDigit(c)) {
                  assmt_code = false
                }
              }
            }
          } 
          if (mark) {
            if (mark.length>0 && mark.length<3) {
              mark_value = true;
              for (let i = 0;i<mark.length;i++) {
                if (!isDigit(mark.charAt(i))) {
                  mark_value = false
                }
              }
            }
          }
          if (scale) {
            if (scale.length>0 && scale.length<9) {
              scale_factor = true;
              for (let i = 0;i<scale.length;i++) {
                if (!isDigit(scale.charAt(i))) {
                  scale_factor = false
                }
              }
            }
          }
          if (section && section.length>0) {
            const c = section.charAt(0)
            if (c=='A' || c=='B') {
              asmt_session_char = true
            }
          }
        }
        if (!asmt_session_numbers) auditResultsMap.get(CheckId.ASMT_SESS_NUM).items.push(item)
        if (!assmt_code) auditResultsMap.get(CheckId.ASMT_CODE).items.push(item)
        if (!mark_value) auditResultsMap.get(CheckId.ASMT_MARK).items.push(item)
        if (!scale_factor) auditResultsMap.get(CheckId.ASMT_SCALE).items.push(item)
        if (!asmt_session_char) auditResultsMap.get(CheckId.ASMT_SESS_CHAR).items.push(item)
      })

      return {}
    }

    
    this.customIterators['MSCATITEMDUP_PARAM'] = async (auditResultsModel:AuditResultCore) => {
      
      const {auditSlug, checkIds, auditResults, auditResultsMap} = auditResultsModel;
      if(this.frameworkCtrl.asmtFmrk.testFormType !== TestFormConstructionMethod.MSCAT){
        alert("This audit can only be ran on MSCAT type forms");
        throw new Error(AuditErrors.INVALID_ASSESMENT_TYPE);
      }
      const panels = this.frameworkCtrl.asmtFmrk.panels;
      const panelAssembly = this.frameworkCtrl.asmtFmrk.panelAssembly;
      // create list of possibly-preceding modules
      const modulesByStage = new Map();
      const stageByModule = new Map();
      const violations = [];
      for (let module of panelAssembly.allModules){
        const {stageNumber} = module;
        if (!modulesByStage.get(+stageNumber)){
          modulesByStage.set(+stageNumber, []);
        }
        modulesByStage.get(+stageNumber).push(module.id);
        stageByModule.set(+module.id, stageNumber)
      }
      
      for (let panel of panels){
        const panelItemsByModule = new Map();
        for (let module of panel.modules){
          const {moduleId, __cached_itemIds} = module;
          panelItemsByModule.set(+moduleId, __cached_itemIds)
        }
        // check for duplicates
        for (let module of panel.modules){
          const {moduleId, __cached_itemIds} = module;
          const itemIds = __cached_itemIds;
          const forbiddenPrevItemId = new Map();
          itemIds?.map(itemId => forbiddenPrevItemId.set(+itemId, true));
          const stageNumber = stageByModule.get(+moduleId);
          for (let prevStageNum = 1; prevStageNum < stageNumber; prevStageNum ++){
            const prevModuleIds = modulesByStage.get(+prevStageNum);
            for (let prevModuleId of prevModuleIds){
              const prevPanelItemIds = panelItemsByModule.get(+prevModuleId);
              console.log('compare', moduleId, 'prev', prevModuleId, prevPanelItemIds)
              prevPanelItemIds?.forEach(prevItemId => {
                if (forbiddenPrevItemId.get(+prevItemId)){
                  violations.push({
                    panelId: panel.id,
                    prevItemId,
                    prevModuleId,
                    prevStageNum,
                    stageNumber,
                    moduleId
                  })
                }
              })
            }
          }
        }
      }
      const results: IAuditFormattedResult[] = [{
        key: auditSlug,
        description: 'Duplicate items in panels(MSCAT)',
        isAutoFixable: false,
        items: violations,
        num_issues: violations?.length
      }]

      if (violations.length){
        console.log('violations', violations)
        alert(`Found ${violations.length} invalid cases of re-use (see console log)`)
      }
      else {
        alert('No invalid duplications found.')
      }

      this.markAuditAsRan(auditSlug, false, true, results)

      return {isCustomResults: true}
    }

    
    this.customIterators['CONTENT_DIFF_AUDIT'] = async (auditResultsModel:AuditResultCore) => {

      const {checkIds, auditResults, auditResultsMap} = auditResultsModel;

      enum CheckId {
        CURRENT_CONTENT_DIFF_AUDIT = 'CURRENT_CONTENT_DIFF_AUDIT',
      }
  
      const test_design_id = parseInt(prompt("Enter the test design id :"));
      if(!test_design_id){
        alert('Please Enter valid test design id');
        throw new Error();
      }
      try {
        const data: any[] = await this.auth.apiFind(this.routes.TEST_DESIGN_QUESTION_VERSIONS, {query: {test_design_ids: [test_design_id]}})
        const parsedData = itemContentDiff(data, true, {itemBankCtrl: this.itemBankCtrl});
        const prefix = 'CONTENT_DIFF_AUDIT';
        this.auditQuestionMem[prefix] = parsedData;
        auditResultsMap.get(CheckId.CURRENT_CONTENT_DIFF_AUDIT).items.push(...parsedData)
        this.auditResultHeader = `${prefix} - Test design id: ${test_design_id} `;
  
      } catch (error) {
        console.error(error);
        throw error;
      }  
      return {} 
    }

    
    this.customIterators['PUBLISHED_CONTENT_DIFF_AUDIT'] = async (auditResultsModel:AuditResultCore) => {

      const {checkIds, auditResults, auditResultsMap} = auditResultsModel;

      const test_design_ids = prompt("Enter comma seperated test design ids :");
      const ids = test_design_ids.toString().split(',').map(id => id.trim()).filter(id => id != null);
      // console.log(ids)
      if(!ids.length || ids.length < 2){
        alert('Please Enter valid test design id');
        throw new Error();
      }
      try {
        const data: any[] = await this.auth.apiFind(this.routes.TEST_DESIGN_QUESTION_VERSIONS, {query: {test_design_ids: ids}})
        const parsedData = legacyItemContentDiff(data, false, {itemBankCtrl: this.itemBankCtrl});
        const prefix = 'PUBLISHED_CONTENT_DIFF_AUDIT';
        this.auditQuestionMem[prefix] = parsedData;
        this.auditResultHeader = `${prefix} - Test design ids: ${ids} `;
        auditResultsMap.get(prefix).items.push(...parsedData)
      } catch (error) {
        console.error(error);
        throw error;
      }

      return {}
    }

    
    this.customIterators['HUMAN_SCORED_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      const {questions, auditResultsMap} = auditResultsModel;
      enum checkID {
        MISSING_HUMAN_SCORED = 'MISSING_HUMAN_SCORED',
        MISSING_SCALES = 'MISSING_SCALES',
        SCALE_MISSING_PROFILE_ID = 'SCALE_MISSING_PROFILE_ID',
        MISSING_INPUT_BLOCK = 'MISSING_INPUT_BLOCK',
        MISCONFIGURED_SCORING_SCALE = 'MISCONFIGURED_SCORING_SCALE',
      }
      for(const item of questions){
        const scoringConfig = this.itemBankCtrl.getQuestionScoringInfo(+item.id);
        const qContent =  getQuestionContent(item);
        let containsTextBox = false; // todo: this is a sub-optimal approach, assumes text is not scored directly (sometimes it is)
        let isHumanScoredMissing = false;
        let missingScales = false;
        let missingScoreProfileID = false;
        let misconfiguredScale = false;
        qContent.forEach(content => {
          if(content.elementType == ElementType.INPUT && content.format == InputFormat.TEXT){
            containsTextBox = true;
          }
        });
        if(!scoringConfig.is_human_scored){
          isHumanScoredMissing = true;
        }
        if(scoringConfig.is_human_scored){
          let scales = JSON.parse(''+scoringConfig.scales);
          if(typeof scales !== 'string' && !scales?.length){
            missingScales = true;
          } else if(typeof scales === 'string'){
            misconfiguredScale = true;
          } else {
            for(let scale of scales){
              if(!scale.score_profile_id){
                missingScoreProfileID = true;
              }
            }
          }
        }
        if(isHumanScoredMissing){
          auditResultsMap.get(checkID.MISSING_HUMAN_SCORED).items.push(item);
        }
        if(missingScales){
          auditResultsMap.get(checkID.MISSING_SCALES).items.push(item);
        }
        if(missingScoreProfileID && !missingScales){
          auditResultsMap.get(checkID.SCALE_MISSING_PROFILE_ID).items.push(item);
        }
        if(!containsTextBox){
          auditResultsMap.get(checkID.MISSING_INPUT_BLOCK).items.push(item);
        }
        if(misconfiguredScale){
          auditResultsMap.get(checkID.MISCONFIGURED_SCORING_SCALE).items.push(item);
        }
      }
      return {}
    }

    
    this.customIterators['SCORE_POINT_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      
      const {questions, auditResultsMap} = auditResultsModel;

      enum CheckId {
        ITEMS_WITH_SCORE_POINT_COUNT = 'ITEMS_WITH_SCORE_POINT_COUNT',
        MISSING_SCORE_POINT = 'MISSING_SCORE_POINT',
        INVALID_SCORE_POINT_ALIGNMENT = 'INVALID_SCORE_POINT_ALIGNMENT',
        INVALID_SCORE_POINT_EXCEEDING_TOTAL_WEIGHT = 'INVALID_SCORE_POINT_EXCEEDING_TOTAL_WEIGHT',
        INVALID_SCORE_POINT_BELOW_TOTAL_WEIGHT = 'INVALID_SCORE_POINT_BELOW_TOTAL_WEIGHT',
        ITEMS_SCORE_POINT_EXCEEDS_MAX_SCORE_EA_SUBMISSIONS = 'ITEMS_SCORE_POINT_EXCEEDS_MAX_SCORE_EA_SUBMISSIONS',  // simulated submission
        ITEMS_SCORE_POINT_BELOW_MIN_SCORE_EA_SUBMISSIONS = 'ITEMS_SCORE_POINT_BELOW_MIN_SCORE_EA_SUBMISSIONS',  // simulated submission
        ITEMS_SCORE_POINT_EXCEEDS_MIN_WEIGHT_EA_SUBMISSIONS = 'ITEMS_SCORE_POINT_EXCEEDS_MIN_WEIGHT_EA_SUBMISSIONS',  // simulated submission
        ITEMS_SCORE_POINT_BELOW_MAX_WEIGHT_EA_SUBMISSIONS = 'ITEMS_SCORE_POINT_BELOW_MAX_WEIGHT_EA_SUBMISSIONS',  // simulated submission
        SCORE_POINT_BELOW_MAXIMUM_CAPTURED_SCORE_SS = 'SCORE_POINT_BELOW_MAXIMUM_CAPTURED_SCORE_SS'      //simulated submission
      };

      let countItemsWithScorePoint = 0;
      for(const qConfig of questions){
        const qContent = this.getQuestionContent(qConfig);
        const totalWeight = getQuestionPoints(qContent);
          // check for possible answers through simulated submissions
          const expectedAnswers = await this.itemEditCtrl.findAllExpectedAnswer(+qConfig.id)
          let possibleScores = [];
          let possibleWeights = [];
          expectedAnswers?.forEach((ea: ExpectedAnswer) => { 
          possibleScores.push(+(ea.score));
          possibleWeights.push(+(ea.weight));
        });
        let maxPossibleScore = Math.max(...possibleScores);
        let minPossibleScore = Math.min(...possibleScores);
        let maxPossibleWeight = Math.max(...possibleWeights);
        let minPossibleWeight = Math.min(...possibleWeights);
        // console.log(qConfig.id, qConfig.label, qConfig.meta['SP'])
        if(qConfig.meta && qConfig.meta.hasOwnProperty('SP')){
          let val = <string>qConfig.meta['SP']
          if (val && val.trim() != null) {
            auditResultsMap.get(CheckId.ITEMS_WITH_SCORE_POINT_COUNT).items.push(qConfig);
            if(+val > totalWeight){
              auditResultsMap.get(CheckId.INVALID_SCORE_POINT_EXCEEDING_TOTAL_WEIGHT).items.push(qConfig);
              auditResultsMap.get(CheckId.INVALID_SCORE_POINT_ALIGNMENT).items.push(qConfig);
            } else if( +val < totalWeight) {
              auditResultsMap.get(CheckId.INVALID_SCORE_POINT_BELOW_TOTAL_WEIGHT).items.push(qConfig);
              auditResultsMap.get(CheckId.INVALID_SCORE_POINT_ALIGNMENT).items.push(qConfig);
            }
            // check with simulated sub
            if(+val > maxPossibleScore) auditResultsMap.get(CheckId.ITEMS_SCORE_POINT_EXCEEDS_MAX_SCORE_EA_SUBMISSIONS).items.push(qConfig);
            if(+val < maxPossibleScore) auditResultsMap.get(CheckId.SCORE_POINT_BELOW_MAXIMUM_CAPTURED_SCORE_SS).items.push(qConfig);
            if(+val < minPossibleScore) auditResultsMap.get(CheckId.ITEMS_SCORE_POINT_BELOW_MIN_SCORE_EA_SUBMISSIONS).items.push(qConfig);
            if(+val > minPossibleWeight) auditResultsMap.get(CheckId.ITEMS_SCORE_POINT_EXCEEDS_MIN_WEIGHT_EA_SUBMISSIONS).items.push(qConfig);
            if(+val < maxPossibleWeight) auditResultsMap.get(CheckId.ITEMS_SCORE_POINT_BELOW_MAX_WEIGHT_EA_SUBMISSIONS).items.push(qConfig);
            countItemsWithScorePoint++;
          }
        } else {
          auditResultsMap.get(CheckId.MISSING_SCORE_POINT).items.push(qConfig);
        }
      }

      return {}
    }

    this.customIterators['QUESTIONNAIRE_ITEMS_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      if(!this.frameworkCtrl.asmtFmrk.isQuestionnaireAssessment){
        alert("Audit Only Available for Questionnaire Assesments")
        throw new Error(AuditErrors.INVALID_ASSESMENT_TYPE)
      }
      return {}
    }
    
    this.customIterators['ALTERNATIVE_LINEAR_TEST_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      if(!this.frameworkCtrl.asmtFmrk.isAlternativeLinearTest){
        alert("Please mark the assessment as alternative Linear test in the framework settings");
        throw new Error();
      }
      return {}
    }

    
    // this.customIterators['REJECTED_ITEMS_AUDIT'] // covered by structured check

    
    this.customIterators['FIELD_TRIAL_ITEMS_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      const {auditResultsMap, questions} = auditResultsModel; 
      enum CheckId {
        FIELD_ITEMS_EXCLUDED_IN_TEST_DESIGNS = "FIELD_ITEMS_EXCLUDED_IN_TEST_DESIGNS",
        FIELD_ITEMS_IN_FT_TESTLETS_IN_NON_FT_TESTLET = "FIELD_ITEMS_IN_FT_TESTLETS_IN_NON_FT_TESTLET"
      }

      const questionSet = new Set(questions.map(q => q.id));
      const itemBankQuestions = (await this.getQuestionsByScope('ITEM_BANK')).filter(q => q?.meta.FT);

      if(this.frameworkCtrl.asmtFmrk.testFormType !== TestFormConstructionMethod.LINEAR){
        const ftQuadrant = new Set();
        this.frameworkCtrl.asmtFmrk.quadrants.forEach(q => {
          if(q.constraints.some(c => (c.param === 'FT_Pool') || (c.param === 'FT' && c.val === '1'))){
            ftQuadrant.add(q.id);
          }
        });
        const testlestlets = this.frameworkCtrl.asmtFmrk.testlets.filter(testlet => !testlet.isDisabled)
        const ftQuestions = new Set<number>();
        const nonFtQuestions = new Set<number>();
        testlestlets.forEach(testlet =>{
           if(ftQuadrant.has(+testlet.quadrant)) {
            testlet.questions.forEach(q => ftQuestions.add(+q.id))
           } else {
            testlet.questions.forEach(q => nonFtQuestions.add(+q.id))
           }
          });
        const activeQuestionInFtTestlet = new Set<number>();
        nonFtQuestions.forEach(q =>{
          if(ftQuestions.has(q)){
            activeQuestionInFtTestlet.add(q);
          }
        });
        questions.forEach(q =>{
          if(activeQuestionInFtTestlet.has(+q.id)){
            auditResultsMap.get(CheckId.FIELD_ITEMS_IN_FT_TESTLETS_IN_NON_FT_TESTLET).items.push(q);
          }
        });
      }
      
      itemBankQuestions.forEach(q =>{
        if(!questionSet.has(q.id)){
          auditResultsMap.get(CheckId.FIELD_ITEMS_EXCLUDED_IN_TEST_DESIGNS).items.push(q);
        }
      });
      return {}
    }

    
    this.customIterators['POSSIBLE_EXPECTED_ANSWER_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      const lang =  this.lang.c()     
      const {auditResultsMap, questions} = auditResultsModel; 
      enum CheckId {
        MISSING_POSSIBLE_EXPECTED_ANSWER = "MISSING_POSSIBLE_EXPECTED_ANSWER",
        CONTAINS_ONE_POSSIBLE_EXPECTED_ANSWER = "CONTAINS_ONE_POSSIBLE_EXPECTED_ANSWER",
        COUNT_MULTI_SELECT_MCQ_WITH_POSSIBLE_MISSING_EA = "COUNT_MULTI_SELECT_MCQ_WITH_POSSIBLE_MISSING_EA",
        ITEM_MISSING_CORRECT_ANSWER = "ITEM_MISSING_CORRECT_ANSWER",
        MULTI_POSSIBLE_EXPECTED_ANSWER = "MULTI_POSSIBLE_EXPECTED_ANSWER",
        SINGLE_MCQ_MISSING_POSSIBLE_CORRECT_ANSWER = "SINGLE_MCQ_MISSING_POSSIBLE_CORRECT_ANSWER",
        FORMATTED_RESPONSE_WITH_MULTIPLE_SCORES = "FORMATTED_RESPONSE_WITH_MULTIPLE_SCORES",
      };

      for(const qConfig of questions){
        //skip all reading passages
        if(qConfig.isReadingSelectionPage){
          continue;
        }

        // check for possible answers through simulated submissions
        const expectedAnswers = (await this.itemEditCtrl.findAllExpectedAnswer(+qConfig.id))?.filter(ea => ea.lang === lang)

        const qContent = this.getQuestionContent(qConfig);
        const scorableContent = this.getQuestionContentEntryElements(qContent, false);
        scorableContent.forEach(content => {
          if(content.elementType === ElementType.MCQ && (<IContentElementMcq>content).isMultiSelect){
            const qC = (<IContentElementMcq>content);
            const numSelected = +qC.maxOptions | 1;
            const options = qC.options?.length | 0;
            const correct = qC.options?.filter(opt => opt.isCorrect).length | 0;
            if(possiblePartialCorrectMCQ(numSelected, options, correct) > expectedAnswers?.length){
              auditResultsMap.get(CheckId.COUNT_MULTI_SELECT_MCQ_WITH_POSSIBLE_MISSING_EA).items.push(qConfig);
            }
          }
        });
        const { meta } = qConfig;
        const SP = meta['SP'] || 0;

        let maxPossibleScore = 0;
        
        const isSingleMcq = scorableContent.length === 1 && scorableContent[0].elementType === ElementType.MCQ && !(<IContentElementMcq>scorableContent[0]).isMultiSelect;
        let numCorrectAnswerPresent = 0;
        const seenFormattedResponseScores = new Map<string, Set<number>>();
        expectedAnswers.forEach(ea => {
          if(ea.weight && ea.score === ea.weight){ numCorrectAnswerPresent++;}
          if(seenFormattedResponseScores.has(ea.formatted_response)){
            seenFormattedResponseScores.get(ea.formatted_response).add(+ea.score)
          } else {
            seenFormattedResponseScores.set(ea.formatted_response, new Set([+ea.score]))
          }
        })
        if([...seenFormattedResponseScores.values()]?.some(responseScore => responseScore.size > 1)){
           auditResultsMap.get(CheckId.FORMATTED_RESPONSE_WITH_MULTIPLE_SCORES).items.push(qConfig);
        }
        if(numCorrectAnswerPresent === 0 && scorableContent.every(sc => [ElementType.MCQ, ElementType.CUSTOM_MCQ].includes(<ElementType>sc.elementType))) auditResultsMap.get(CheckId.ITEM_MISSING_CORRECT_ANSWER).items.push(qConfig);
        if(numCorrectAnswerPresent > 1) auditResultsMap.get(CheckId.MULTI_POSSIBLE_EXPECTED_ANSWER).items.push(qConfig);
        if(isSingleMcq){
          if(numCorrectAnswerPresent < (<IContentElementMcq>scorableContent[0]).options?.filter(opt => opt.isCorrect).length) {
            auditResultsMap.get(CheckId.SINGLE_MCQ_MISSING_POSSIBLE_CORRECT_ANSWER).items.push(qConfig);
          }
        } 
        else {

          if(expectedAnswers.length === 0 && qConfig.isReadingSelectionPage != true){
            auditResultsMap.get(CheckId.MISSING_POSSIBLE_EXPECTED_ANSWER).items.push(qConfig);
          } else if(expectedAnswers.length === 1){
            auditResultsMap.get(CheckId.CONTAINS_ONE_POSSIBLE_EXPECTED_ANSWER).items.push(qConfig);
          }
        }
      }
      return {}
    }
    
    // this.customIterators['VIDEO_ITEM_AUDIT'] // isCompleteCheck

    
    this.customIterators['BLANK_INVISIBLE_ENTRIES_ITEMS_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      const {auditResultsMap, questions} = auditResultsModel; 
      enum CheckIds {
        DRAG_DROP_MISSING_TARGETS_INSERTION = "DRAG_DROP_MISSING_TARGETS_INSERTION",
      }
      questions.forEach(question =>{
        let insertionMissingTarget = false;
        const blockElements = getQuestionDeepElements(question);
        for (let i = 0; i < blockElements.length; i++) {
          const element = blockElements[i];
        
          if (element.elementType === ElementType.INSERTION) {
            if (element?.['textBlocks']) {
              if (!element?.['textBlocks'].some(textblock => (textblock.element.elementType === ElementType.BLANK || textblock.element.elementType === ElementType.BLANK_DEPRECIATED))) {
                insertionMissingTarget = true;
                break;
              }
            } else {
              insertionMissingTarget = true;
              break;
            }
          }
        }
        if(insertionMissingTarget){
          auditResultsMap.get(CheckIds.DRAG_DROP_MISSING_TARGETS_INSERTION).items.push(question);
        }
      });
      return {}
    }

    //a
    // this.customIterators['DRAG_DROP_AUDIT_MISSING_KEYS']
  

    //b
    // this.customIterators['DRAG_DROP_AUDIT_MISSING_DIMENSIONS']
  
    
    this.customIterators['ASSOCIATED_READING_PASSAGE_AUDIT'] = async (auditResultsModel:AuditResultCore) => {
      
      const {questions, auditResultsMap} = auditResultsModel;
      
      enum CheckId {
        MISSING_READING_PASSAGE = "MISSING_READING_PASSAGE",
        INVALID_READING_PASSAGE = 'INVALID_READING_PASSAGE',
        TRAILING_SPACE_IN_READING_PASSAGE = 'TRAILING_SPACE_IN_READING_PASSAGE',
        MISSING_BOOKMARK_ID = 'MISSING_BOOKMARK_ID',
        MISSING_TARGET_ITEM = 'MISSING_TARGET_ITEM',
        INVALID_TARGET_ITEM = 'INVALID_TARGET_ITEM',
        TRAILING_SPACE_IN_TARGET_ITEM = 'TRAILING_SPACE_IN_TARGET_ITEM',
        INVALID_BOOKMARK_ID = 'INVALID_BOOKMARK_ID',
        TRAILING_SPACE_IN_BOOKMARK_ID = 'TRAILING_SPACE_IN_BOOKMARK_ID',
        MISALIGNED_READING_PASSAGE = 'MISALIGNED_READING_PASSAGE',
      };
      const scoredQuestions =  questions.filter(q => !q.isReadingSelectionPage && !q.isQuestionnaire);
      const nonScoredQuestions = questions.filter(q => q.isReadingSelectionPage && !q.isQuestionnaire);
      const existingBookmarksMap = new Map<string, string[]>();
      const regexBookMarks = /<\s*bookmark\s+id\s*=\s*"(.*?)"\s*>/g;
      nonScoredQuestions.forEach(passage =>{
        const label = passage.label.trim();
        existingBookmarksMap.set(label, []);
        const elements = getQuestionDeepElements(passage);
        elements.forEach(el =>{
          if(el.elementType === ElementType.TEXT && el.paragraphStyle === TextParagraphStyle.PARAGRAPHS){
            let match;
            el.paragraphList.forEach(paragraph =>{
              while((match = regexBookMarks.exec(paragraph.caption)) !== null){
                existingBookmarksMap.get(label).push(match[1]);
              }
            })
          }
          if(el.elementType === ElementType.PASSAGE){
            let match;
            while((match = regexBookMarks.exec(el['text'])) !== null){
              existingBookmarksMap.get(label).push(match[1]);
            }
          }
        })
      });
      scoredQuestions.forEach(q => {
        const elementBasedFails = {}
        const questionPassagePrefix = q.label?.trim()?.split('_')?.[0];
        const readSelections = q.readSelections;
        if(!readSelections?.length){
          auditResultsMap.get(CheckId.MISSING_READING_PASSAGE).items.push(q);
        } else {
          readSelections.forEach(readSel => {
            if(!existingBookmarksMap.has(readSel)){
              if(existingBookmarksMap.has(readSel.trim())){
                elementBasedFails[CheckId.TRAILING_SPACE_IN_READING_PASSAGE] = true;
              } else {
                elementBasedFails[CheckId.INVALID_READING_PASSAGE] = true;
              }
            }
            if(readSel && readSel.trim()?.split('/')?.[0] !== questionPassagePrefix){
              elementBasedFails[CheckId.MISALIGNED_READING_PASSAGE] = true
            }
          })
        }
        const elements = getQuestionDeepElements(q);
        
        elements.forEach(e => {
          if(e.elementType === ElementType.TEXT && e.paragraphStyle === TextParagraphStyle.ADVANCED_INLINE){
            for(let textElement of e.advancedList) {
              if(textElement.elementType === ElementType.BOOKMARK_LINK){
                const targetItem = textElement.itemLabel;
                const bookmarkId = textElement.bookmarkId;
                const targetItemTrimmed = targetItem?.trim();
                const bookmarkIdTrimmed = bookmarkId?.trim();
                if(!bookmarkId){
                  elementBasedFails[CheckId.MISSING_BOOKMARK_ID] = true;
                } else if(!existingBookmarksMap.get(targetItemTrimmed)?.includes(bookmarkId)){
                  // If the trimmed version isn't included regular invalid
                  if(existingBookmarksMap.get(targetItemTrimmed)?.includes(bookmarkIdTrimmed)){
                    elementBasedFails[CheckId.TRAILING_SPACE_IN_BOOKMARK_ID] = true;
                  }
                  elementBasedFails[CheckId.INVALID_BOOKMARK_ID] = true;
                }
                if(!targetItem){
                  elementBasedFails[CheckId.MISSING_TARGET_ITEM] = true;
                } else if (!readSelections.includes(targetItem)){
                  // If the trimmed version isn't included regular invalid
                  if(readSelections.includes(targetItemTrimmed)){
                    elementBasedFails[CheckId.TRAILING_SPACE_IN_TARGET_ITEM] = true;
                  } else {
                    elementBasedFails[CheckId.INVALID_TARGET_ITEM] = true;
                  }
                }
                if(Object.values(elementBasedFails).reduce((acc, value) => acc && value, true)){
                  break;
                }
              }
            }
          }
        });
        for (let elementBasedFail in elementBasedFails){
          if(elementBasedFails[elementBasedFail]){
            auditResultsMap.get(elementBasedFail).items.push(q);
          }
        }
      });
      return {}
    }

    
    this.customIterators['ITEMS_MISSING_COMPLETION_MARK_CONDITION_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap} = auditResultsModel;
      enum CheckId {
        MORE_DRAGGABLES_THAN_TARGETS_INSERTION = 'MORE_DRAGGABLES_THAN_TARGETS_INSERTION',
        MORE_TARGETS_THAN_FILLABLE_INSERTION = 'MORE_TARGETS_THAN_FILLABLE_INSERTION',
        LESS_DRAGGABLES_THAN_MARK_COMPLETED_DND = 'LESS_DRAGGABLES_THAN_MARK_COMPLETED_DND',
        MORE_DRAGGABLES_THAN_MARK_COMPLETED_DND = 'MORE_DRAGGABLES_THAN_MARK_COMPLETED_DND',
        LESS_DRAGGABLES_THAN_TARGETS_DND = 'LESS_DRAGGABLES_THAN_TARGETS_DND',
        MORE_DRAGGABLES_THAN_TARGETS_DND = 'MORE_DRAGGABLES_THAN_TARGETS_DND',
        LESS_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING = 'LESS_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING',
        MORE_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING = 'MORE_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING',
        LESS_DRAGGABLES_THAN_TARGETS_GROUPING = 'LESS_DRAGGABLES_THAN_TARGETS_GROUPING',
        MORE_DRAGGABLES_THAN_TARGETS_GROUPING = 'MORE_DRAGGABLES_THAN_TARGETS_GROUPING'
    };
    

      questions.forEach((question)=>{
        const failedMap = {}
        getQuestionDeepElements(question).forEach((element)=>{
          // Grouping and DND use same structure so they can share the draggables and targets
          const draggables = element['draggables']?.length ?? 0;
          let targets = element['targets']?.length ?? 0;
          const isOptionsReusable = element['isOptionsReusable'];
          const isNotReusableOrMissingDraggables = (!isOptionsReusable || !draggables);
          switch(element.elementType){
            case ElementType.INSERTION:
              element['textBlocks']?.forEach(textblock =>{
                if(textblock.element.elementType === ElementType.BLANK || textblock.element.elementType === ElementType.BLANK_DEPRECIATED){
                  targets++;
                }
              });
              if(draggables > targets){
                failedMap[CheckId.MORE_DRAGGABLES_THAN_TARGETS_INSERTION] = true;
              }
              if((!element['isRepeatableOptions'] || !draggables) && targets > draggables){
                failedMap[CheckId.MORE_TARGETS_THAN_FILLABLE_INSERTION] = true;
              }
              break;
            case ElementType.GROUPING:
              const reqMinimumPlacedTarget = element['reqMinimumPlacedTarget'] ?? 0;
              if(draggables < reqMinimumPlacedTarget && isNotReusableOrMissingDraggables){
                failedMap[CheckId.LESS_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING] = true;
              }
              if(draggables > reqMinimumPlacedTarget){
                failedMap[CheckId.MORE_DRAGGABLES_THAN_MARK_COMPLETED_GROUPING] = true;
              }
              if(draggables < targets && isNotReusableOrMissingDraggables){
                failedMap[CheckId.LESS_DRAGGABLES_THAN_TARGETS_GROUPING] = true;
              }
              if(draggables > targets){
                failedMap[CheckId.MORE_DRAGGABLES_THAN_TARGETS_GROUPING] = true;
              }
              break;
            case ElementType.MOVEABLE_DND:
              const howManyToFill = element['howManyToFill'] ?? 0;
              if(draggables < howManyToFill && isNotReusableOrMissingDraggables){
                failedMap[CheckId.LESS_DRAGGABLES_THAN_MARK_COMPLETED_DND] = true;
              }
              if(draggables > howManyToFill){
                failedMap[CheckId.MORE_DRAGGABLES_THAN_MARK_COMPLETED_DND] = true;
              }
              if(draggables < targets && isNotReusableOrMissingDraggables){
                failedMap[CheckId.LESS_DRAGGABLES_THAN_TARGETS_DND] = true;
              }
              if(draggables > targets){
                failedMap[CheckId.MORE_DRAGGABLES_THAN_TARGETS_DND] = true;
              }
              break;
          }
        });
        Object.keys(failedMap).forEach( key =>
          auditResultsMap.get(key).items.push(question)
        )

      });
      
      return {};
    };
    
    
    // this.customIterators['FILE_EXTENTION_ITEM_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      
    //   console.log(auditResultsModel)
    //   return {};
    // }
    
    
    this.customIterators['IS_RESPONDABLE_ITEM_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      enum CheckId {
        READING_SEL_OR_QUESTIONNAIRE_AND_IS_RESPONDABLE = 'READING_SEL_OR_QUESTIONNAIRE_AND_IS_RESPONDABLE'
      }
      const {auditSlug, questions, auditResultsMap} = auditResultsModel;
      questions.forEach(question =>{
        if(question.meta && question.meta.hasOwnProperty('is_respondable')){
          let is_respondable_val = <string>question.meta['is_respondable'];
          if(is_respondable_val){
            const reading_sel_checked = question.isReadingSelectionPage;
            const questionnaire_checked = question.isQuestionnaire;
            if(reading_sel_checked || questionnaire_checked){
              auditResultsMap.get(CheckId.READING_SEL_OR_QUESTIONNAIRE_AND_IS_RESPONDABLE).items.push(question);
            }
          }
          }
      });
      this.markAuditAsRan(auditSlug, false, true, auditResultsMap);
      return {}
    }

    //this.customIterators['OVERALL_EXPECTATION_AUDIT']
    

    
    this.customIterators['MULTIMEDIA_ASSET_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      enum CheckId{
        GET_TOTAL_MULTI_MEDIA_COUNT_AND_FILESIZE = 'GET_TOTAL_MULTI_MEDIA_COUNT_AND_FILESIZE',
      }
      const size_limit = parseInt(prompt("Enter in kilobytes the total file size you want to flag items based on:"));
      if(!size_limit || size_limit < 1){
        alert('Please enter a value greater than 1 KB');
        throw new Error();
      }

      const {auditSlug,checkIds, auditResultsMap} = auditResultsModel;
      const {questions} = this.itemBankCtrl;
      const assetResults = await this.getAssetResults(questions)
      let remodeledResults;
      for(let checkId of checkIds){
        if(checkId === CheckId.GET_TOTAL_MULTI_MEDIA_COUNT_AND_FILESIZE){
          remodeledResults = this.remodeledData(assetResults,size_limit);
          this.auditQuestionMem[auditSlug] = remodeledResults;
          this.auditResultHeader = `${checkId} - Total Size Limit set to ${size_limit} KB`;
          auditResultsMap.get(checkId).items.push(...remodeledResults);
        } 
      }
      
      this.markAuditAsRan(auditSlug, false, true, auditResultsMap);
      return {isCustomResults: true};
    }

    
    this.customIterators['PJ_SCAN_AUDIT'] = async (auditResultsModel: AuditResultCore, skipDisabled: boolean) => {

      const ranCode = 'PJ_SCAN_AUDIT';
      enum CheckId {
        MISSING_SCAN_SLUG = "MISSING_SCAN_SLUG",
        INCORRECTLY_MAPPED_SCAN_SLUG = "INCORRECTLY_MAPPED_SCAN_SLUG",
        MISSING_PAPER_TEXT = "MISSING_PAPER_TEXT",
        SCAN_ID_PRESERVED_PARAMS = "SCAN_ID_PRESERVED_PARAMS", // applicable to the whole test design, not a question
      };

      interface IAuditResult { id: CheckId,  caption: string, items: any[], type: EAuditElementType};

      const auditResults: IAuditResult[] = [
        {id: CheckId.MISSING_SCAN_SLUG, caption: 'Items that have scan slug missing', items: [], type: 'QUESTION'},
        {id: CheckId.INCORRECTLY_MAPPED_SCAN_SLUG, caption: 'Items that have scan slug incorrectly mapped', items: [], type: 'QUESTION'},
        {id: CheckId.MISSING_PAPER_TEXT, caption: 'Items that have paper text or voiceover missing', items: [], type: 'QUESTION'},
        {id: CheckId.SCAN_ID_PRESERVED_PARAMS, caption: 'Assessment framework has no configured SCAN_ID in preserved params', items: [], type: 'QUESTION'},
      ];
      const { auditResultsMap} = auditResultsModel;
      auditResults.forEach(result => auditResultsMap.set(result.id, result))

      // const questions = isScopedToTestDesign ? await this.getScreens() : this.frameworkCtrl.itemBankCtrl.getItems();
      // console.log('questions', questions)
      console.log('asmtFrmwrk', this.frameworkCtrl.asmtFmrk)
      console.log('testlest', this.frameworkCtrl.testletCtrl)

      for(const testlet of this.frameworkCtrl.asmtFmrk.testlets) {
        //- Only Audit Enabled Forms
        if(testlet.isDisabled && skipDisabled) {
          continue;
        }

        const questions = testlet.questions.map(q => this.frameworkCtrl.itemBankCtrl.getQuestionById(q.id)).filter(q => q);
        console.log('questions', questions)

        for(const qConfig of questions){
          const qContent = this.getQuestionContent(qConfig);

          // Only Audit Input Long Text Dual
          const longTextDual = qContent.filter((content: IContentElementInput) => {
            return content.elementType === ElementType.INPUT && content.isDual
          });

          if(!longTextDual.length) {
            continue;
          }
        
          // Check for scan slug presence
          const scanSlug = qConfig.meta?.SCAN_ID;
          console.log('scanSlug', scanSlug);
          if(!scanSlug) {
            auditResultsMap.get(CheckId.MISSING_SCAN_SLUG).items.push(qConfig);
          }

          // Check for scan slug mapping
          const sectionToValidSlugMap = {
            0: ['SESSION_A'],
            1: ['SESSION_B'],
            2: ['SESSION_C'],
            3: ['SESSION_DR', 'SESSION_DW']
          }
          const section = +testlet.section - 1;
          const validSlugs = sectionToValidSlugMap[section] || [];
          console.log('slugCheck', section, validSlugs, scanSlug, validSlugs.includes(scanSlug));
          if(!validSlugs.includes(scanSlug)) {
            auditResultsMap.get(CheckId.INCORRECTLY_MAPPED_SCAN_SLUG).items.push(qConfig);
          }

          // Check for Session D scan slug mapping based on similarity slug
          const sectionToValidSSlugMap = {
            'SESSION_DR': ['IN', 'LN'],
            'SESSION_DW': ['SW']
          }
          const validSSlugs = sectionToValidSSlugMap[scanSlug] || []; 
          console.log('validSSlugs', testlet.similaritySlug, validSSlugs, validSSlugs.includes(testlet.similaritySlug))
          if(validSSlugs.length > 0 && (!testlet.similaritySlug || !validSSlugs.includes(testlet.similaritySlug))) {
            auditResultsMap.get(CheckId.INCORRECTLY_MAPPED_SCAN_SLUG).items.push(qConfig);
          }

          // Check for paper text and voiceover presence
          longTextDual.forEach((content: IContentElementInput) => {
            const noVoiceoverUrl = !content?.paperVoiceOver?.url || !content?.paperVoiceOver?.url.trim().length;
            const noVoiceOverScript = !content.paperVoiceOver?.script || !content?.paperVoiceOver?.script.trim().length;
            const noVoiceover = noVoiceoverUrl && noVoiceOverScript;
            
            if(noVoiceover || !content?.paperText || !content?.paperText.trim().length) {
              auditResultsMap.get(CheckId.MISSING_PAPER_TEXT).items.push(qConfig);
            }
          });
        }
      }

      //Validate SCAN_ID is present in Preserved Parameters section of the Assessment Framework
      const preservedMetaParams = this.frameworkCtrl.asmtFmrk.preservedMetaParams || [];
      if(!preservedMetaParams.includes('SCAN_ID')) {
        auditResultsMap.get(CheckId.SCAN_ID_PRESERVED_PARAMS).items.push(true);
      }
      
      const results = this.getFormattedResults(Object.values(CheckId), auditResultsMap);
      this.markAuditAsRan(ranCode, false, true, results);
    
      return {}
    }

    //this.customIterators['SKILL']
    
    //this.customIterators['SKPTILL']
    
    //this.customIterators['PASSAGE']
    
    //this.customIterators['STRAND']

    //this.customIterators['REPORTING_SUBJECT']

    
    this.customIterators['ITEM_MATRIX_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      const {auditResultsMap, questions} = auditResultsModel;
      enum CheckId {
        UNCONFIRMED = "UNCONFIRMED",
        OUTDATED = "OUTDATED",
        // FLAGGED = "FLAGGED",
        REUSABLE_DRAGGABLES = "REUSABLE_DRAGGABLES",
        MULTI_COMBINATION = "MULTI_COMBINATION",
        MULTI_TARGET = "MULTI_TARGET",
        CUSTOM_VALIDATION = "CUSTOM_VALIDATION",
        NON_SCORING_MATRIX = "NON_SCORING_MATRIX",
        FLAGGED_ITEMS = "FLAGGED_ITEMS",
        Q_EXP_ANS_MULTI_ACC = "Q_EXP_ANS_MULTI_ACC"
      };
      
      const lang = this.lang.c() 
      for (const qConfig of questions) {
        const qContent = getQuestionRespondableDeep(qConfig);
        let failedMap: { [key: string]: boolean } = {};
        let matrixCount = 0;
        const expectedAnswers = (await this.itemEditCtrl.findAllExpectedAnswer(+qConfig.id))?.filter(ea => ea.lang === lang);
        qContent.forEach(content => {
          if(scoreMatrixElementTypes.includes(content.elementType)){
            matrixCount++;
          }
          if (!failedMap[CheckId.NON_SCORING_MATRIX] && !scoreMatrixElementTypes.includes(content.elementType)) {
            failedMap[CheckId.NON_SCORING_MATRIX] = true;
          }
      
          if (scoreMatrixElementTypes.includes(content.elementType) && content.scoreMatrix) {
            const columnCorrectSet = new Set(); // set used to find if column has multiple correct
            for (const row of content.scoreMatrix.values || []) {
              if(failedMap[CheckId.Q_EXP_ANS_MULTI_ACC]) break; // only do this if it hasn't been flagged already
              let correctInRow = 0; // Value used to check if the row has multiple correct entries
            
              for (let idx = 0; idx < row.length; idx++) {
                const v = row[idx];
            
                if (v?.value) {
                  // Increment correct count for the row
                  if (++correctInRow > 1) {
                    failedMap[CheckId.Q_EXP_ANS_MULTI_ACC] = true;
                    break; // Exit all loops
                  }
            
                  // Check if this column index has already been marked correct
                  if (columnCorrectSet.has(idx)) {
                    failedMap[CheckId.Q_EXP_ANS_MULTI_ACC] = true;
                    break; // Exit all loops
                  } else {
                    columnCorrectSet.add(idx);
                  }
                }
              }
            }
            if (!failedMap[CheckId.UNCONFIRMED] && content.scoreMatrix.isConfirmed == false) {
              failedMap[CheckId.UNCONFIRMED] = true;
            }
      
            if (!failedMap[CheckId.OUTDATED] && content.scoreMatrix.isUpdated == false) {
              failedMap[CheckId.OUTDATED] = true;
            }
      
            if (!failedMap[CheckId.REUSABLE_DRAGGABLES] && (content.elementType == ElementType.MOVEABLE_DND || content.elementType == ElementType.GROUPING) && (<IContentElementMoveableDragDrop>content).isOptionsReusable == true) {
              failedMap[CheckId.REUSABLE_DRAGGABLES] = true;
            }
      
            if (content.elementType == ElementType.MOVEABLE_DND) {
              if (!failedMap[CheckId.MULTI_COMBINATION] && (<IContentElementMoveableDragDrop>content).isAcceptMultipleCombinations == true) {
                failedMap[CheckId.MULTI_COMBINATION] = true;
              }
      
              if (!failedMap[CheckId.MULTI_TARGET] && (<IContentElementMoveableDragDrop>content).isMultipleOptionsRight == true) {
                failedMap[CheckId.MULTI_TARGET] = true;
              }
      
              if (!failedMap[CheckId.CUSTOM_VALIDATION] && (<IContentElementMoveableDragDrop>content).isCustomValidataion == true) {
                failedMap[CheckId.CUSTOM_VALIDATION] = true;
              }
            }
          }
        });
        if(!failedMap[CheckId.NON_SCORING_MATRIX] && matrixCount === 1){
          const flaggedAnswers = expectedAnswers.filter((ans: any) => {
            return !ans.is_score_matrix_validated;
          });
          if (flaggedAnswers.length > 0) {
            failedMap[CheckId.FLAGGED_ITEMS] = true;
          }
        }
        // Push all failed conditions at the end
        Object.keys(failedMap).forEach(key => {
          auditResultsMap.get(key).items.push(qConfig);
        });
      }
      
      
      return {}
    }

    
    this.customIterators['ITEM_TYPE_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap} = auditResultsModel;
      enum CheckId {
        ITEM_TYPE_MISSING = 'ITEM_TYPE_MISSING',
        CLK_NO_MCQS = 'CLK_NO_MCQS',
        DP_NO_MCQS = 'DP_NO_MCQS',
        DDP_NO_TWO_MCQS = 'DDP_NO_TWO_MCQS',
        DG_NO_VALID_BLOCKS = 'DG_NO_VALID_BLOCKS',
        CH_NO_SELECTION_TABLE = 'CH_NO_SELECTION_TABLE',
        SW_NO_SHORT_ANSWER = 'SW_NO_SHORT_ANSWER',
        LW_NO_SHORT_ANSWER = 'LW_NO_SHORT_ANSWER',
        OR_NO_SHORT_ANSWER = 'OR_NO_SHORT_ANSWER',
      }    
      questions.forEach(q =>{
        if(!q?.meta?.ItemType){
          auditResultsMap.get(CheckId.ITEM_TYPE_MISSING).items.push(q);
          return;
        }
        const itemType:ItemTypesParam = q?.meta?.ItemType;
        const blockCount = countKeyValues(getQuestionContent(q), 'elementType');
        const mcqElements = this.getQuestionContentEntryElements(q.content, true).filter(c => c.elementType === ElementType.MCQ);
        const mcqDisplayStyleCountMap = countKeyValues(mcqElements, 'displayStyle');
        const totalDropdownCount = (typeof mcqDisplayStyleCountMap[McqDisplay.DROPDOWN] === 'number' ? mcqDisplayStyleCountMap[McqDisplay.DROPDOWN] : 0) + (typeof mcqDisplayStyleCountMap[McqDisplay.CUSTOM_DROPDOWN] === 'number' ? mcqDisplayStyleCountMap[McqDisplay.CUSTOM_DROPDOWN] : 0);
        switch(itemType){
          case 'DDP':
            if( totalDropdownCount < 2){
              auditResultsMap.get(CheckId.DDP_NO_TWO_MCQS).items.push(q);
            }
            break;
          case 'DP':
            if(!totalDropdownCount){
              auditResultsMap.get(CheckId.DP_NO_MCQS).items.push(q);
            }
            break
          case 'CLK':
            if(!blockCount[ElementType.MCQ] || blockCount[ElementType.MCQ] <= totalDropdownCount){
              auditResultsMap.get(CheckId.CLK_NO_MCQS).items.push(q);
            }
            break
          case 'DG':
            if(!blockCount[ElementType.MOVEABLE_DND] && !blockCount[ElementType.ORDER] && !blockCount[ElementType.GROUPING] && !blockCount[ElementType.INSERTION]){
              auditResultsMap.get(CheckId.DG_NO_VALID_BLOCKS).items.push(q);
            }
            break;
          case 'CH':
            if(!blockCount[ElementType.SELECT_TABLE]){
              auditResultsMap.get(CheckId.CH_NO_SELECTION_TABLE).items.push(q);
            }
            break;
          case 'OR':
            if(!blockCount[ElementType.INPUT]){
              auditResultsMap.get(CheckId.OR_NO_SHORT_ANSWER).items.push(q);
            }
            break;
          case 'LW':
            if(!blockCount[ElementType.INPUT]){
              auditResultsMap.get(CheckId.LW_NO_SHORT_ANSWER).items.push(q);
            }
            break;
          case 'SW':
            if(!blockCount[ElementType.INPUT]){
              auditResultsMap.get(CheckId.SW_NO_SHORT_ANSWER).items.push(q);
            }
            break;
        }
      })
      
      
      return {}
    }

    //// TODO make the audit show individual checks for each preserved parameter
    this.customIterators['ITEM_MAP_MODULE_META_AUDIT'] = async (auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap, } = auditResultsModel;
      const preservedMetaParams = this.frameworkCtrl.asmtFmrk.preservedMetaParams;
      if(!preservedMetaParams){
        alert("No Preserved Params to Check");
        throw new Error("NO_RESERVED_PARAMS");
      }
      enum CheckId {
        MISSING_META = 'MISSING_RESERVED_META'
      }
      ITEM_MAP_MODULE_META_AUDIT_CONST_LABELS.forEach(questionLabel =>{
        const question = this.itemBankCtrl.getQuestionByLabel(questionLabel);
        if(question){
          questions.push(question);
        }
      });
      questions.forEach(q =>{
        let isFailed = false;
        preservedMetaParams.forEach(param =>{
          if(isNullOrEmptyOrUndefined(q.meta?.[param])){
            isFailed = true;
          }
        });
        if(isFailed){
          auditResultsMap.get(CheckId.MISSING_META).items.push(q);
        }
      });
      return {}
    }

    
    this.customIterators['ITEM_CATEGORY_AUDIT'] = async(auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap, } = auditResultsModel;
      enum CheckId {
        ITEM_CATEGORY_QUESTIONNAIRE_MISSMATCH = 'ITEM_CATEGORY_QUESTIONNAIRE_MISSMATCH'
      }
      const questionnaireD1Params = ['S1', 'S2'];
      questions.forEach(q =>{
        const {meta, isQuestionnaire} =  q
        const {Item_Category, D1} = meta
        if(Item_Category === 'Questionnaire' && (!isQuestionnaire || !questionnaireD1Params.includes(D1)))
        auditResultsMap.get(CheckId.ITEM_CATEGORY_QUESTIONNAIRE_MISSMATCH).items.push(q);
      });
      return {}
    }
    this.customIterators['RESOURCE_AUDIT'] = async(auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap, } = auditResultsModel;
      enum CheckId {
        RESOURCE_ITEMS_WITH_RESPONDABLE = 'RESOURCE_ITEMS_WITH_RESPONDABLE',
        ITEMS_WITHOUT_RESPONDABLE_AND_NOT_READING_SELECTION = 'ITEMS_WITHOUT_RESPONDABLE_AND_NOT_READING_SELECTION',
        ITEMS_WITH_IS_RESPONDABLE_AND_READING_SELECTION = 'ITEMS_WITH_IS_RESPONDABLE_AND_READING_SELECTION'
      }
      questions.forEach(question =>{
        const isReadingSelection = question.isReadingSelectionPage;
        const respondables = getQuestionRespondableDeep(question);
        if(isReadingSelection && respondables.length){
          auditResultsMap.get(CheckId.RESOURCE_ITEMS_WITH_RESPONDABLE).items.push(question);
        }
        if(!isReadingSelection && !respondables.length){
          auditResultsMap.get(CheckId.ITEMS_WITHOUT_RESPONDABLE_AND_NOT_READING_SELECTION).items.push(question);
        }
        const {is_respondable} = question.meta
        if(isReadingSelection && !!is_respondable){
          auditResultsMap.get(CheckId.ITEMS_WITH_IS_RESPONDABLE_AND_READING_SELECTION).items.push(question);
        }
      })

      return {}
    }
    this.customIterators['MSCAT_PANEL_MODULE_AUDIT'] = async(auditResultsModel: AuditResultCore) => {
      const {questions, auditResultsMap, mscatPanels} = auditResultsModel;
      enum CheckId {
        MISALIGNED_ITEM_COUNT_2_3 = 'MISALIGNED_ITEM_COUNT_2_3',
        MISALIGNED_ITEM_COUNT_5_6 = 'MISALIGNED_ITEM_COUNT_5_6',
        MISALIGNED_SP_SUM_2_3 = 'MISALIGNED_SP_SUM_2_3',
        MISALIGNED_SP_SUM_5_6 = 'MISALIGNED_SP_SUM_5_6',
      }
      const scorePointMap = new Map<number, number>();
      questions.forEach(question =>{
        const scorePoint = <string>question.meta['SP'];
        if(checkPropCondVal(scorePoint, 'IS_NAN')){
          scorePointMap.set(question.id, 0);
        } else {
          scorePointMap.set(question.id, +scorePoint);
        }
      })
      mscatPanels.forEach(panel => {
        const moduleItemMap = new Map<number, {scorePointSum:number, itemCount:number}>();
        panel.modules.forEach(module => {
          const moduleInfo = {scorePointSum:0, itemCount: module.__cached_itemIds?.length ?? 0}
          moduleItemMap.set(+module.moduleId, moduleInfo);
          module.__cached_itemIds?.forEach(id =>{
            moduleInfo.scorePointSum += scorePointMap.get(id)
          })
        })
        const moduleTwoInfo = moduleItemMap.get(2);
        const moduleThreeInfo = moduleItemMap.get(3);
        const moduleFiveInfo = moduleItemMap.get(5);
        const moduleSixInfo = moduleItemMap.get(6);
        if(moduleTwoInfo.itemCount != moduleThreeInfo.itemCount){
          auditResultsMap.get(CheckId.MISALIGNED_ITEM_COUNT_2_3).items.push(panel);
        }
        if(moduleFiveInfo.itemCount != moduleSixInfo.itemCount){
          auditResultsMap.get(CheckId.MISALIGNED_ITEM_COUNT_5_6).items.push(panel);
        }
        if(moduleTwoInfo.scorePointSum != moduleThreeInfo.scorePointSum){
          auditResultsMap.get(CheckId.MISALIGNED_SP_SUM_2_3).items.push(panel);
        }
        if(moduleFiveInfo.scorePointSum != moduleSixInfo.scorePointSum){
          auditResultsMap.get(CheckId.MISALIGNED_SP_SUM_5_6).items.push(panel);
        }
      });

      return {}
    }

  }


  // interface methods
  private async getQuestionsByScope(questionScope: AuditQuestionScope, languageSensitive: boolean = true){
    return getQuestionsByScope(questionScope, {
      lang: this.lang.c(),
      itemBankCtrl: this.itemBankCtrl, 
      frameworkCtrl: this.frameworkCtrl, 
    },
    languageSensitive
  )
  }

  private getQuestionContent(question: IQuestionConfig, lang?: 'en' | 'fr') {
    return getQuestionContent(question, lang)
  }

  // private isRejectedItem(qConfig: IQuestionConfig) {
  //   if(qConfig?.meta){
  //     return !!qConfig.meta?.['REJ']
  //   }
  //   return false;
  // }

  private async processDraggableElements(element: IContentElementDndDraggable, results: IAssetResult[]) {
    let totalSize = 0;
    let totalImageSize = 0;
    let totalAudioSize = 0;
    const { element: contentElement } = element
    if (contentElement.elementType === ElementType.IMAGE) {
      const { image }: { image: IContentElementImage } = contentElement.images.default;
      const assetSize = await getAssetSize(image?.url);
      results.push({ url: image.url, size: Math.round(assetSize * 100) / 100, entryId: image.entryId, type: ElementType.IMAGE });
      totalSize += assetSize;
      totalImageSize += assetSize;
    }
    if (contentElement.voiceover?.url) {
      const assetSize = await getAssetSize(contentElement.voiceover.url);
      results.push({ url: contentElement.voiceover.url, size: Math.round(assetSize * 100) / 100, entryId: contentElement.voiceover.entryId, type: ElementType.AUDIO });
      totalSize += assetSize;
      totalAudioSize += assetSize;
    }
    if (element.voiceover?.url) {
      const assetSize = await getAssetSize(element.voiceover.url);
      results.push({ url: element.voiceover.url, size: Math.round(assetSize * 100) / 100, entryId: element.voiceover.entryId, type: ElementType.AUDIO });
      totalSize += assetSize;
      totalAudioSize += assetSize;
    }
    return { totalSize, totalImageSize, totalAudioSize };
  }


  private async processMcQOptions(option: IContentElementMcqOption, results: IAssetResult[]) {
    let totalSize = 0;
    let totalImageSize = 0;
    let totalAudioSize = 0;
    if (option.elementType === ElementType.IMAGE) {
      const assetSize = await getAssetSize(option?.url);
      results.push({ url: option.url, size: Math.round(assetSize * 100) / 100, entryId: option.entryId, type: ElementType.IMAGE });
      totalSize += assetSize;
      totalImageSize += assetSize;
    }
    if (option.voiceover?.url) {
      const assetSize = await getAssetSize(option.voiceover.url);
      results.push({ url: option.voiceover.url, size: Math.round(assetSize * 100) / 100, entryId: option.voiceover.entryId, type: ElementType.AUDIO });
      totalSize += assetSize;
      totalAudioSize += assetSize;
    }
    return { totalSize, totalImageSize, totalAudioSize };
  }


  private async processContentElement(content: IContentElementTemplate | IContentElementMcq | IContentElement, results: IAssetResult[]) {
    let totalSize = 0;
    let totalImageSize = 0;
    let totalAudioSize = 0;

    if (content.elementType === ElementType.IMAGE) {
      const { image }: { image: IContentElementImage } = content.images.default;
      const assetSize = await getAssetSize(image.url);
      results.push({ url: image.url, size: Math.round(assetSize * 100) / 100, entryId: image.entryId, type: ElementType.IMAGE });
      totalSize += assetSize;
      totalImageSize += assetSize;
    }

    if (content.voiceover?.url) {
      const assetSize = await getAssetSize(content.voiceover.url);
      results.push({ url: content.voiceover.url, size: Math.round(assetSize * 100) / 100, entryId: content.voiceover.entryId, type: ElementType.AUDIO });
      totalSize += assetSize;
      totalAudioSize += assetSize;
    }

    if (content.elementType === ElementType.MCQ) {
      if (Array.isArray((content as IContentElementMcq).options)) {
        for (let option of (content as IContentElementMcq).options) {
          const { totalSize: optionTotalSize, totalImageSize: optionTotalImageSize, totalAudioSize: optionTotalAudioSize } = await this.processMcQOptions(option, results);
          totalSize += optionTotalSize;
          totalImageSize += optionTotalImageSize;
          totalAudioSize += optionTotalAudioSize;
        }
      }
    }
    if (content.elementType === ElementType.GROUPING) {
      if (Array.isArray((content as IContentElementGroup).draggables)) {
        for (let element of (content as IContentElementGroup).draggables) {
          const { totalSize: draggableTotalSize, totalImageSize: draggableTotalImageSize, totalAudioSize: draggableTotalAudioSize } = await this.processDraggableElements(element, results);
          totalSize += draggableTotalSize;
          totalImageSize += draggableTotalImageSize;
          totalAudioSize += draggableTotalAudioSize;

        }
      }
    }
    if (content.elementType === ElementType.TEMPLATE || content.elementType === ElementType.FRAME) {
      if (Array.isArray(content["content"])) {
        for (let element of content["content"]) {
          const { totalSize: nestedElTotalSize, totalImageSize: nestedElTotalImageSize, totalAudioSize: nestedElTotalAudioSize } = await this.processContentElement(element, results);
          totalSize += nestedElTotalSize;
          totalImageSize += nestedElTotalImageSize;
          totalAudioSize += nestedElTotalAudioSize;

        }
      }
    }
    return { totalSize, totalImageSize, totalAudioSize };
  }

  private async getAssetResults(questions: IQuestionConfig[]): Promise<Map<string, IQuestionResult>> {
    const assetResult = new Map<string, IQuestionResult>();
    await Promise.all(questions.map(async q => {
      const qContent = await this.getQuestionContent(q);
      const results: IAssetResult[] = [];
      let totalSize = 0;
      let totalImageSize = 0;
      let totalAudioSize = 0;
      for (let content of qContent) {
        const { totalSize: contentTotalSize, totalImageSize: contentTotalImageSize, totalAudioSize: contentTotalAudioSize } = await this.processContentElement(content, results);
        totalSize += contentTotalSize;
        totalImageSize += contentTotalImageSize;
        totalAudioSize += contentTotalAudioSize;
      }
      if (q.voiceover?.url) {
        const assetSize = await getAssetSize(q.voiceover.url);
        results.push({ url: q.voiceover.url, size: Math.round(assetSize * 100) / 100, entryId: q.voiceover.entryId, type: "Question Voice-Over" });
        totalSize += assetSize;
        totalAudioSize += assetSize;
      }
      if (results.length > 0) {
        assetResult.set(`${q.id} - ${q.label}`, { qid: q.id, results, imageSize: Math.round(totalImageSize * 100) / 100, audioSize: Math.round(totalAudioSize * 100) / 100, totalSize: Math.round(totalSize * 100) / 100 });
      }
    }));

    return assetResult;
  }


  remodeledData(imageResults: Map<string, IQuestionResult>, sizeLimit: number){
    const remodeledResults = Array.from(imageResults.entries()).map(([label,value]) => {
      const content: IExpansionPanelContent[] = [
        { type: ExpansionPanelContentType.JSON_VIEW,label: `Total Size = ${value.totalSize} KB; Total Image Size = ${value.imageSize} KB; Total Audio Size = ${value.audioSize} KB`,
         data: value.results.map((result) => { return { type:`${result.type}`,id: `${result.entryId}`, size:`${result.size} KB`,url:`${result.url}`  } }) }]
      const greaterThanSize = (value.totalSize > sizeLimit) ? true : false

      return {
        title: `${label}`,
        subTitle: `Total Files = ${value.results.length}`,
        content,
        greaterThanSize,
        style: {'border':`solid 0.1em ${greaterThanSize ? 'orange' : 'green'}`}
      }
    })
    return remodeledResults;
  }

  getAuditLogs() {
    this.auditLogs = [];
    this.auth.apiGet(this.routes.TEST_AUTH_ITEM_SET_AUDITS, this.itemBankCtrl.customTaskSetId, 
      { query: {
          lang: this.lang.getCurrentLanguage()
      }}).then((res) => {
        const latestDesignDate = this.frameworkCtrl.publishingCtrl.testDesignReleaseHistory?.[0] ? 
          new Date(this.frameworkCtrl.publishingCtrl.testDesignReleaseHistory?.[0]?.created_on) :
          null;
        res.forEach((log) => {
          const logDate = new Date(log.audited_on)
          if (!latestDesignDate) {
            log.is_new = true;
          } else {
            if(logDate > latestDesignDate) {
              log.is_new = true;
            } else {
              log.is_new = false;
            }
          }
          log.audited_on = logDate;
          this.auditLogs.push(log);
        })
    })
  }

  getAuditLog(auditSlug: string) {
    if(this.auditLogs) {
      return this.auditLogs.find(log => log.audit_slug == auditSlug)
    } 

    return null;
  }  
  removeDuplicatesFromQuestions(questions: IQuestionConfig[]){
    return questions.filter((v,i,a)=>a.findIndex(v2=>(v2.id===v.id))===i)
  }
  private markAuditRunning(ranCode: string, isRunning: boolean){
    this.auditsRunning[ranCode] = isRunning
  }  

  private getContext() : IContext {
    return {
      lang: this.lang.c(),
      frameworkCtrl: this.frameworkCtrl,
      itemBankCtrl: this.itemBankCtrl
    }
  }
}

function factorial(x:number){
  return (x > 1) ? x * factorial(x-1) : 1;
}

/**
 * Calculates all number of all combinations that contain at least one correct answer
 * @param numSelected 
 * @param options 
 * @param correct 
 * @returns 
 */
function possiblePartialCorrectMCQ(numSelected:number, options:number, correct:number){
  return posibleCombinations(options, numSelected) - posibleCombinations(options, (numSelected - correct));
}

/**
 * Calculates the number of possible combinations that can be made by choosing a specific number of items
 * from a larger set, where the order of selection does not matter.
 *
 * @param optionsAvailable The total number of distinct options available to choose from.
 * @param slots The number of items to be selected from the available options.
 * @returns The number of possible combinations=.
 */
function posibleCombinations(optionsAvailable: number, slots: number) {
  return factorial(optionsAvailable) / (factorial(slots) * factorial(optionsAvailable - slots));
}

