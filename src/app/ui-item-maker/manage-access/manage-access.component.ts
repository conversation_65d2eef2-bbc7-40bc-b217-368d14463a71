import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AuthService, getFrontendDomain } from 'src/app/api/auth.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from 'src/app/api/routes.service';
import { EditSaveFormlet, IEditSaveCtrl } from 'src/app/ui-partial/edit-save/edit-save.component';
import { MemDataPaginated, strIncludes } from 'src/app/ui-partial/paginator/helpers/mem-data-paginated';
import { AuthRolesService } from '../auth-roles.service';
import { IItemSetDef, ItemMakerService } from '../item-maker.service';
import { FilterSettingMode } from 'src/app/ui-partial/capture-filter-range/capture-filter-range.component';
import { UserRoles } from 'src/app/api/models/roles';
import * as moment from 'moment-timezone';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';

interface IAuthorAccess {
  id: number,
  name: string, 
  email: string,
  source?: PermSource,
  formlet: EditSaveFormlet,
  roleTypeFc: FormControl,
  expiresOn?: moment,
  isPending: boolean
}

export enum PermSource {
  GROUP = 'GROUP',
  ASSESSMENT = 'ASSESSMENT'
}
enum AccessCol {
  USER_ACCESS = 'USER_ACCESS',
  ID = 'ID',
  NAME = 'NAME',
  EMAIL = 'EMAIL',
  ACCESS_LEVEL = 'ACCESS_LEVEL',
  SOURCE = 'SOURCE',
  REVOKE = 'REVOKE'
}

const ID_COL = {
  id: AccessCol.ID, 
  caption: 'ID',
  prop: 'id'
}

const NAME_COL = {
  id: AccessCol.NAME,
  caption: 'tc_name',
  prop: 'name'
}

const EMAIL_COL = {
  id: AccessCol.EMAIL, 
  caption: 'test_auth_email_col',
  prop: 'email'
}

const ACCESS_COL = {
  id: AccessCol.ACCESS_LEVEL,
  caption: 'test_auth_access_level_col',
  prop: 'accessLevel'
}

const SOURCE_COL = {
  id: AccessCol.SOURCE,
  caption: 'Permission Source',
  prop: 'source'
}

const USER_ACCESS_COL = {
  id: AccessCol.USER_ACCESS,
  caption: 'auth_user_access',
}

const REVOKE_COL = {
  id: AccessCol.REVOKE, 
  caption: 'auth_revoke_access'
}

const colPropMap = {};
const renderPropMap = {};


export const renderSource = (source: PermSource, lang: LangService) => {
  switch(source) {
    case PermSource.GROUP:
      return lang.tra('test_auth_authoring_group')
    case PermSource.ASSESSMENT:
      return lang.tra('lbl_assessment')
  }
}
@Component({
  selector: 'manage-access',
  templateUrl: './manage-access.component.html',
  styleUrls: ['./manage-access.component.scss']
})
export class ManageAccessComponent implements OnInit {

  AccessCol = AccessCol;

  constructor(
    private authRoles: AuthRolesService,
    private auth: AuthService,
    private routes: RoutesService,
    public lang: LangService,
    private myItems: ItemMakerService,
    private pageModalService: PageModalService,
    private whitelabel: WhitelabelService
  ) { }

  @Input() authoringGroupId: number
  @Input() singleGroupId: number
  @Input() itemSetId: number
  @Input() itemSet: IItemSetDef
  @Input() groupName: string


  authorsTable : MemDataPaginated<any>;

  public pageSize = 8;

  public authRoleTypes;
  public authRoleCaptionMap;

  // Modal
  pageModal: PageModalController;
  public columns;

  public excludeEmails:{email: string, role: UserRoles}[] = []; //emails disallowed sharing access with
 
  public isSharing:boolean = false;
  PermSource = PermSource;
  public lastGroupIndex; 

  ngOnInit(): void {

    this.authRoleCaptionMap = this.authRoles.getCaptionMap();
    if(this.singleGroupId) {
      this.columns = [ 
        ID_COL, NAME_COL, EMAIL_COL,  ACCESS_COL, SOURCE_COL, USER_ACCESS_COL, REVOKE_COL
      ];
    } else {
      this.columns = [
        ID_COL, NAME_COL, EMAIL_COL,  ACCESS_COL, USER_ACCESS_COL, REVOKE_COL
      ]
    }


    for(const col of this.columns) {
      colPropMap[col.id] = col.prop;
    }

    renderPropMap['source'] = (author) => {
      return this.renderSource(author.source);
    } 
    
    const filterSettings = {
      'accessLevel': (entry, filter) => {
        const renderedVal = this.authRoleCaptionMap[entry.roleTypeFc.value];
        const includes = strIncludes(filter, renderedVal);
        return includes;
      }
    };
    for(const prop of Object.keys(renderPropMap) ) {
      filterSettings[prop] = (entry, filter) => {
        const renderedVal = renderPropMap[prop](entry);
        const includes = strIncludes(filter, renderedVal);
        return includes;
      }
    }

    this.authorsTable = new MemDataPaginated({
      data: [],
      pageSize: this.pageSize,
      filterSettings
    });

    this.authRoleTypes = this.authRoles.getAuthRoles();

    this.ensureSingleGroupId().then( () => {
      this.refreshData();
    })

    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  renderSource(source: PermSource) {
    return renderSource(source, this.lang)
  }

  refreshData() {
    const query = {
      single_group_id: this.singleGroupId,
      group_id: this.authoringGroupId,
      auth_group_ids: this.genAuthGroupIds(),
      getAuthEmails: true
    }
    this.auth.apiFind(this.routes.TEST_AUTH_GROUP_MEMBERS, {query}).then((res: any[]) => {
        const data: IAuthorAccess[] = res.map( (r, index) => {
          const actualRole = this.authRoles.determineRoleFromRoleTypes(r.role_types)
          const fc = new FormControl(actualRole);
          const formlet = new EditSaveFormlet({ roleTypeFc: fc}, () => {
            return this.auth.apiPatch(this.routes.TEST_AUTH_ACCOUNTS_ACCESS, r.id, {
              group_id: this.getEditGroupId(!r.is_single),
              newRole: fc.value
            }, { query:{
              auth_group_ids: this.genAuthGroupIds()
            }})
          });

          if(!r.is_single) {
            if(this.singleGroupId) {
              this.excludeEmails.push({email: r.contact_email, role: actualRole});
            }
            this.lastGroupIndex = index;
          }
          const expires_on_local = r.expires_on ? moment(r.expires_on) : null;

          let name;
          if(r.first_name) {
            name = r.first_name;
            if(r.last_name) {
              name += " " + r.last_name;
            }
          }

          return {
            id: r.id,
            name,
            email: r.contact_email,
            formlet,
            roleTypeFc: fc,
            source: r.is_single ? PermSource.ASSESSMENT : PermSource.GROUP,
            expiresOn: expires_on_local,
            isPending: !r.is_claimed
          }
        })
        this.authorsTable.injestNewData(data);
        if(this.singleGroupId) {
          this.toggleShowGroupRows();
        }
      }
    )
  }
  genAuthGroupIds() {
    return this.singleGroupId ? [this.singleGroupId, this.authoringGroupId] : this.authoringGroupId;
  } 


  ensureSingleGroupId() {
    if(this.singleGroupId === -1) {
      return this.auth.apiCreate(this.routes.TEST_AUTH_SINGLE_GROUP, {item_set_id: this.itemSetId}).then((res)=> {
        this.singleGroupId = res;
        this.itemSet.single_group_id = res;
      })
    } else {
      //Ok if singleGroupId is undefined - we don't use it in that case
      return Promise.resolve();
    }
  }

  getEditGroupId(isGroupEntry: boolean) {
    if( isGroupEntry || !this.singleGroupId) {
      return this.authoringGroupId;
    } else {
      return this.singleGroupId
    }
  }

  revokeAuthGroupAccess(author: IAuthorAccess) {
    const group_id = this.getEditGroupId(author.source === PermSource.GROUP);
    const uid = author.id;
    this.auth.apiRemove(this.routes.TEST_AUTH_ACCOUNTS_ACCESS, uid, {query: {
      group_id,
      auth_group_ids: this.genAuthGroupIds(),
      isAssessment: !!this.singleGroupId
    }}).then( res => {
      this.authorsTable.removeData( entry => entry.id === uid );
      //remove them from the list and refresh
    })
  }

  isEditing(author:IAuthorAccess) {
    return !!author.formlet.buttonCtrl.isEditing;
  }

  renderProperty(author: IAuthorAccess, col: AccessCol) {
    const prop = colPropMap[col];
    if(renderPropMap[prop]) {
      return renderPropMap[prop](author);
    }

    if(!author[prop]) {
      return "";
    }
    return author[prop]; 
  }

  getUserAccessLink(author: IAuthorAccess) {
    return `/en/test-auth/manage-author/${author.id}`
  }

  toggleShowGroupRows() {
    if(!this.authorsTable.activeFilters['source'] ) {
      this.authorsTable.activeFilters['source'] = {
        mode: FilterSettingMode.VALUE,
        config: {
          value: PermSource.ASSESSMENT
        }
      };
    } else {
      this.authorsTable.activeFilters['source'] = undefined;
    }
    this.authorsTable.refreshFilters();
  }

  lastGroupPage() {
    return Math.floor(this.lastGroupIndex / this.pageSize) + 1;
  }

  anyActiveFilters() {
    
    if(!this.authorsTable.activeFilters) {
      return false;
    }

    for(const filterId in this.authorsTable.activeFilters) {
      if(this.authorsTable.activeFilters[filterId]) {
        return true;
      }
    }

    return false;
  }

  areGroupRowsExpanded() {
    return !this.authorsTable.activeFilters['source'];
  }

  showCollapse(rowIndex: number) {
    if(this.anyActiveFilters() || !this.areGroupRowsExpanded()) {
      return false;
    }
    const showNonLastGroupPage = this.authorsTable.getPage() !== this.lastGroupPage() && rowIndex === this.pageSize - 1;
    const currPage = this.authorsTable.getPage();
    const lastGroupPage = this.lastGroupPage();
    const isLastGroupPage =  currPage === lastGroupPage;
    const lastGroupPageIndex = (this.lastGroupIndex % this.pageSize)
    const showLastGroupPage = isLastGroupPage && rowIndex === lastGroupPageIndex;
    return this.singleGroupId && ( showNonLastGroupPage || showLastGroupPage)
  }

  handleDoneCreating() {
    this.isSharing = false;
    this.refreshData();
  }

  isAuthGroupSuper() {
    return this.myItems.getGroupById(this.authoringGroupId).isSuper;
  }

  renderExpiresOn(author: IAuthorAccess) {
    const m:moment = author.expiresOn;
    
    return m.format('M/D/YYYY');
  }

  resendInvitation(author: IAuthorAccess) {
    const params = {
      query:{
        auth_group_ids: this.singleGroupId? [this.singleGroupId, this.authoringGroupId] : this.authoringGroupId
      }
    };
    
    const domain = getFrontendDomain();
    const resendInviteInfo = {
      uid: author.id,
      domain,
      langCode: this.lang.c()
    }

    this.auth.apiCreate(this.routes.TEST_AUTH_ACCOUNTS_RESEND_INVITE, resendInviteInfo, params).then(() => {})
  }

  newExpiration;
  changeExpirationModalStart(author: IAuthorAccess) {
      const config: any = {
        uid: author.id,
        group_id: this.singleGroupId,
        newRole: author.roleTypeFc.value
      }

      this.newExpiration = author.expiresOn.toDate().toISOString().substring(0,10);
  
      this.pageModal.newModal({type: 'EXPIRATION_EXTENSION', config, finish: this.updateExpiration.bind(this)});
  }

  updateExpiration(config) {
    const userUid = config.uid;
    const expires_on_utc = this.newExpiration 
      ? moment(this.newExpiration).utc().format("YYYY-MM-DD HH:mm") 
      : null;

    const data = {
      ...config,
      expires_on: expires_on_utc
    }

    this.auth.apiPatch(this.routes.TEST_AUTH_ACCOUNTS_ACCESS,
      userUid, 
      data, 
      { query:
        {
          auth_group_ids: this.genAuthGroupIds()
        }
    }).then(() => this.refreshData());

    this.pageModal.closeModal();
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
}