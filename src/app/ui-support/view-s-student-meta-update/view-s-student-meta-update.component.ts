import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { LangService } from 'src/app/core/lang.service';
import { apiErrMsgToSlug } from '../../core/util/api-error-messages';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';
import { Router, ActivatedRoute } from '@angular/router';

// Type definitions for better type safety
interface IStudent {
  uid: string;
  firstName: string;
  lastName: string;
  oen?: string;
  testWindows: ITestWindow[];
}

interface ITestWindow {
  id: number;
  name: string;
}

interface IMetaData {
  id: number;
  key: string;
  value: any;
  label?: string;
  key_namespace?: string;
  isVirtual?: boolean; // Flag to identify virtual entries for user table fields
}

interface IMetaChange extends IMetaData {
  from_value: any;
}

interface ISnapshotData {
  snapshotId: string;
  ssaisId: string;
  isSafeToUpdate: boolean;
  message: string;
  studentInfoJson: {
    user_metas?: IMetaData[];
  };
}

interface ISearchQuery {
  oen?: string;
  studentUid?: string;
}

interface IUpdatePayload {
  studentUid: string;
  snapshotIdToRevoke: string;
  ssaisId: string;
  studentInfoJson: any;
  updates: IMetaChange[];
  password?: string;
}

@Component({
  selector: 'app-view-s-student-meta-update',
  templateUrl: './view-s-student-meta-update.component.html',
  styleUrls: ['./view-s-student-meta-update.component.scss']
})
export class ViewSStudentMetaUpdateComponent implements OnInit {
  // Search inputs
  oen: string;
  uid: string;

  // Main data
  student: IStudent | null = null;
  testWindows: ITestWindow[] = [];
  selectedTestWindowId: number | null = null;
  snapshotData: ISnapshotData | null = null;
  
  // Meta editing
  availableMetas: IMetaData[] = [];
  selectedMetaId: number | null = null;
  currentMetaValue: any = null;
  newMetaValue: any = null;
  pendingChanges: IMetaChange[] = [];

  // UI state
  isLoading = false;
  isLoadingSnapshot = false;
  isLoadingUpdate = false;
  isLoadingValidation = false;
  error: string | null = null;

  // Validation results
  validationErrors: string[] = [];
  validationWarnings: string[] = [];
  hasValidationBeenPerformed = false; // Track if validation has been performed

  // Current stored validation errors
  currentStoredErrors: {
    namespace: string;
    userMetasErrors: string[];
    snapshotErrors: string[];
    hasStoredErrors: boolean;
  } | null = null;

  // Validation application controls
  applyValidationOnUpdate = true; // Checked by default
  isLoadingApplyValidation = false;
  lastValidationType: 'changes' | 'snapshot' | null = null; // Track what type of validation was last performed

  // Checkbox controls for where to apply validation results
  applyToUserMetas = true; // Checked by default
  applyToSnapshot = true; // Checked by default

  public breadcrumb: IBreadcrumbRoute[];

  constructor(
    private auth: AuthService,
    public lang: LangService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.breadcrumb = [
      this.breadcrumbsService.SUPPORT_DASHBOARD(),
      this.breadcrumbsService._CURRENT('Student Meta Update', this.router.url),
    ];

    // Check for UID in query parameters and load student if present
    this.route.queryParams.subscribe(params => {
      if (params['uid']) {
        this.uid = params['uid'];
        this.performStudentSearch({ studentUid: this.uid });
      }
    });
  }

  /**
   * Gets the snapshot data for JSON viewer, ensuring virtual entries are never shown
   */
  get snapshotDataForViewer(): any {
    if (!this.snapshotData) {
      return null;
    }

    // Create a clean copy without any potential virtual entries
    const cleanSnapshot = JSON.parse(JSON.stringify(this.snapshotData.studentInfoJson));

    // Ensure user_metas only contains real entries (positive IDs)
    if (cleanSnapshot.user_metas) {
      cleanSnapshot.user_metas = cleanSnapshot.user_metas.filter((meta: any) => meta.id > 0);
    }

    return cleanSnapshot;
  }

  /**
   * Searches for a student using the provided query parameters
   */
  performStudentSearch(query: ISearchQuery): void {
    this.isLoading = true;
    this.error = null;
    this.student = null;
    this.resetSnapshotData();

    this.auth.apiFind('public/support/student-meta-update', { query }).then(
      (data: IStudent) => {
        this.student = data;
        this.testWindows = data.testWindows;
        this.isLoading = false;

        // Update URL query parameters to remember the student UID
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: { uid: data.uid },
          queryParamsHandling: 'merge'
        });
      }
    ).catch(
      err => {
        this.error = 'Error finding student. ' + (err.error?.message || err.message);
        this.isLoading = false;

        // Clear UID from URL if search fails
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: { uid: null },
          queryParamsHandling: 'merge'
        });
      }
    );
  }

  /**
   * Loads assessment snapshot for the selected student and test window
   */
  loadSnapshot(): void {
    if (!this.student?.uid || !this.selectedTestWindowId) {
      this.error = 'Student and test window must be selected';
      return;
    }

    this.isLoadingSnapshot = true;
    this.error = null;
    this.snapshotData = null;
    this.resetMetaData();

    const query = {
      studentUid: this.student.uid,
      testWindowId: this.selectedTestWindowId.toString()
    };

    this.auth.apiFind('public/support/student-meta-update', { query }).then(
      (data: ISnapshotData) => {
        this.snapshotData = data;
        // Ensure we only get real user_metas (positive IDs) from the snapshot
        const realUserMetas = (data.studentInfoJson?.user_metas || []).filter((meta: any) => meta.id > 0);
        this.availableMetas = realUserMetas;
        this.augmentMetasWithUserFields();
        this.isLoadingSnapshot = false;
      }
    ).catch(
      err => {
        this.error = 'Error loading snapshot. ' + (err.error?.message || err.message);
        this.isLoadingSnapshot = false;
      }
    );
  }
  
  /**
   * Handles meta selection change and updates current/new values
   */
  onMetaSelectionChange(): void {
    if (!this.selectedMetaId) {
      this.resetMetaValues();
      return;
    }

    const selectedMeta = this.findMetaById(this.selectedMetaId);
    if (selectedMeta) {
      this.currentMetaValue = selectedMeta.value;
      this.newMetaValue = selectedMeta.value;
    }
  }

  /**
   * Checks if the current meta value has been modified
   */
  isValueChanged(): boolean {
    return this.currentMetaValue !== this.newMetaValue;
  }

  /**
   * Adds the current meta change to pending changes
   */
  addChange(): void {
    if (!this.isValueChanged() || !this.selectedMetaId) {
      return;
    }

    const selectedMeta = this.findMetaById(this.selectedMetaId);
    if (!selectedMeta) {
      return;
    }

    // Replace existing change for the same meta if it exists
    this.pendingChanges = this.pendingChanges.filter(change => change.id !== selectedMeta.id);

    this.pendingChanges.push({
      ...selectedMeta,
      key_namespace: selectedMeta.key_namespace || 'eqao_sdc', // Ensure key_namespace is set
      from_value: this.currentMetaValue,
      value: this.newMetaValue,
    });

    // Clear validation results when changes are made
    this.clearValidationResults();
    this.resetMetaSelection();
  }

  /**
   * Removes a pending change by meta ID
   */
  removeChange(metaId: number): void {
    this.pendingChanges = this.pendingChanges.filter(change => change.id !== metaId);
    // Clear validation results when changes are made
    this.clearValidationResults();
  }

  /**
   * Clears validation results
   */
  private clearValidationResults(): void {
    this.validationErrors = [];
    this.validationWarnings = [];
    this.hasValidationBeenPerformed = false;
    this.lastValidationType = null;
    this.currentStoredErrors = null;
  }

  /**
   * Converts API error messages to translation slugs using shared utility
   */
  public apiErrMsgToSlug(errMsg: string): string {
    return apiErrMsgToSlug(errMsg, this.lang);
  }


  /**
   * Validates pending changes using red flag validation
   */
  validateChanges(): void {
    if (!this.student || !this.selectedTestWindowId) {
      this.error = 'Student and test window are required for validation';
      return;
    }

    this.isLoadingValidation = true;
    this.error = null;
    this.validationErrors = [];
    this.validationWarnings = [];
    this.lastValidationType = 'changes';

    const validationPayload = {
      studentUid: this.student.uid,
      testWindowId: this.selectedTestWindowId.toString(),
      updates: this.pendingChanges,
      isDryRun: true
    };

    this.auth.apiPatch('public/support/student-meta-update', 'validate', validationPayload).then((response: any) => {
      this.validationErrors = response.ErrorMessages || [];
      this.validationWarnings = response.warningMessages || [];
      this.currentStoredErrors = response.currentStoredErrors || null;
      this.hasValidationBeenPerformed = true;
      this.isLoadingValidation = false;
    }).catch(err => {
      this.error = 'Error during validation. ' + (err.error?.message || err.message);
      this.isLoadingValidation = false;
    });
  }



  /**
   * Validates the snapshot data without any pending changes
   */
  validateSnapshot(): void {
    if (!this.student || !this.selectedTestWindowId || !this.snapshotData) {
      this.error = 'Student, test window, and snapshot data are required';
      return;
    }

    this.isLoadingValidation = true;
    this.error = null;
    this.lastValidationType = 'snapshot';

    const validationPayload = {
      studentUid: this.student.uid,
      testWindowId: this.selectedTestWindowId.toString(),
      updates: [], // Empty updates array to validate snapshot data as-is
      isDryRun: true
    };

    this.auth.apiPatch('public/support/student-meta-update', 'validate', validationPayload).then((response: any) => {
      this.validationErrors = response.ErrorMessages || [];
      this.validationWarnings = response.warningMessages || [];
      this.currentStoredErrors = response.currentStoredErrors || null;
      this.hasValidationBeenPerformed = true;
      this.isLoadingValidation = false;
    }).catch(err => {
      this.error = 'Error during validation. ' + (err.error?.message || err.message);
      this.isLoadingValidation = false;
    });
  }

  /**
   * Applies validation results to user_metas and/or snapshot based on checkbox selections
   */
  applyValidationResults(): void {
    if (!this.student || !this.selectedTestWindowId) {
      this.error = 'Student and test window are required';
      return;
    }

    if (!this.applyToUserMetas && !this.applyToSnapshot) {
      this.error = 'Please select at least one target (User Metas or Snapshot) to apply validation results';
      return;
    }

    const errorCount = this.validationErrors.length;
    const targets = [];
    if (this.applyToUserMetas) targets.push('user_metas table');
    if (this.applyToSnapshot) targets.push('snapshot JSON');

    const confirmMessage = `This will apply validation results to: ${targets.join(' and ')}. Continue?`;

    if (!confirm(confirmMessage)) {
      return;
    }

    this.isLoadingApplyValidation = true;
    this.error = null;

    // Single API call with both targets specified
    const applyPayload = {
      studentUid: this.student.uid,
      testWindowId: this.selectedTestWindowId.toString(),
      updates: this.lastValidationType === 'changes' ? this.pendingChanges : [],
      applyToUserMetas: this.applyToUserMetas,
      applyToSnapshot: this.applyToSnapshot,
      isDryRun: false
    };

    this.auth.apiPatch('public/support/student-meta-update', 'apply-validation', applyPayload).then((response: any) => {
      this.isLoadingApplyValidation = false;

      // Show success message based on what was actually applied
      const appliedTargets = [];
      if (response.appliedToUserMetas) appliedTargets.push('user_metas table');
      if (response.appliedToSnapshot) appliedTargets.push('snapshot JSON');

      const resultMessage = errorCount > 0
        ? `Successfully applied ${errorCount} validation errors to: ${appliedTargets.join(' and ')}`
        : `Successfully applied validation results to: ${appliedTargets.join(' and ')}`;
      alert(resultMessage);

      // If applied to snapshot, reload the snapshot to show the changes
      if (this.applyToSnapshot && response.appliedToSnapshot) {
        this.loadSnapshot();
      }

      // Re-fetch validation results to show the updated state
      this.reValidateAfterApply();
    }).catch(err => {
      this.error = 'Error applying validation results. ' + (err.error?.message || err.message);
      this.isLoadingApplyValidation = false;
    });
  }

  /**
   * Re-validates after applying validation results to show updated state
   */
  private reValidateAfterApply(): void {
    if (this.lastValidationType === 'changes') {
      // Re-validate changes
      this.validateChanges();
    } else if (this.lastValidationType === 'snapshot') {
      // Re-validate snapshot
      this.validateSnapshot();
    }
  }

  /**
   * Initiates meta update with user confirmation
   */
  submitMetaUpdate(): void {
    if (!this.student || this.pendingChanges.length === 0) {
      return;
    }

    const confirmMessage = `Are you sure you want to apply ${this.pendingChanges.length} change(s) for ${this.student.firstName} ${this.student.lastName}?`;
    
    if (confirm(confirmMessage)) {
      const password = prompt('Please enter the password to proceed:');

      if (password) {
        this.executeMetaUpdate(password);
      } else {
        alert('Password is required. The update has been cancelled.');
      }
    }
  }

  /**
   * Executes the meta update API call
   */
  private executeMetaUpdate(password: string): void {
    if (!this.student || !this.snapshotData) {
      this.error = 'Missing required data for update';
      return;
    }

    this.isLoadingUpdate = true;
    this.error = null;

    const payload: IUpdatePayload = {
      studentUid: this.student.uid,
      snapshotIdToRevoke: this.snapshotData.snapshotId,
      ssaisId: this.snapshotData.ssaisId,
      studentInfoJson: this.snapshotData.studentInfoJson,
      updates: this.pendingChanges,
      password: password,
    };

    this.auth.apiCreate('public/support/student-meta-update', payload).then((response: any) => {
      this.isLoadingUpdate = false;
      alert(response.message || 'Meta updated successfully!');

      // Apply validation results if checkbox is checked and there are validation errors
      if (this.applyValidationOnUpdate && this.validationErrors.length > 0) {
        this.applyValidationResults();
      }

      this.refreshStudentData();
    }).catch(err => {
      this.error = 'Error updating meta. ' + (err.error?.message || err.message);
      this.isLoadingUpdate = false;
    });
  }

  /**
   * Finds a meta object by its ID
   */
  private findMetaById(id: number): IMetaData | undefined {
    return this.availableMetas.find(meta => meta.id === id);
  }

  /**
   * Refreshes student data after successful update
   */
  private refreshStudentData(): void {
    if (this.oen) {
      this.performStudentSearch({ oen: this.oen });
    } else if (this.uid) {
      this.performStudentSearch({ studentUid: this.uid });
    }
  }

  /**
   * Resets all meta-related data
   */
  private resetMetaData(): void {
    this.availableMetas = [];
    this.pendingChanges = [];
    this.clearValidationResults();
    this.resetMetaSelection();
  }

  /**
   * Resets meta selection and values
   */
  private resetMetaSelection(): void {
    this.selectedMetaId = null;
    this.resetMetaValues();
  }

  /**
   * Resets current and new meta values
   */
  private resetMetaValues(): void {
    this.currentMetaValue = null;
    this.newMetaValue = null;
  }

  /**
   * Resets snapshot-related data
   */
  private resetSnapshotData(): void {
    this.snapshotData = null;
    this.selectedTestWindowId = null;
    this.resetMetaData();
  }

  /**
   * Augments the available metas with virtual entries for user table fields
   * that might not exist in user_metas but can still be edited
   */
  private augmentMetasWithUserFields(): void {
    if (!this.student || !this.snapshotData) {
      return;
    }

    const userFields = [
      { key: 'FirstName', currentValue: this.student.firstName },
      { key: 'LastName', currentValue: this.student.lastName }
    ];

    userFields.forEach(field => {
      // Check if this field already exists in user_metas
      const existingMeta = this.availableMetas.find(meta =>
        meta.key === field.key && meta.key_namespace === 'eqao_sdc'
      );

      if (!existingMeta) {
        // Create a virtual meta entry with a negative ID to distinguish from real ones
        const virtualId = -(this.availableMetas.length + userFields.indexOf(field) + 1);
        this.availableMetas.push({
          id: virtualId,
          key: field.key,
          key_namespace: 'eqao_sdc',
          value: field.currentValue,
          isVirtual: true
        });
      }
    });

    // Sort to put virtual entries at the top for easy access
    this.availableMetas.sort((a, b) => {
      if (a.isVirtual && !b.isVirtual) return -1;
      if (!a.isVirtual && b.isVirtual) return 1;
      return 0;
    });
  }
}