<header [breadcrumbPath]="breadcrumb"></header>

<div class="page-body">
  <div class="page-content is-fullpage" style="padding: 2em">
    <h1 class="title">Student Meta Update</h1>

    <div class="box">
      <div class="content">
        <p>This process is for changing a student's metadata after ISRs have been released and requires sign-off from the EQAO data team.</p>
        <p>
          <strong>Note:</strong> This process is required after Test Taker Registration Information (TTRI) is locked down (typically at the end of the admin window), and the ISRs
          are released.
        </p>
      </div>
      
        <div class="notification is-info">
          <h4 class="subtitle is-6">📋 Instructions</h4>
          <ul>
            <li>Ensure you have proper authorizations before proceeding</li>
            <li>Find the student by OEN or UID</li>
            <li>Double-check the student's information to avoid updating the wrong student's data</li>
            <li>Select the appropriate test window for the changes you need to make (most likely should be made to every test window)</li>
            <li>Load the snapshot to see current metadata and check if updates are safe</li>
            <li>Select a field to change, enter the new value, and add it to the queue</li>
            <li>Use the "Validate Changes" button to check for red flag violations</li>
            <li>Review all pending changes and validation results carefully before submitting</li>
            <li>Submit all changes - this will apply them in a single transaction</li>
            <li>Apply changes to all test windows and validate the data</li>
          </ul>
        </div>
        
        <div class="notification is-warning">
          <h4 class="subtitle is-6">⚠️ Important Warnings</h4>
          <ul>
            <li><strong>This process makes permanent changes to student data in the TTRI snapshot and database</strong></li>
            <li><strong>Use the "Validate Changes" button to check for red flag violations before submitting</strong></li>
            <li>The tool makes raw modifications to the student's metadata in the TTRI snapshot that can be dangerous if not used correctly</li>
            <li>Ensure you understand the implications of the changes you are making</li>
          </ul>
        </div>

            <div class="columns">
        <div class="column is-half">
          <label class="label">Student OEN</label>
          <div class="field has-addons">
            <div class="control is-expanded has-icons-left">
              <input class="input" type="text" placeholder="Enter 9-digit OEN (e.g., 123456789)" [(ngModel)]="oen" />
              <span class="icon is-small is-left">
                <i class="fas fa-id-card"></i>
              </span>
            </div>
            <div class="control">
              <button class="button is-info" (click)="performStudentSearch({ oen: oen })" [class.is-loading]="isLoading" title="Find Student by OEN">
                <span class="icon">
                  <i class="fas fa-search"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
        <div class="column is-half">
          <label class="label">Student UID</label>
          <div class="field has-addons">
            <div class="control is-expanded has-icons-left">
              <input class="input" type="number" placeholder="Enter numeric UID (e.g., 12345)" [(ngModel)]="uid" />
              <span class="icon is-small is-left">
                <i class="fas fa-user"></i>
              </span>
            </div>
            <div class="control">
              <button class="button is-info" (click)="performStudentSearch({ studentUid: uid })" [class.is-loading]="isLoading" title="Find Student by UID">
                <span class="icon">
                  <i class="fas fa-search"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="error" class="notification is-danger">
      <button class="delete" (click)="error = null"></button>
      {{ error }}
    </div>

    <div *ngIf="student" class="box">
      <h2 class="subtitle">Student Information</h2>      

      <div class="box has-background-light">
        <p><strong>UID:</strong> {{ student.uid }}</p>
        <p><strong>Name:</strong> {{ student.firstName }} {{ student.lastName }}</p>
        <p><strong>OEN:</strong> {{ student.oen || 'N/A' }}</p>
      </div>

      <div class="field">
        <label class="label">Test Window</label>
        <div class="control">
          <div class="select">
            <select [(ngModel)]="selectedTestWindowId">
              <option *ngFor="let tw of testWindows" [value]="tw.id">{{ tw.id }} - {{ tw.name }}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="field is-grouped">
        <div class="control">
          <button class="button is-info" (click)="loadSnapshot()" [disabled]="!selectedTestWindowId" [class.is-loading]="isLoadingSnapshot">
            <span class="icon"><i class="fas fa-download"></i></span>
            <span>Load Snapshot</span>
          </button>
        </div>
        <div class="control" *ngIf="snapshotData">
          <button class="button is-warning" (click)="validateSnapshot()" [class.is-loading]="isLoadingValidation">
            <span class="icon"><i class="fas fa-check-circle"></i></span>
            <span>Validate Snapshot</span>
          </button>
        </div>
      </div>
    </div>

    <div *ngIf="snapshotData" class="box">
      <h2 class="subtitle">Snapshot Details</h2>
      
      <div class="notification" [ngClass]="{ 'is-success': snapshotData.isSafeToUpdate, 'is-warning': !snapshotData.isSafeToUpdate }">
        {{ snapshotData.message }}
      </div>

      <h3 class="subtitle is-5">Student Info JSON</h3>
      <ngx-json-viewer [json]="snapshotDataForViewer" [expanded]="false"></ngx-json-viewer>

      <div style="margin-top: 2rem">
        <h2 class="subtitle">Update Student Meta</h2>
        
        <div class="notification is-info">
          <h4 class="subtitle is-6">📋 How to Update Metadata</h4>
          <ol>
            <li><strong>Select a field</strong> from the dropdown below</li>
            <li><strong>Review the current value</strong> to ensure you're changing the right field</li>
            <li><strong>Enter the new value</strong> in the correct format</li>
            <li><strong>Add to queue</strong> - you can queue multiple changes</li>
            <li><strong>Validate changes</strong> - use the "Validate Changes" button to check for red flag violations</li>
            <li><strong>Review all pending changes and validation results</strong> carefully before submitting</li>
            <li><strong>Submit all changes</strong> - this will apply them in a single transaction</li>
          </ol>
          <p class="mt-3"><strong>Note:</strong> Fields marked as "(User Field)" are core student information (like FirstName, LastName) that may not exist in user_metas but can still be edited. These changes will update both the users table and create/update corresponding user_metas entries.</p>
          <p class="mt-2"><strong>Apply Validation Results:</strong> Use the checkbox to automatically apply validation results upon updating, or use the manual apply button in the validation results card. Current data validation applies to user_metas only, while changes validation applies to snapshot JSON.</p>
        </div>

        <p class="content">
          Select a field to change, enter the new value, and add it to the queue. You can queue multiple changes before submitting.
        </p>

        <div class="field">
          <label class="label">Select Meta Field</label>
          <div class="control">
            <div class="select">
              <select [(ngModel)]="selectedMetaId" (ngModelChange)="onMetaSelectionChange()">
                <option [ngValue]="null" disabled>-- Select a field --</option>
                <option *ngFor="let meta of availableMetas" [ngValue]="meta.id">
                  {{ meta.key }} - {{ meta.key_namespace }}{{ meta.isVirtual ? ' (User Field)' : '' }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div *ngIf="selectedMetaId">
          <div class="field">
            <label class="label">Current Value</label>
            <div class="control">
              <input class="input" type="text" [(ngModel)]="currentMetaValue" readonly />
            </div>
          </div>

          <div class="field">
            <label class="label">New Value</label>
            <div class="control">
              <input class="input" type="text" [(ngModel)]="newMetaValue" placeholder="Enter the new value (verify format carefully)" />
            </div>
          </div>
          <div class="field">
            <div class="control">
              <button class="button is-link" (click)="addChange()" [disabled]="!isValueChanged()">
                <span class="icon"><i class="fas fa-plus"></i></span>
                <span>Add Change to Queue</span>
              </button>
            </div>
          </div>
        </div>

        <div *ngIf="pendingChanges.length > 0" style="margin-top: 2rem">
          <h3 class="subtitle is-5">Pending Changes</h3>
          <p class="content">
            The following changes will be applied in a single transaction. Review them before submitting.
          </p>
          <table class="table is-striped is-fullwidth">
            <thead>
              <tr>
                <th>Field</th>
                <th>From</th>
                <th>To</th>
                <th style="width: 5%"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let change of pendingChanges">
                <td>
                  {{ change.key }} - {{ change.key_namespace }}
                  <span *ngIf="change.isVirtual" class="tag is-info is-small ml-1">User Field</span>
                </td>
                <td>
                  <span class="has-text-danger">{{ change.from_value | json }}</span>
                </td>
                <td>
                  <span class="has-text-success">{{ change.value | json }}</span>
                </td>
                <td>
                  <button class="delete" (click)="removeChange(change.id)" title="Remove this change"></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div *ngIf="pendingChanges.length > 0" style="margin-top: 2rem">
          <div class="field">
            <label class="checkbox">
              <input type="checkbox" [(ngModel)]="applyValidationOnUpdate">
              Apply red flag validation results upon updating (recommended)
            </label>
            <p class="help">When checked, validation errors will be automatically applied to the student's record after successful updates.</p>
          </div>

          <div class="field is-grouped">
            <div class="control">
              <button class="button is-warning" (click)="validateChanges()" [class.is-loading]="isLoadingValidation">
                <span class="icon"><i class="fas fa-check-circle"></i></span>
                <span>Validate Changes</span>
              </button>
            </div>
            <div class="control">
              <button class="button is-danger" (click)="submitMetaUpdate()" [class.is-loading]="isLoadingUpdate">
                <span class="icon"><i class="fas fa-paper-plane"></i></span>
                <span>Submit All Changes</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Validation Results Card -->
    <div *ngIf="hasValidationBeenPerformed" class="box">
      <h2 class="subtitle">Validation Results</h2>

      <!-- Current Stored Errors -->
      <div *ngIf="currentStoredErrors" style="margin-bottom: 2rem;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
          <h3 class="subtitle is-5" style="margin-bottom: 0;">📋 Current Stored Validation Errors</h3>
          <span class="tag is-info">{{ currentStoredErrors.namespace }}</span>
        </div>

        <!-- User Metas Table -->
        <div style="margin-bottom: 1.5rem;">
          <div style="margin-bottom: 0.75rem;">
            <span class="icon">
              <i class="fas fa-database"></i>
            </span>&nbsp;<span class="has-text-weight-semibold">User Metas Table</span>
            <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
              <span class="tag" [ngClass]="currentStoredErrors.userMetasErrors.length > 0 ? 'is-danger' : 'is-success'">
                {{ currentStoredErrors.userMetasErrors.length > 0 ? 'Errors' : 'Passed' }}
              </span>
              <span class="tag is-light">
                {{ currentStoredErrors.userMetasErrors.length }}
              </span>
            </span>
          </div>

          <div *ngIf="currentStoredErrors.userMetasErrors.length > 0">
            <ul style="list-style: none; padding: 0; margin: 0;">
              <li *ngFor="let error of currentStoredErrors.userMetasErrors" class="validation-error-border" style="margin-bottom: 0.5rem; padding: 0.5rem;">
                <code class="is-small">{{ error }}</code> - <tra [slug]="apiErrMsgToSlug(error)"></tra>
              </li>
            </ul>
          </div>
        </div>

        <!-- Snapshot JSON -->
        <div style="margin-bottom: 1.5rem;">
          <div style="margin-bottom: 0.75rem;">
            <span class="icon">
              <i class="fas fa-camera"></i>
            </span>&nbsp;<span class="has-text-weight-semibold">Snapshot JSON</span>
            <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
              <span class="tag" [ngClass]="currentStoredErrors.snapshotErrors.length > 0 ? 'is-danger' : 'is-success'">
                {{ currentStoredErrors.snapshotErrors.length > 0 ? 'Errors' : 'Passed' }}
              </span>
              <span class="tag is-light">
                {{ currentStoredErrors.snapshotErrors.length }}
              </span>
            </span>
          </div>

          <div *ngIf="currentStoredErrors.snapshotErrors.length > 0">
            <ul style="list-style: none; padding: 0; margin: 0;">
              <li *ngFor="let error of currentStoredErrors.snapshotErrors" class="validation-error-border" style="margin-bottom: 0.5rem; padding: 0.5rem;">
                <code class="is-small">{{ error }}</code> - <tra [slug]="apiErrMsgToSlug(error)"></tra>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- New Validation Results -->
      <div style="margin-bottom: 2rem;">
        <div style="margin-bottom: 1.5rem;">
          <span class="icon">
            <i class="fas fa-search"></i>
          </span>&nbsp;<h3 class="subtitle is-5" style="margin-bottom: 0; display: inline;">New Validation Results</h3>
          <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
            <span class="tag" [ngClass]="(validationErrors.length > 0 || validationWarnings.length > 0) ? 'is-danger' : 'is-success'">
              {{ (validationErrors.length > 0 || validationWarnings.length > 0) ? 'Errors' : 'Passed' }}
            </span>
            <span class="tag is-light">
              {{ validationErrors.length + validationWarnings.length }}
            </span>
          </span>
        </div>

        <!-- Validation Errors -->
        <div *ngIf="validationErrors.length > 0" style="margin-bottom: 1.5rem;">
          <div style="margin-bottom: 0.75rem;">
            <span class="icon has-text-danger">
              <i class="fas fa-times-circle"></i>
            </span>&nbsp;<span class="has-text-weight-semibold has-text-danger">Validation Errors</span>
            <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
              <span class="tag is-danger">Errors</span>
              <span class="tag is-light">{{ validationErrors.length }}</span>
            </span>
          </div>

          <ul style="list-style: none; padding: 0; margin: 0;">
            <li *ngFor="let error of validationErrors" class="validation-error-border" style="margin-bottom: 0.5rem; padding: 0.5rem;">
              <code class="is-small">{{ error }}</code> - <tra [slug]="apiErrMsgToSlug(error)"></tra>
            </li>
          </ul>
        </div>

        <!-- Validation Warnings -->
        <div *ngIf="validationWarnings.length > 0" style="margin-bottom: 1.5rem;">
          <div style="margin-bottom: 0.75rem;">
            <span class="icon has-text-warning">
              <i class="fas fa-exclamation-triangle"></i>
            </span>&nbsp;<span class="has-text-weight-semibold has-text-warning">Validation Warnings</span>
            <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
              <span class="tag is-warning">Warnings</span>
              <span class="tag is-light">{{ validationWarnings.length }}</span>
            </span>
          </div>

          <ul style="list-style: none; padding: 0; margin: 0;">
            <li *ngFor="let warning of validationWarnings" class="validation-error-border" style="margin-bottom: 0.5rem; padding: 0.5rem;">
              <code class="is-small">{{ warning }}</code> - <tra [slug]="apiErrMsgToSlug(warning)"></tra>
            </li>
          </ul>
        </div>

        <!-- Success Message -->
        <div *ngIf="validationErrors.length === 0 && validationWarnings.length === 0" style="margin-bottom: 0.75rem;">
          <span class="has-text-weight-semibold">No validation failures detected</span>
          <span class="tags has-addons" style="margin-left: 0.5rem; display: inline-flex;">
            <span class="tag is-success">Passed</span>
            <span class="tag is-light">0</span>
          </span>
        </div>

        <!-- Apply Validation Results Controls -->
        <div *ngIf="hasValidationBeenPerformed" style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #dbdbdb;">
          <h4 class="subtitle is-6" style="margin-bottom: 1rem;">🔧 Apply Validation Results</h4>

          <div style="margin-bottom: 1rem;">
            <div class="field">
              <label class="checkbox">
                <input type="checkbox" [(ngModel)]="applyToUserMetas">
                &nbsp;Apply to User Metas Table
              </label>
              <p class="help is-size-7">Records errMsg in the live student data (user_metas table)</p>
            </div>

            <div class="field">
              <label class="checkbox">
                <input type="checkbox" [(ngModel)]="applyToSnapshot">
                &nbsp;Apply to Snapshot JSON
              </label>
              <p class="help is-size-7">Records errMsg in the snapshot data (creates new snapshot)</p>
            </div>
          </div>

          <div class="field">
            <button
              class="button is-warning"
              (click)="applyValidationResults()"
              [class.is-loading]="isLoadingApplyValidation"
              [disabled]="!applyToUserMetas && !applyToSnapshot">
              <span class="icon"><i class="fas fa-save"></i></span>
              <span>Apply Results</span>
            </button>
            <p class="help is-size-7" *ngIf="!applyToUserMetas && !applyToSnapshot">
              Select at least one target to apply validation results.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>