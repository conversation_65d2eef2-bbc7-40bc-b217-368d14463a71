export const replaceS3Domains = (url: string): string => {
    if (!url || typeof url !== "string") {
        return url
    }

    const domainsList = {
        'authoring.mathproficiencytest.ca': 'authoring',
        'd3azfb2wuqle4e.cloudfront.net': 'authoring',
        'storage.mathproficiencytest.ca': 'storage',
        'd3c1mbjtikq6ue.cloudfront.net': 'storage'
    }
    const replacementDomain = 'https://eqao.vretta.com';
    
    let newUrl = url;
    
    Object.keys(domainsList).forEach(domain => {
        if(url.includes(domain)){
            const urlSegArr = url.split('/')
            const subdirectoryIdx = urlSegArr.findIndex(seg => seg.includes(domain)) + 1
            if(subdirectoryIdx){
                newUrl = [replacementDomain, domainsList[domain]].concat(urlSegArr.slice(subdirectoryIdx)).join('/')
            }
        }
    });

    return newUrl;
}

/**
 * Convert an array of header (index 0) and values (rest of indexes) to an array of key value objects.
 * [["connection_id", "uid"], ['IxdRufxvYosCGVw=', 5197203]] -> [{connection_id: "IxdRufxvYosCGVw=", uid: 5197203}]
 * @param {array} data 
 * @returns {array} array of key value object(s)
 */
export const convertHeaderArrayIntoObjects = (data: any[][]):any[] => {
    if (!Array.isArray(data[0])) { // object or undefined return false
        return data
    }
    const [headers, ...datas] = data;
    const dataObj = datas.map(row => {
        const obj = {}
        headers.forEach((key, index) => {
            obj[key] = row[index]
        })
        return obj
    })
    return dataObj
}