import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ViewAltVersionReqShippingVendorDashboardComponent } from './view-alt-version-req-shipping-vendor-dashboard/view-alt-version-req-shipping-vendor-dashboard.component';
import { Routes } from '@angular/router';
import { RouterModule } from '@angular/router';
import { DataGuardService } from '../core/data-guard.service';

const routes: Routes = [
  { path: '', component: ViewAltVersionReqShippingVendorDashboardComponent, canDeactivate: [DataGuardService] },
  { path: `dashboard`, component: ViewAltVersionReqShippingVendorDashboardComponent , canDeactivate: [DataGuardService]},
];


@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class UiAltVersionReqShippingVendorRoutingModule { }
