import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ColumnApi, GridApi } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {Modal, ACTIONS, ALT_VERSION_REQUEST_STATUS, REQUEST_STATUS_SLUGS, SchoolColumns, ShippingColumns, RequestColumns} from 'src/app/ui-alt-version-ctrl/alt-version-ctrl-req-table/types'
import moment from 'moment';

@Component({
  selector: 'view-alt-version-req-shipping-vendor-dashboard',
  templateUrl: './view-alt-version-req-shipping-vendor-dashboard.component.html',
  styleUrls: ['./view-alt-version-req-shipping-vendor-dashboard.component.scss']
})
export class ViewAltVersionReqShippingVendorDashboardComponent implements OnInit {

  constructor( private breadcrumbsService:BreadcrumbsService,
    private router:Router,
    private route:ActivatedRoute,
    private routes:RoutesService,
    private auth:AuthService,
    private pageModalService: PageModalService,
    public lang: LangService,
    ) { }

  public breadcrumb = [];

  private routeSub:Subscription;

  // Request ag-grid information
  requestGridColumnApi: ColumnApi;
  requestGridApi: GridApi;
  requestRows=[]
  columnDefs = [
    {width:50, checkboxSelection:true },
    { headerName:'ID', field:'id', width:75},
    { headerName: this.lang.tra('request_date_alt_version'), field:'created_on', width:180, cellRenderer: this.parseDate.bind(this)},
    { headerName: this.lang.tra('request_status_alt_version'), field:'status', width:175, valueGetter: this.getRequestSlug.bind(this)},
    { headerName: this.lang.tra('assessment_name_alt_version'), field:'assessment_name', width:200, cellRenderer: function(params){
      const names = JSON.parse(params.value);
      return `${names.en}/${names.fr}`;
    }},
    { headerName: this.lang.tra('op_admin_date_alt_version'), field:'op_administration_date', width:225, cellRenderer: function(params){
      if(!params.value) {
        return null;
      }
      const current = new Date(Date.parse(params.value));
      const parsedDate = current.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric'});
      const parsedTime = current.toLocaleTimeString('default');
      const parsedFull = `${parsedDate} ${parsedTime}`;

      return Date.parse(params.value) < Date.now() ?  (
        parsedFull
      ) :
      `<div style="color: red;">${parsedFull}</div>`
    }},
    { headerName: this.lang.tra('tent_assessment_date_alt_version'), field:'tent_session_date', width:175, cellRenderer: function(params){
      if(!params.value) {
        return null;
      }
      return moment(params.value, "YYYY-MM-DD").format("MM/DD/YYYY");
    }},
    { headerName: this.lang.tra('test_window_alt_version'), field:'test_window_id', width:175, },
    { headerName: this.lang.tra('language_alt_version'), field:'lang', width:175, },
    { headerName: this.lang.tra('req_formats_alt_version'), field:'requested_formats', width:400, cellRenderer: function(params){
      // Translate the list of slugs based on the language of the request
      return params.value.split(',').map(slug => this.lang.tra(slug, params.data.lang)).join(', ')
    }.bind(this)},
    { headerName: this.lang.tra('reason_alt_version'), field:'reason', width:175, },
    { headerName: this.lang.tra('date_approved_alt_version'), field:'approved_on', width:180, cellRenderer: this.parseDate.bind(this)},
    
    { headerName: this.lang.tra('approved_by_alt_version'), field:'approved_by_email', width:175, },

    //Sample Link
    { headerName: this.lang.tra('sample_links_alt_version'), field: 'sample_link', width:175, cellRenderer: this.parseSampleLinks.bind(this)},

    //Assessment Links
    { headerName: this.lang.tra('assessment_links_alt_version'), field: 'operational_link', width:175, cellRenderer: this.parseOperationalLinks.bind(this)},

    //Individual links are hidden but are included in the CSV export instead of the combined list of hyperlinks
    { headerName: this.lang.tra('alt_version_access_link_pdf_regular_sample'), field:'sample_link_pdf_regular', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_pdf_large_sample'), field:'sample_link_pdf_large', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_mp3_sample'), field:'sample_link_mp3', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_asl_sample'), field:'sample_link_asl', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_ebraille_sample'), field:'sample_link_ebraille', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_pdf_regular_assessment'), field:'operational_link_pdf_regular', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_pdf_large_assessment'), field:'operational_link_pdf_large', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_mp3_assessment'), field:'operational_link_mp3', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_asl_assessment'), field:'operational_link_asl', hide: true},
    { headerName: this.lang.tra('alt_version_access_link_ebraille_assessment'), field:'operational_link_ebraille', hide: true},

    { headerName: this.lang.tra('sch_mident_alt_version'), field:'schl_mident', width:175, },
    { headerName: this.lang.tra('sch_name_alt_version'), field:'schl_name', width:175, },
    { headerName: this.lang.tra('sch_admin_name_alt_version'), field:'schl_admin_name', width:175, },
    { headerName: this.lang.tra('sch_admin_email_col_alt_version'), field:'schl_admin_email', width:175, },
    { headerName: this.lang.tra('teacher_alt_version'), field:'teacher_name', width:175, },
    { headerName: this.lang.tra('sch_contact_braille_alt_version'), field:'schl_contact_braille', width:175, },
    { headerName: this.lang.tra('sch_email_braille_alt_version'), field:'schl_email_braille', width:175, },
    { headerName: this.lang.tra('sch_phone_alt_version'), field:'schl_phone_braille', width:175, },
    { headerName: this.lang.tra('sch_address_braille_alt_version'), field:'schl_address_braille', width:175, },
    { headerName: this.lang.tra('ship_required_alt_version'), field:'shipping_required', width:175, },
    { headerName: this.lang.tra('ship_company_sample_alt_version'), field:'shipping_company_sample', width:175, },

    //Tracking num (sample)
    { headerName: this.lang.tra('ship_track_sample_alt_version'), field:'tracking_number_sample', width:175, cellRenderer: this.parseSampleTracking.bind(this)},

    //Tracking URL Sample - Hidden column for export
    { headerName: this.lang.tra('ship_track_url_sample_alt_version'), field:'tracking_url_sample', width:175, hide: true},

    { headerName: this.lang.tra('ship_company_op_alt_version'), field:'shipping_company_operational', width:175, },

    //Tracking num (assessment)
    { headerName: this.lang.tra('ship_track_op_alt_version'), field:'tracking_number_operational', width:175, cellRenderer: this.parseOpTracking.bind(this)},

    //Tracking URL assessment - Hidden column for export
    { headerName: this.lang.tra('ship_track_url_op_alt_version'), field:'tracking_url_operational', width:175, hide: true},
  ];

  requestGridOptions:any = {
    columnDefs: this.columnDefs,
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
    enableCellTextSelection: true,
    onRowSelected: (event: any) => {
      const request = event.api?.getSelectedRows()[0];
      const id = request?.id || null;
      this.requestIdToPatch = id;
      this.isSelected = event.api.getSelectedRows().length > 0 ? true : false;
      if(this.isSelected) {
        this.isPending = request.status == ALT_VERSION_REQUEST_STATUS.Pending ? true : false;
        this.isShipping = request.status != ALT_VERSION_REQUEST_STATUS.Pending && request.status != ALT_VERSION_REQUEST_STATUS.Canceled && request.status != "Rejected" && request.shipping_required == "Yes" ? true: false;
      }
    }
  };

  // Columns affected by the filter
  schoolColumns = SchoolColumns;
  shippingColumns = ShippingColumns;
  requestColumns = RequestColumns;

  // Modal
  pageModal: PageModalController;
  Modal = Modal;

  // Approve and reject handling
  isSelected: boolean = false;
  requestIdToPatch: number;
  isPending: boolean = false;
  isShipping: boolean = false;

  // Toggles
  isRequestInfo: boolean = true;
  isSchoolInfo: boolean = false;
  isShippingInfo: boolean = true;

  // Shipping Info Input
  shippingCompanySample: string;
  trackingNumberSample: number;
  trackingUrlSample: number;
  shippingCompanyOp: string;
  trackingNumberOp: number;
  trackingUrlOp: string;
  trackingNumberReturn: number;
  trackingUrlReturn: string;
  deliveredDateSample;
  deliveredDateOperational;

  onGridReady(params) {
    this.requestGridColumnApi = params.columnApi;
    this.requestGridApi = params.api;
    this.toggleInfo();
  }

  async ngOnInit(): Promise<void> {
    this.routeSub = this.route.params.subscribe(routeParams => {
      // this.setupId = routeParams['setupId'];
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT('breadcrumb_title_vendor_alt_version', this.router.url),
      ]
      this.initRouteView();
    });
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadRequests();
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }
  
  initRouteView() {

  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  async loadRequests() {
    this.requestRows = await this.auth.apiFind(this.routes.ALT_VERSION_SHIPPING_REQUESTS, {query: {action: "shipping"}});
    this.isSelected = false;
  }

  toggleInfo() {
    if(!this.isRequestInfo && !this.isShippingInfo && !this.isSchoolInfo) {
      this.requestGridColumnApi.setColumnsVisible(this.requestColumns, true);
      this.requestGridColumnApi.setColumnsVisible(this.shippingColumns, true);
      this.requestGridColumnApi.setColumnsVisible(this.schoolColumns, true);
    } else {
      this.requestGridColumnApi.setColumnsVisible(this.requestColumns, this.isRequestInfo);
      this.requestGridColumnApi.setColumnsVisible(this.shippingColumns, this.isShippingInfo);
      this.requestGridColumnApi.setColumnsVisible(this.schoolColumns, this.isSchoolInfo);
    }
  }

  shippingModalStart() {
    const config: any = {}
    const data = this.requestGridApi.getSelectedRows()[0];
    this.shippingCompanySample = data.shipping_company_sample;
    this.shippingCompanyOp = data.shipping_company_operational;
    this.trackingNumberSample = data.tracking_number_sample;
    this.trackingNumberOp = data.tracking_number_operational;
    this.trackingNumberReturn = data.tracking_number_return;
    this.trackingUrlSample = data.tracking_url_sample;
    this.trackingUrlOp = data.tracking_url_operational;
    this.trackingUrlReturn = data.tracking_url_return;
    this.deliveredDateSample = data.shipping_delivered_date_sample;
    this.deliveredDateOperational = data.shipping_delivered_date_operational;

    this.pageModal.newModal({type: Modal.SHIPPING, config, finish: this.shippingModalFinish});
  }

  isOperationalSent(){
    const requests = this.requestGridApi.getSelectedRows()
    if (requests[0].status == ALT_VERSION_REQUEST_STATUS.Operational_Link_Send) return true
    return false
  }

  shippingModalFinish = (config: { payload: { id: any, data: number }}) =>  {
    let data = {
      shipping_company_sample: this.shippingCompanySample,
      tracking_number_sample: this.trackingNumberSample,
      tracking_url_sample: this.trackingUrlSample,
      shipping_company_operational: this.shippingCompanyOp,
      tracking_number_operational: this.trackingNumberOp,
      tracking_url_operational: this.trackingUrlOp,
      tracking_number_return: this.trackingNumberReturn,
      tracking_url_return: this.trackingUrlReturn,
      shipping_delivered_date_sample: this.deliveredDateSample, 
      shipping_delivered_date_operational: this.deliveredDateOperational
    }

    this.auth.apiPatch(this.routes.ALT_VERSION_SHIPPING_REQUESTS, this.requestIdToPatch, data, {query: {action: ACTIONS.SHIPPING}}).then((res) => {
      this.loadRequests();
      this.pageModal.closeModal();
    }).catch((err) => {
      alert("There was an error saving your shipping info");
    });
  }

  getColumnKeysForExport(columnApi: ColumnApi){
    //Columns which are lists of hyperlinks will be broken up into individual columns with links on export (individual columns are hidden on web page)
    const colHeadersToRemoveFromExport = [this.lang.tra('sample_links_alt_version'), this.lang.tra('assessment_links_alt_version')]
    const colFieldsToAddToExport = [
      'sample_link_pdf_regular', 'sample_link_pdf_large', 'sample_link_mp3', 'sample_link_asl', 'sample_link_ebraille',
      'operational_link_pdf_regular', 'operational_link_pdf_large', 'operational_link_mp3', 'operational_link_asl', 'operational_link_ebraille', 'tracking_url_sample', 'tracking_url_operational'
    ]
    const columnKeysForExport = columnApi
    .getAllColumns()
    .filter(col => {
      const colDef = col.getColDef()
      if(colDef.checkboxSelection) return false
      else if (colFieldsToAddToExport.includes(colDef.field)) return true
      else if (colHeadersToRemoveFromExport.includes(colDef.headerName)) return false
      else if (colDef.hide) return false
      else return true
    })
    return columnKeysForExport
  }

  processCells(cell) {
    let cellVal = cell.value;
    if ( cell.column.colDef.cellRenderer ) {
        const field = cell.column.colDef.field
        if (!['tracking_number_sample', 'tracking_number_operational'].includes(field)){
            cellVal = cell.column.colDef.cellRenderer({value: cell.value, data: cell.node.data, plainTextMode: true});
        }
    }
    return cellVal;
  }

  exportRequests() {
    this.requestGridApi.exportDataAsCsv({
      fileName: `alt_version_exports ${new Date()}`,
      processCellCallback: this.processCells,
      columnKeys: this.getColumnKeysForExport(this.requestGridColumnApi)
    });
  }

  parseDate(params) {
    if(!params.value) {
      return null;
    }
    const current = new Date(Date.parse(params.value));
    const parsedDate = current.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric'});
    const parsedTime = current.toLocaleTimeString('default');
    return `${parsedDate} ${parsedTime}`;
  }

  parseSampleLinks(params){
    let linkList ="";
    if(params.data.sample_link_pdf_regular) linkList += `<a href=${params.data.sample_link_pdf_regular} target=_blank>${this.lang.tra('alt_version_link_title_pdf_regular')}</a>, `
    if(params.data.sample_link_pdf_large) linkList += `<a href=${params.data.sample_link_pdf_large} target=_blank>${this.lang.tra('alt_version_link_title_pdf_large')}</a>, `
    if(params.data.sample_link_mp3) linkList += `<a href=${params.data.sample_link_mp3} target=_blank>${this.lang.tra('alt_version_link_title_mp3')}</a>, `
    if(params.data.sample_link_asl) linkList += `<a href=${params.data.sample_link_asl} target=_blank>${this.lang.tra('alt_version_link_title_asl')}</a>, `
    if(params.data.sample_link_ebraille) linkList += `<a href=${params.data.sample_link_ebraille} target=_blank>${this.lang.tra('alt_version_link_title_ebraille')}</a>, `
    return linkList.slice(0,-2);
  }

  parseOperationalLinks(params){
    let linkList ="";
    if(params.data.operational_link_pdf_regular) linkList += `<a href=${params.data.operational_link_pdf_regular} target=_blank>${this.lang.tra('alt_version_link_title_pdf_regular')}</a>, `
    if(params.data.operational_link_pdf_large) linkList += `<a href=${params.data.operational_link_pdf_large} target=_blank>${this.lang.tra('alt_version_link_title_pdf_large')}</a>, `
    if(params.data.operational_link_mp3) linkList += `<a href=${params.data.operational_link_mp3} target=_blank>${this.lang.tra('alt_version_link_title_mp3')}</a>, `
    if(params.data.operational_link_asl) linkList += `<a href=${params.data.operational_link_asl} target=_blank>${this.lang.tra('alt_version_link_title_asl')}</a>, `
    if(params.data.operational_link_ebraille) linkList += `<a href=${params.data.operational_link_ebraille} target=_blank>${this.lang.tra('alt_version_link_title_ebraille')}</a>, `
    return linkList.slice(0,-2);
  }

  parseSampleTracking(params){
    if(params.data.tracking_number_sample) return `<a href=${params.data.tracking_url_sample} target=_blank>${params.data.tracking_number_sample}</a>`
    else return null
  }

  parseOpTracking(params){
    if(params.data.tracking_number_operational) return `<a href=${params.data.tracking_url_operational} target=_blank>${params.data.tracking_number_operational}</a>`
    else return null
  }

  getRequestSlug(params): string {
    let slug = "";
    switch(params.data.status) {
      case ALT_VERSION_REQUEST_STATUS.Pending:
        slug = REQUEST_STATUS_SLUGS.Pending
        break;
      case ALT_VERSION_REQUEST_STATUS.Approved: 
        slug = REQUEST_STATUS_SLUGS.Approved
        break;
      case ALT_VERSION_REQUEST_STATUS.Canceled: 
        slug = REQUEST_STATUS_SLUGS.Canceled
        break;
      case ALT_VERSION_REQUEST_STATUS.Rejected: 
        slug = REQUEST_STATUS_SLUGS.Rejected
        break;
      case ALT_VERSION_REQUEST_STATUS.Shipment_Send: 
        slug = REQUEST_STATUS_SLUGS.Shipment_Send
        break;
      case ALT_VERSION_REQUEST_STATUS.Operational_Link_Send: 
        slug = REQUEST_STATUS_SLUGS.Operational_Link_Send
        break;
    }

    return this.lang.tra(slug);
  }

}
