<div class="page-body">
    <div>
        <header
            [breadcrumbPath]="breadcrumb"
        ></header>
        <div class="page-content is-fullpage">
            <div  class="dashboard-cards-container" [class.is-full-width]=true>
                <div class="card dashboard-card">
                    <div class="table-holder">
                        <div style="display: flex; justify-content: space-between;">
                            <h2 class="section-header">
                                <tra slug="Shipping Vendor Dashboard"></tra>
                            </h2>
                        </div>
                        <div style="display: flex; flex-direction:column; gap: 20px; padding-bottom: 1em;">
                            <div style="display: flex;justify-content:space-between; align-items: center;">
                                <div class="request-buttons">
                                    <button class="button" [disabled]="!isSelected || !isShipping" (click)="shippingModalStart()">
                                        <tra slug="shipping_alt_version"></tra>
                                    </button>
                                </div>
                                <div class="request-options">
                                    <div>
                                        <button *ngIf="requestRows" class="button has-icon" (click)="exportRequests()">
                                            <span><tra slug="sa_classrooms_export"></tra></span>
                                            <span class="icon"><i class="fas fa-table"></i></span>
                                        </button>
                                    </div>
                                    <drop-down [title]="'filters_alt_version'" [dropdownId]="'shipping-vendor-dropdown-id'">
                                        <li class="dropdown-list">
                                            <input type="checkbox" [(ngModel)]="isRequestInfo" id="reqCheck" (change)="toggleInfo()">
                                            <label for="reqCheck" class="checkbox-label"><tra slug="request_info_alt_version"></tra></label>
                                        </li>
                                        <li class="dropdown-list">
                                            <input type="checkbox" [(ngModel)]="isSchoolInfo" id="schoolCheck" (change)="toggleInfo()">
                                            <label for="schoolCheck" class="checkbox-label"><tra slug="school_info_alt_version"></tra></label>
                                        </li>
                                        <li class="dropdown-list">
                                            <input type="checkbox" [(ngModel)]="isShippingInfo" id="shipCheck" (change)="toggleInfo()">
                                            <label for="shipCheck" class="checkbox-label"><tra slug="shipping_alt_version"></tra></label>
                                        </li>
                                    </drop-down>
                                </div>
                            </div>
                            <div class="grid-container">
                                <ag-grid-angular
                                    class="ag-theme-alpine ag-grid-fullpage"
                                    style="border: none;"
                                    [rowData]="requestRows"
                                    [gridOptions]="requestGridOptions"
                                    (gridReady)="onGridReady($event)"
                                ></ag-grid-angular>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
            <div *ngSwitchCase="Modal.SHIPPING" style="width: 35em; ">
              <h3><tra slug="shipping_alt_version"></tra></h3>
              <table>
                  <tr>
                      <td>
                          <tra slug="ship_company_sample_alt_version"></tra>
                      </td>
                      <td>
                          <input class="input" placeholder="{{lang.tra('alt_version_shipping_company_placeholder')}}" type="text" [(ngModel)]="shippingCompanySample">
                      </td>
                  </tr>
                  <tr>
                      <td>
                          <tra slug="ship_track_sample_alt_version"></tra>
                      </td>
                      <td>
                          <input class="input" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" type="text" [(ngModel)]="trackingNumberSample">
                          <input class="input" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" type="text" [(ngModel)]="trackingUrlSample">
                      </td>
                  </tr>
                  <tr>
                    <td>
                      <tra slug="ship_deliver_date_sample_alt_version"></tra>
                    </td>
                    <td>
                      <input class="input" type="date" [(ngModel)]="deliveredDateSample">
                    </td>
                  </tr>
                  <tr>
                      <td>
                          <tra slug="ship_company_op_alt_version"></tra>
                      </td>
                      <td>
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_shipping_company_placeholder')}}" [disabled]="!isOperationalSent()" [(ngModel)]="shippingCompanyOp">
                      </td>
                  </tr>
                  <tr>
                      <td>
                          <tra slug="ship_track_op_alt_version"></tra>
                      </td>
                      <td>
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" [disabled]="!isOperationalSent()" [(ngModel)]="trackingNumberOp">
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" [disabled]="!isOperationalSent()" [(ngModel)]="trackingUrlOp">
                      </td>
                  </tr>
                  <tr>
                    <td>
                      <tra slug="ship_deliver_date_op_alt_version"></tra>
                    </td>
                    <td>
                      <input class="input" type="date" [(ngModel)]="deliveredDateOperational" [disabled]="!isOperationalSent()">
                    </td>
                  </tr>
                  <tr>
                      <td>
                          <tra slug="ship_track_return_alt_version"></tra>
                      </td>
                      <td>
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" [disabled]="!isOperationalSent()" [(ngModel)]="trackingNumberReturn">
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" [disabled]="!isOperationalSent()" [(ngModel)]="trackingUrlReturn">
                      </td>
                  </tr>
              </table>
            </div>
        </div>
        <modal-footer [pageModal]="pageModal" [isConfirmAlert]="true" ></modal-footer>
    </div>
</div>