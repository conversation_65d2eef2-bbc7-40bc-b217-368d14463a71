@import '../../ui-testctrl/view-tc-dashboard/view-tc-dashboard.component.scss';
@import '../../ui-partial/drop-down/drop-down.component.scss';
@import '../../../styles/partials/_modal.scss';
@import '../../../styles/partials/_media.scss';

.custom-modal { @extend %custom-modal; }

.section-header {
    font-weight: 700 !important;
}

.dashboard-cards-container{
    padding-right:40px;
    padding-left:40px;
}

input[type=checkbox] {
    cursor: pointer;
}

.checkbox-label {
    cursor: pointer;
    width: 100%;
    height: 100%;
}

.ag-grid-fullpage {
    height: 50vh !important;
}

.request-buttons {
    display: flex;
    gap: 1em;
}

.button{
    height: 37px;
    background: rgb(243, 245, 245);
    padding: 0.5em;
    padding-left: 1em;
    padding-right: 1em;
    border-radius: 0.5em;
    border: none;
    font-weight: 700;
    font-size: medium;
    font-size:s;
    &:hover:enabled{
        cursor: pointer;    
        background: rgb(234, 235, 235);
    }
    &:disabled{
        background: rgb(243, 245, 245);
    }
}

.approve{
    color: white;
    background: #66BB6A;
    &:hover:enabled{
        background: #4CAF50;
        color: white;
    }
    &:disabled {
        background: #66BB6A;
        color: white;
    }
}

.reject{
    color: #EF5350;
    background: white;
    border:solid 1px #EF5350;
    &:hover:enabled{
        color: white;
        background: #EF5350;
    }
    &:disabled {
        border:solid 1px #EF5350;
        background: white;
        color: #EF5350;
    }
}
.internal-notes {
    width: 100%;
    max-width: 100%;
    height: 130px;
    min-height: 130px;
    border: none;
    border-radius: 0.5em;
    background-color: #f8f8f8;
    padding: 1em;
    overflow: auto;
    outline: none;
    resize: vertical;
    &:disabled {
        cursor:not-allowed;
        opacity: 0.5;
    }
}

.email {
    min-height: 400px;
    height: 400px;
    font-size:medium;
    // border: 1px solid rgb(230, 230, 230);
    background: white;
    padding: 0;
    color: rgb(8, 8, 8);
}

.subject {
    width: 100%;
    min-height: 30px;
    border: none;
    border-bottom: 1px solid rgb(230, 230, 230);
    // border-radius: 0.5em;
    padding-top: 1em;
    padding-bottom: 1em;
    outline: none;
    font-size:medium;
    color: rgb(5, 5, 5);
}

.ag-theme-alpine {
    .ag-root-wrapper {
        border: none;
    }

    .ag-header {
        border-color: var(--ag-row-border-color, var(--ag-secondary-border-color, #dde2eb));
        background: white;
    }
}

.note-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.note {
    padding: 10px;
    border-radius: 0.5em;
    display: flex;
    flex-direction: column;
    gap: 7px;
    box-shadow: 0 5px 10px rgba(154,160,185,.05), 0 8px 14px rgba(166,173,201,.2);

    .username {
        font-weight: bold;
        font-size: medium;
    }
    .date {
        font-weight:300;
        font-size: small;
    }
    .text {
        font-size: medium;
    }
}

.request-options {
    display: flex; 
    gap: 10px; 
    align-items: center;
}


@include viewport-mdp{
    .request-buttons {
        flex-direction: column;
    }

    .dashboard-cards-container{
        padding-right: 0px;
        padding-left: 0px;
        .card.dashboard-card {
            align-items:stretch !important;
        }
    }

    .internal-notes {
        width: 100%;
        max-width: 100%;
    }

    .request-options {
        flex-direction: column;
    }
}

@include viewport-md{
    .request-buttons {
        flex-direction: column;
    }

    .dashboard-cards-container{
        padding-right: 0px;
        padding-left: 0px;
        .card.dashboard-card {
            align-items:stretch !important;
        }
    }

    .internal-notes {
        width: 100%;
        max-width: 100%;
    }

    .request-options {
        flex-direction: column;
    }
}