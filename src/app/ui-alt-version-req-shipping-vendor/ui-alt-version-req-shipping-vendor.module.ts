import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UiAltVersionReqShippingVendorRoutingModule } from './ui-alt-version-req-shipping-vendor-routing.module';
import { ViewAltVersionReqShippingVendorDashboardComponent } from './view-alt-version-req-shipping-vendor-dashboard/view-alt-version-req-shipping-vendor-dashboard.component';
import { AgGridModule } from 'ag-grid-angular';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [ViewAltVersionReqShippingVendorDashboardComponent],
  imports: [
    CommonModule,
    UiAltVersionReqShippingVendorRoutingModule,
    AgGridModule,
    UiPartialModule,
    FormsModule
  ]
})
export class UiAltVersionReqShippingVendorModule { }
