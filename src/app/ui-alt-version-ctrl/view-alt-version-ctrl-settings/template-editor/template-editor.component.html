<div *ngIf="isLoaded && selectedRequest" class="template-info">
  <h3>
    <span *ngIf="isCustom" class="has-text-info"><b><tra slug="alt_version_template_subheader_custom"></tra></b></span>
    <span *ngIf="!isCustom" class="has-text-primary"><b><tra slug="alt_version_template_subheader_default"></tra></b></span>
  </h3>

  <div class="notification is-warning is-light" *ngIf="!isCustom && !template.is_finalized">
    <tra slug="alt_version_template_msg_default_not_final"></tra>
  </div>

  <div class="notification is-danger is-light" *ngIf="accessLinkErrMsg">
    <b><tra slug="alt_version_template_msg_files_not_ready"></tra></b>
    <br/>
    {{accessLinkErrMsg}}
  </div>

</div>


<div *ngIf="isLoaded" class="columns">

  <!-- Composing template -->
  <div class="column">

    <h2><tra slug="alt_version_template_header"></tra></h2>

    <div class="field">
      <label class="label"><tra slug="alt_version_temp_subject"></tra></label>
      <div class="control">
        <input [disabled]="!isEditing" class="input" type="text" placeholder="Subject" [(ngModel)]="template.subject">
      </div>
    </div>
  
    <div class="field">
      <label class="label"><tra slug="alt_version_temp_message"></tra></label>
      <div class="control">
        <textarea [disabled]="!isEditing" [cdkTextareaAutosize]="true" [cdkAutosizeMinRows]="10" type="textarea" class="input textarea" placeholder="Message Body" [(ngModel)]="template.body"></textarea>
      </div>
      <div>
        <a class="help" (click)="showFormattingInstructions()"><tra slug="alt_version_temp_format_instr_link"></tra></a>
      </div>
      <div>
        <a class="help" (click)="showPlaceholderInstructions()"><tra slug="alt_version_temp_placeholder_instr_link"></tra></a>
      </div>
    </div>

    <div class="columns">
      <div class="column is-narrow">
        <input type="checkbox" [(ngModel)]="showPreview" id="showPreviewCheckbox">
        <label for="showPreviewCheckbox" class="checkbox-label"> &nbsp;<tra slug="alt_version_temp_check_preview"></tra></label>
      </div>
      <div class="column">
        <div class="buttons is-pulled-right">
          
          <button *ngIf="selectedRequest && !isEditing && isCustom" (click)="confirmRevertToDefault()" class="button is-small is-light"><tra slug="alt_version_temp_btn_revert"></tra></button>
          
          <button *ngIf="!isEditing" (click)="onEdit()" [disabled]="isEditBtnDisabled()" class="button is-small"><tra slug="edit_btn"></tra></button>
    
          <button *ngIf="isEditing" (click)="onCancel()" class="button is-small is-danger is-light"><tra slug="cancel_btn"></tra></button>
    
          <button *ngIf="isEditing" (click)="onSave()" class="button is-small is-success"><tra slug="save_btn"></tra></button>
        </div>
      </div>
    </div>

  </div>

  <!-- Previewing template-->
  <div *ngIf="showPreview" class="column">

    <h2><tra slug="alt_version_template_preview_header"></tra></h2>

    <div class="field">
      <label class="label"><tra slug="alt_version_temp_subject"></tra></label>
      <div class="preview-field">
        <markdown
          class="markdown" 
          [data]="renderPlaceholderDemo(template.subject)" 
          ngPreserveWhitespaces
        ></markdown>
      </div>
    </div>

    <div class="field">
      <label class="label"><tra slug="alt_version_temp_message"></tra></label>
      <div class="preview-field">
        <markdown
          class="markdown" 
          [data]="renderPlaceholderDemo(template.body)" 
          ngPreserveWhitespaces
        ></markdown>
      </div>
    </div>

    <div class="notification is-danger is-light">
      <tra slug="alt_version_template_placeholder_note"></tra>
    </div>

  </div>

</div>


<div *ngIf="isLoaded && !selectedRequest" class="is-pulled-right">
  <button class="button is-primary is-hovered" [class.is-inverted]="template.is_finalized" (click)="toggleIsFinalized()" [disabled]="isEditing">
    <tra *ngIf="!template.is_finalized" slug="Mark template as Finalized"></tra>
    <tra *ngIf="template.is_finalized" slug="Unmark template as Finalized"></tra>
  </button>
</div>


<!-- Placeholders table modal -->
<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents" style="width: 70vw;">
    
    <table>
      <tr>
        <td><b><tra slug="alt_version_temp_placeholder_property"></tra></b></td>
        <td><b><tra slug="alt_version_temp_placeholder_pl"></tra></b></td>
        <td><b><tra slug="alt_version_temp_placeholder_example"></tra></b></td>
      </tr>

      <tr *ngFor="let pl of TEMPLATE_PLACEHOLDERS">
        <td><tra [slug]="pl.caption"></tra></td>
        <td><code>{{pl.placeholder}}</code></td>
        <td><span [innerHTML]="pl.default_demo"></span></td>
      </tr>
    </table>
    
    <modal-footer [pageModal]="pageModal" [confirmButton]="false" [closeMessage]="'btn_close'"></modal-footer>
  </div>
</div>