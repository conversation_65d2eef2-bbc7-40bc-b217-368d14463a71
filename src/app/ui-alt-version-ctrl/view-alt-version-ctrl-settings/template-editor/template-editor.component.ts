import { Component, OnInit, Input, Output, EventEmitter, OnChanges } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {TEMPLATE_PLACEHOLDERS, TargetStatus} from './../types'
import { LangService } from 'src/app/core/lang.service';

interface Template {
  subject:string,
  body:string;
  is_finalized?: number
}

@Component({
  selector: 'template-editor',
  templateUrl: './template-editor.component.html',
  styleUrls: ['./template-editor.component.scss']
})
export class TemplateEditorComponent implements OnInit {

  constructor(
    private loginGuard: LoginGuardService,
    private routes: RoutesService,
    private auth: AuthService,
    private pageModalService: PageModalService,
    private lang: LangService
  ) { }

  @Input() reqAssessment;
  @Input() reqFormat;
  @Input() reqLang;
  @Input() reqStatus;
  // Provided in the preview message tab for a particular request, not the settings page
  @Input() selectedRequest;

  @Output() isEditingEmitter = new EventEmitter();

  TEMPLATE_PLACEHOLDERS = TEMPLATE_PLACEHOLDERS;

  isLoaded:boolean;
  pageModal: PageModalController;

  showPreview:boolean = false;
  isEditing:boolean = false;

  //Template editing
  savedTemplate: Template;
  template: Template;

  isCustom:boolean;
  accessLinkErrMsg:string;

  ngOnChanges(){
    this.loadData();
  }

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadData();
  }

  loadData(){
    this.accessLinkErrMsg = null;
    this.loadTemplate();
    // Only need specific access links if previewing for a particular request. Cancel notification does not have links.
    if (this.selectedRequest && this.reqStatus !== TargetStatus.CANCEL ) this.loadAccessLinks()
  }

  /**
   * Get the access links (as an already rendered string of html) that the student would get for this notification
   * Store it in the request info so that it can replace a placeholder
   */
  loadAccessLinks(){
    this.auth.apiGet(this.routes.ALT_VERSION_CTRL_ACCESS_FILES, this.selectedRequest.id, {
      query: {
        req_status: this.reqStatus,
      }
    })
    .then(res => {
      this.selectedRequest.renderedAccessLinks = res.renderedAccessLinks;
    })
    .catch(err => {
      this.accessLinkErrMsg = err.message;
    })
    
  }


  /** Get the template of this particular request  */
  async getIndividualTemplate(){
    const res = await this.auth.apiGet(this.routes.ALT_VERSION_CTRL_REQUEST_NOTIFICATION_TEMPLATES, this.selectedRequest.req_info_id, {
      query: {
        assessment: this.reqAssessment,
        format: this.reqFormat,
        req_status: this.reqStatus,
        lang: this.reqLang
      }
    })
    this.isCustom = res.isCustom
    return res.template
  }

  /** Get the default template of this category */
  getDefaultTemplate(){
    return this.auth.apiFind(this.routes.ALT_VERSION_CTRL_NOTIFICATION_TEMPLATES, {
      query: {
        assessment: this.reqAssessment,
        format: this.reqFormat,
        req_status: this.reqStatus,
        lang: this.reqLang
      }
    })
  }

  async loadTemplate(){
    this.isLoaded = false;
    this.savedTemplate = !!this.selectedRequest ? await this.getIndividualTemplate() : await this.getDefaultTemplate();
    this.template = {...this.savedTemplate}
    this.isLoaded = true;
  }

  confirmRevertToDefault(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('alt_version_temp_btn_revert_confirm'),
      confirm: () => {
        this.revertToDefault();
      }
    })
  }

  async revertToDefault(){
    // Change the value in the individual template to null
    await this.saveIndividualTemplate(true);
    // Pull the default template
    this.savedTemplate = await this.getDefaultTemplate();
    this.template = {...this.savedTemplate}
    this.isCustom = false;
  }

  onEdit(){
    this.isEditing = true;
    this.isEditingEmitter.emit(true);
  }

  onCancel(){
    this.template = {...this.savedTemplate}
    this.isEditing = false;
    this.isEditingEmitter.emit(false);
  }

  /**
   * Save the request-specific template
   * @param isForceClear - Pass `true` to clear the individual template and revert to default, otherwise will save currently displayed template
   */
  async saveIndividualTemplate(isForceClear?:boolean){
    const template = isForceClear ? null : JSON.stringify(this.template)
    return this.auth.apiPatch(this.routes.ALT_VERSION_CTRL_REQUEST_NOTIFICATION_TEMPLATES, this.selectedRequest.req_info_id,
      { template },
      { 
        query: {
          req_status: this.reqStatus,
        }
      }
    );
  }

  async saveDefaultTemplate(){
    return this.auth.apiPatch(this.routes.ALT_VERSION_CTRL_NOTIFICATION_TEMPLATES, -1,
      { template: JSON.stringify(this.template)},
      { 
        query: {
          assessment: this.reqAssessment,
          format: this.reqFormat,
          req_status: this.reqStatus,
          lang: this.reqLang
        }
      }
    );
  }

  async onSave(){
    if (!this.template.subject.trim() || !this.template.body.trim()) {
      return this.loginGuard.quickPopup(this.lang.tra('alt_version_temp_empty_error'))
    }

     const doSave = async () => {
      if (!!this.selectedRequest) {
        await this.saveIndividualTemplate()
        if (!this.isCustom) this.isCustom = true;
      }
      else {
        await this.saveDefaultTemplate();
      }
      this.savedTemplate = this.template;
      this.isEditing = false;
      this.isEditingEmitter.emit(false);
    }

    if (!!this.selectedRequest && !this.isCustom) {
      return this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('alt_version_template_customize_confirm'),
        confirm: () => doSave()
      })
    }
    
    return doSave()
  }

  /** Toggle whether the template is finalized or not */
  toggleIsFinalized(){
    
    const setFinalized = (is_finalized) => {
      const query = {
        assessment: this.reqAssessment,
        format: this.reqFormat,
        req_status: this.reqStatus,
        lang: this.reqLang
      }
      this.auth.apiPatch(this.routes.ALT_VERSION_CTRL_NOTIFICATION_TEMPLATES, -1, {is_finalized}, {query})
      .then (() => {
        this.template.is_finalized = is_finalized;
      });
    }

    const isNewFinalized = this.template.is_finalized ? 0 : 1
    const confirmCaption = isNewFinalized ? "alt_version_template_mark_final_confirm" : "alt_version_template_unmark_final_confirm"
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra(confirmCaption),
      confirm: () => {
        setFinalized(isNewFinalized);
      }
    })

  }

  /** Returns if the editing button should be disabled (editing forbidden) */
  isEditBtnDisabled():boolean{
    const isPreviewModeDefaultNotFinalized = this.selectedRequest && !this.isCustom && !this.template.is_finalized
    const isDefaultModeFinalized = !this.selectedRequest && !!this.template.is_finalized
    return (isPreviewModeDefaultNotFinalized || isDefaultModeFinalized)
  }


  showFormattingInstructions() {
    this.loginGuard.confirmationReqActivate({
      caption: 'caption_md_formatting_instructions',
      btnProceedConfig: {hide: true},
      btnCancelConfig: {caption: 'btn_close'}
    })
  }

  showPlaceholderInstructions(){
    this.pageModal.newModal({
      type: 'placeholderInstructions',
      config: {},
      finish: () => {
      }
    })
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }


  /** Replace custom placeholders in template with demo data  */
  renderPlaceholderDemo(targetString:string) {
    // Loop through each placeholder object
    for (const pl of TEMPLATE_PLACEHOLDERS) {
      const { placeholder, default_demo, request_field } = pl;
      // Create a regular expression to find the placeholder in the input string
      const regex = new RegExp(placeholder.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');

      // Replace all occurrences of the placeholder with the student's info if applicable, or otherwise with the default demo value
      const replacementValue = (this.selectedRequest && request_field) ? this.selectedRequest[request_field] : default_demo
      const replacement = pl.isLink ? replacementValue : pl.isDiv? ('<div style="color: red">'+replacementValue+'</div>') : ('<span style="color: red">'+replacementValue+'</span>')
      targetString = targetString.replace(regex, replacement);
    }
    return targetString;
  }

}
